name: Toolchain

on:
  pull_request:
    branches: [ stripe, develop, main ]
  push:
    branches: [ stripe, develop ]

jobs:
  toolchain:
    runs-on: ubuntu-latest
    name: Enforce pnpm + frozen lockfile

    steps:
      # Use PR HEAD instead of the synthetic merge when event is pull_request
      - name: Checkout (PR HEAD)
        if: ${{ github.event_name == 'pull_request' }}
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          fetch-depth: 0

      # Keep a normal checkout for push events
      - name: Checkout (push)
        if: ${{ github.event_name == 'push' }}
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Node 20
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          check-latest: true

      - name: Enable Corepack
        run: corepack enable

      - name: Activate pnpm
        run: corepack prepare pnpm@latest --activate

      - name: Verify pnpm
        run: pnpm -v

      - name: Install (frozen)
        run: pnpm install --frozen-lockfile

      - name: Verify lockfile integrity
        run: |
          echo "✅ pnpm install with frozen lockfile succeeded"
          echo "✅ Lockfile integrity verified"
          
      - name: Check for conflicting lockfiles (tracked files only, with debug)
        run: |
          set -euo pipefail
          echo "GITHUB_SHA=$GITHUB_SHA"
          echo "HEAD commit:"
          git show -s --oneline HEAD
          echo "Scanning tracked files for conflicting lockfiles…"
          MATCHES="$(git ls-files | egrep '(^|/)(package-lock\.json|yarn\.lock|bun\.lockb)$' || true)"
          if [ -n "$MATCHES" ]; then
            echo "Found conflicting lockfiles:"
            echo "$MATCHES"
            exit 1
          else
            echo "✅ No conflicting lockfiles found in tracked files."
          fi

      - name: Validate workspace configuration
        run: |
          if [ ! -f "pnpm-workspace.yaml" ]; then
            echo "❌ pnpm-workspace.yaml is missing"
            exit 1
          else
            echo "✅ pnpm-workspace.yaml exists"
          fi
          
      - name: Validate .npmrc configuration
        run: |
          if [ ! -f ".npmrc" ]; then
            echo "❌ .npmrc is missing"
            exit 1
          else
            echo "✅ .npmrc exists"
            cat .npmrc
          fi
