#!/usr/bin/env node

/**
 * Regional Payment Methods Implementation Validator
 * 
 * Validates the complete implementation of the regional payment methods
 * system including database schema, services, API routes, and UI components.
 */

const fs = require('fs');
const path = require('path');

// Implementation components to validate
const IMPLEMENTATION_COMPONENTS = {
  database: {
    name: 'Database Schema',
    files: [
      'database/migrations/20241201_payment_methods_schema.sql',
      'frontend/src/lib/types/database.types.ts'
    ],
    tables: [
      'payment_method_types',
      'regional_payment_methods',
      'customer_payment_preferences',
      'payment_method_validation_rules',
      'payment_method_processing_logs'
    ]
  },
  services: {
    name: 'Service Layer',
    files: [
      'frontend/src/lib/services/regional-payment-method-service.ts',
      'frontend/src/lib/services/stripe-payment-method-service.ts',
      'frontend/src/lib/services/payment-method-validation-service.ts'
    ],
    methods: [
      'getAvailablePaymentMethods',
      'validatePaymentMethod',
      'createPaymentMethod',
      'processPayment',
      'saveCustomerPreferences'
    ]
  },
  api: {
    name: 'API Routes',
    files: [
      'frontend/src/app/api/payment-methods/route.ts',
      'frontend/src/app/api/payment-methods/validate/route.ts',
      'frontend/src/app/api/payment-methods/recommend/route.ts',
      'frontend/src/app/api/payment-methods/preferences/route.ts',
      'frontend/src/app/api/payment-methods/stripe/process/route.ts'
    ],
    endpoints: [
      'GET /api/payment-methods',
      'POST /api/payment-methods',
      'POST /api/payment-methods/validate',
      'POST /api/payment-methods/recommend',
      'GET /api/payment-methods/preferences',
      'POST /api/payment-methods/preferences',
      'PUT /api/payment-methods/preferences',
      'POST /api/payment-methods/stripe/process'
    ]
  },
  components: {
    name: 'UI Components',
    files: [
      'frontend/src/components/payment/PaymentMethodSelector.tsx',
      'frontend/src/components/payment/PaymentMethodForm.tsx',
      'frontend/src/components/payment/PaymentMethodManager.tsx',
      'frontend/src/components/payment/PaymentMethodSummary.tsx'
    ],
    features: [
      'Multi-country payment method selection',
      'Real-time validation',
      'Processing fee calculation',
      'Estimated completion time display',
      'Error handling and user feedback'
    ]
  },
  types: {
    name: 'TypeScript Types',
    files: [
      'frontend/src/lib/types/payment-methods.ts'
    ],
    interfaces: [
      'PaymentMethodType',
      'RegionalPaymentMethod',
      'PaymentMethodValidationRequest',
      'PaymentMethodValidationResponse',
      'StripePaymentMethodCreationRequest',
      'CustomerPaymentPreferences'
    ]
  },
  hooks: {
    name: 'React Hooks',
    files: [
      'frontend/src/lib/hooks/usePaymentMethods.ts',
      'frontend/src/lib/hooks/usePaymentProcessing.ts'
    ],
    functions: [
      'usePaymentMethods',
      'usePaymentProcessing'
    ]
  },
  tests: {
    name: 'Test Suite',
    files: [
      'frontend/src/tests/regional-payment-methods.test.ts',
      'frontend/src/tests/payment-methods-api.test.ts',
      'frontend/src/tests/payment-flow-e2e.test.ts'
    ],
    coverage: [
      'Unit tests for services',
      'Integration tests for API',
      'End-to-end tests for UI',
      'Multi-country scenarios',
      'Error handling'
    ]
  }
};

// Multi-country requirements
const MULTI_COUNTRY_REQUIREMENTS = {
  US: {
    currency: 'USD',
    paymentMethods: ['card', 'ach_debit'],
    validation: ['Luhn algorithm', 'ABA routing number'],
    compliance: ['PCI DSS', 'ACH rules']
  },
  BE: {
    currency: 'EUR',
    paymentMethods: ['card', 'sepa_debit', 'bancontact'],
    validation: ['IBAN MOD-97', 'BIC validation'],
    compliance: ['PSD2', 'SEPA regulations']
  },
  GB: {
    currency: 'GBP',
    paymentMethods: ['card', 'bacs_debit'],
    validation: ['Sort code validation', 'Account number format'],
    compliance: ['FCA regulations', 'BACS rules']
  },
  CA: {
    currency: 'CAD',
    paymentMethods: ['card', 'acss_debit'],
    validation: ['Transit number', 'Institution number'],
    compliance: ['CPA rules', 'PAD agreements']
  }
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(60), 'cyan');
  log(message, 'bright');
  log('='.repeat(60), 'cyan');
}

function logSubHeader(message) {
  log('\n' + '-'.repeat(40), 'blue');
  log(message, 'blue');
  log('-'.repeat(40), 'blue');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Validate file existence
function validateFiles(component) {
  logSubHeader(`Validating ${component.name} Files`);
  
  let allFilesExist = true;
  
  for (const file of component.files) {
    if (fs.existsSync(file)) {
      logSuccess(`Found: ${file}`);
    } else {
      logError(`Missing: ${file}`);
      allFilesExist = false;
    }
  }
  
  return allFilesExist;
}

// Validate file content
function validateFileContent(filePath, requiredContent) {
  if (!fs.existsSync(filePath)) {
    return { exists: false, hasContent: false, missingContent: requiredContent };
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const missingContent = [];
  
  for (const item of requiredContent) {
    if (!content.includes(item)) {
      missingContent.push(item);
    }
  }
  
  return {
    exists: true,
    hasContent: missingContent.length === 0,
    missingContent
  };
}

// Validate database schema
function validateDatabaseSchema() {
  logSubHeader('Validating Database Schema');
  
  const schemaFile = 'database/migrations/20241201_payment_methods_schema.sql';
  const typesFile = 'frontend/src/lib/types/database.types.ts';
  
  let isValid = true;
  
  // Check schema file
  const schemaValidation = validateFileContent(schemaFile, IMPLEMENTATION_COMPONENTS.database.tables);
  if (schemaValidation.exists) {
    if (schemaValidation.hasContent) {
      logSuccess('Database schema contains all required tables');
    } else {
      logError(`Missing tables: ${schemaValidation.missingContent.join(', ')}`);
      isValid = false;
    }
  } else {
    logError('Database schema file not found');
    isValid = false;
  }
  
  // Check types file
  if (fs.existsSync(typesFile)) {
    logSuccess('Database types file exists');
  } else {
    logWarning('Database types file not found - may need to be generated');
  }
  
  return isValid;
}

// Validate service layer
function validateServiceLayer() {
  logSubHeader('Validating Service Layer');
  
  let isValid = true;
  
  for (const file of IMPLEMENTATION_COMPONENTS.services.files) {
    const validation = validateFileContent(file, IMPLEMENTATION_COMPONENTS.services.methods);
    
    if (validation.exists) {
      if (validation.hasContent) {
        logSuccess(`${path.basename(file)} contains all required methods`);
      } else {
        logError(`${path.basename(file)} missing methods: ${validation.missingContent.join(', ')}`);
        isValid = false;
      }
    } else {
      logError(`Service file not found: ${file}`);
      isValid = false;
    }
  }
  
  return isValid;
}

// Validate API routes
function validateAPIRoutes() {
  logSubHeader('Validating API Routes');
  
  let isValid = true;
  
  for (const file of IMPLEMENTATION_COMPONENTS.api.files) {
    if (fs.existsSync(file)) {
      logSuccess(`API route exists: ${path.basename(file)}`);
    } else {
      logError(`API route missing: ${file}`);
      isValid = false;
    }
  }
  
  return isValid;
}

// Validate UI components
function validateUIComponents() {
  logSubHeader('Validating UI Components');
  
  let isValid = true;
  
  for (const file of IMPLEMENTATION_COMPONENTS.components.files) {
    if (fs.existsSync(file)) {
      logSuccess(`Component exists: ${path.basename(file)}`);
    } else {
      logError(`Component missing: ${file}`);
      isValid = false;
    }
  }
  
  return isValid;
}

// Validate TypeScript types
function validateTypeScriptTypes() {
  logSubHeader('Validating TypeScript Types');
  
  const typesFile = IMPLEMENTATION_COMPONENTS.types.files[0];
  const validation = validateFileContent(typesFile, IMPLEMENTATION_COMPONENTS.types.interfaces);
  
  if (validation.exists) {
    if (validation.hasContent) {
      logSuccess('All required TypeScript interfaces are defined');
      return true;
    } else {
      logError(`Missing interfaces: ${validation.missingContent.join(', ')}`);
      return false;
    }
  } else {
    logError('TypeScript types file not found');
    return false;
  }
}

// Validate React hooks
function validateReactHooks() {
  logSubHeader('Validating React Hooks');
  
  let isValid = true;
  
  for (const file of IMPLEMENTATION_COMPONENTS.hooks.files) {
    if (fs.existsSync(file)) {
      logSuccess(`Hook exists: ${path.basename(file)}`);
    } else {
      logError(`Hook missing: ${file}`);
      isValid = false;
    }
  }
  
  return isValid;
}

// Validate test suite
function validateTestSuite() {
  logSubHeader('Validating Test Suite');
  
  let isValid = true;
  
  for (const file of IMPLEMENTATION_COMPONENTS.tests.files) {
    if (fs.existsSync(file)) {
      logSuccess(`Test file exists: ${path.basename(file)}`);
    } else {
      logError(`Test file missing: ${file}`);
      isValid = false;
    }
  }
  
  return isValid;
}

// Validate multi-country support
function validateMultiCountrySupport() {
  logSubHeader('Validating Multi-Country Support');
  
  logInfo('Checking multi-country requirements...');
  
  for (const [country, requirements] of Object.entries(MULTI_COUNTRY_REQUIREMENTS)) {
    logInfo(`${country} (${requirements.currency}):`);
    logInfo(`  Payment methods: ${requirements.paymentMethods.join(', ')}`);
    logInfo(`  Validation: ${requirements.validation.join(', ')}`);
    logInfo(`  Compliance: ${requirements.compliance.join(', ')}`);
  }
  
  logSuccess('Multi-country requirements documented');
  return true;
}

// Generate validation report
function generateValidationReport(results) {
  const report = {
    timestamp: new Date().toISOString(),
    components: results,
    summary: {
      totalComponents: Object.keys(results).length,
      validComponents: Object.values(results).filter(r => r).length,
      invalidComponents: Object.values(results).filter(r => !r).length
    }
  };
  
  // Write report
  const reportPath = 'validation-reports/payment-methods-validation.json';
  const reportDir = path.dirname(reportPath);
  
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }
  
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  logSuccess(`Validation report saved to: ${reportPath}`);
  
  return report;
}

// Main validation function
function main() {
  logHeader('Regional Payment Methods Implementation Validator');
  
  const results = {
    database: validateDatabaseSchema(),
    services: validateServiceLayer(),
    api: validateAPIRoutes(),
    components: validateUIComponents(),
    types: validateTypeScriptTypes(),
    hooks: validateReactHooks(),
    tests: validateTestSuite(),
    multiCountry: validateMultiCountrySupport()
  };
  
  // Generate report
  const report = generateValidationReport(results);
  
  // Display summary
  logHeader('VALIDATION SUMMARY');
  
  log(`Total Components: ${report.summary.totalComponents}`, 'bright');
  log(`Valid: ${report.summary.validComponents}`, 'green');
  log(`Invalid: ${report.summary.invalidComponents}`, 'red');
  
  const overallValid = report.summary.invalidComponents === 0;
  
  if (overallValid) {
    logSuccess('\n🎉 ALL COMPONENTS VALID! Regional Payment Methods implementation is complete.');
  } else {
    logError('\n❌ SOME COMPONENTS INVALID. Please review and fix issues.');
  }
  
  return overallValid;
}

// Run validation
if (require.main === module) {
  const success = main();
  process.exit(success ? 0 : 1);
}

module.exports = {
  validateDatabaseSchema,
  validateServiceLayer,
  validateAPIRoutes,
  validateUIComponents,
  validateTypeScriptTypes,
  validateReactHooks,
  validateTestSuite,
  validateMultiCountrySupport,
  IMPLEMENTATION_COMPONENTS,
  MULTI_COUNTRY_REQUIREMENTS
};
