#!/bin/bash

# Deployment Health Check Script
# Comprehensive health validation for production deployment

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
HEALTH_ENDPOINT="${HEALTH_ENDPOINT:-http://localhost:3000/api/health}"
METRICS_ENDPOINT="${METRICS_ENDPOINT:-http://localhost:3000/api/metrics}"
MAX_RETRIES="${MAX_RETRIES:-5}"
RETRY_DELAY="${RETRY_DELAY:-10}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color
BOLD='\033[1m'

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_critical() {
    echo -e "${RED}${BOLD}[CRITICAL]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}${BOLD}$1${NC}"
}

# Check if required tools are available
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    local missing_tools=()
    
    if ! command -v curl &> /dev/null; then
        missing_tools+=("curl")
    fi
    
    if ! command -v jq &> /dev/null; then
        missing_tools+=("jq")
    fi
    
    if ! command -v node &> /dev/null; then
        missing_tools+=("node")
    fi
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        log_error "Missing required tools: ${missing_tools[*]}"
        log_info "Please install the missing tools and try again."
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Wait for service to be available
wait_for_service() {
    local endpoint="$1"
    local service_name="$2"
    local retries=0
    
    log_info "Waiting for $service_name to be available at $endpoint..."
    
    while [ $retries -lt $MAX_RETRIES ]; do
        if curl -s -f "$endpoint" > /dev/null 2>&1; then
            log_success "$service_name is available"
            return 0
        fi
        
        retries=$((retries + 1))
        log_warning "Attempt $retries/$MAX_RETRIES failed, retrying in ${RETRY_DELAY}s..."
        sleep $RETRY_DELAY
    done
    
    log_error "$service_name is not available after $MAX_RETRIES attempts"
    return 1
}

# Validate environment configuration
validate_environment() {
    log_header "=== Environment Validation ==="
    
    if [ -f "$PROJECT_ROOT/scripts/validate-production-environment.js" ]; then
        log_info "Running environment validation script..."
        
        cd "$PROJECT_ROOT"
        if node scripts/validate-production-environment.js; then
            log_success "Environment validation passed"
            return 0
        else
            log_error "Environment validation failed"
            return 1
        fi
    else
        log_warning "Environment validation script not found, skipping..."
        return 0
    fi
}

# Check basic connectivity
check_basic_connectivity() {
    log_header "=== Basic Connectivity Check ==="
    
    log_info "Testing basic health endpoint..."
    
    local response
    local http_code
    
    response=$(curl -s -w "%{http_code}" "$HEALTH_ENDPOINT" || echo "000")
    http_code="${response: -3}"
    
    if [ "$http_code" = "200" ]; then
        log_success "Basic health check passed (HTTP $http_code)"
        return 0
    else
        log_error "Basic health check failed (HTTP $http_code)"
        return 1
    fi
}

# Perform detailed health check
detailed_health_check() {
    log_header "=== Detailed Health Check ==="
    
    log_info "Performing comprehensive health check..."
    
    local response
    local http_code
    local temp_file
    
    temp_file=$(mktemp)
    
    # Get detailed health status
    response=$(curl -s -w "%{http_code}" "$HEALTH_ENDPOINT?detailed=true" -o "$temp_file" || echo "000")
    http_code="${response: -3}"
    
    if [ "$http_code" != "200" ] && [ "$http_code" != "503" ]; then
        log_error "Health check endpoint returned HTTP $http_code"
        rm -f "$temp_file"
        return 1
    fi
    
    # Parse health check response
    if command -v jq &> /dev/null; then
        local overall_status
        local score
        local checks
        
        overall_status=$(jq -r '.status' "$temp_file" 2>/dev/null || echo "unknown")
        score=$(jq -r '.score' "$temp_file" 2>/dev/null || echo "0")
        
        log_info "Overall Status: $overall_status"
        log_info "Health Score: $score%"
        
        # Check individual services
        log_info "Individual service checks:"
        
        local services=("environment" "database" "stripe" "payment_encryption" "monitoring")
        local failed_services=()
        
        for service in "${services[@]}"; do
            local service_status
            service_status=$(jq -r ".checks[] | select(.service == \"$service\") | .status" "$temp_file" 2>/dev/null || echo "unknown")
            
            case "$service_status" in
                "healthy")
                    log_success "  ✓ $service: healthy"
                    ;;
                "degraded")
                    log_warning "  ⚠ $service: degraded"
                    ;;
                "unhealthy")
                    log_error "  ✗ $service: unhealthy"
                    failed_services+=("$service")
                    ;;
                *)
                    log_warning "  ? $service: unknown status"
                    ;;
            esac
        done
        
        # Overall assessment
        if [ "$overall_status" = "healthy" ]; then
            log_success "Detailed health check passed"
            rm -f "$temp_file"
            return 0
        elif [ "$overall_status" = "degraded" ]; then
            log_warning "System is operational but degraded"
            rm -f "$temp_file"
            return 0
        else
            log_error "System is unhealthy. Failed services: ${failed_services[*]}"
            rm -f "$temp_file"
            return 1
        fi
    else
        log_warning "jq not available, cannot parse detailed health response"
        rm -f "$temp_file"
        return 0
    fi
}

# Check metrics endpoint
check_metrics_endpoint() {
    log_header "=== Metrics Endpoint Check ==="
    
    log_info "Testing metrics endpoint..."
    
    local response
    local http_code
    
    response=$(curl -s -w "%{http_code}" "$METRICS_ENDPOINT" || echo "000")
    http_code="${response: -3}"
    
    if [ "$http_code" = "200" ]; then
        log_success "Metrics endpoint is accessible (HTTP $http_code)"
        
        # Check if response contains Prometheus metrics
        local metrics_content
        metrics_content=$(curl -s "$METRICS_ENDPOINT" | head -10)
        
        if echo "$metrics_content" | grep -q "# HELP\|# TYPE"; then
            log_success "Metrics endpoint returns valid Prometheus format"
            return 0
        else
            log_warning "Metrics endpoint accessible but format unclear"
            return 0
        fi
    else
        log_warning "Metrics endpoint not accessible (HTTP $http_code)"
        return 0  # Non-critical for basic functionality
    fi
}

# Test payment system functionality
test_payment_functionality() {
    log_header "=== Payment System Functionality Test ==="
    
    log_info "Testing payment system endpoints..."
    
    # Test payment method validation endpoint (if available)
    local validation_endpoint="$HEALTH_ENDPOINT/../validate-payment-method"
    local response
    local http_code
    
    # This is a basic connectivity test - actual payment testing would require test data
    response=$(curl -s -w "%{http_code}" -X OPTIONS "$validation_endpoint" || echo "000")
    http_code="${response: -3}"
    
    if [ "$http_code" = "200" ] || [ "$http_code" = "405" ]; then
        log_success "Payment validation endpoint is accessible"
    else
        log_warning "Payment validation endpoint test inconclusive (HTTP $http_code)"
    fi
    
    # Note: Actual payment testing would require:
    # - Test Stripe keys
    # - Test payment method data
    # - Proper authentication
    log_info "Note: Full payment functionality testing requires test environment setup"
    
    return 0
}

# Check monitoring integration
check_monitoring_integration() {
    log_header "=== Monitoring Integration Check ==="
    
    log_info "Checking monitoring system integration..."
    
    # Check if Sentry is configured
    if [ -n "${NEXT_PUBLIC_SENTRY_DSN:-}" ]; then
        log_success "Sentry DSN configured"
    else
        log_warning "Sentry DSN not configured - error tracking disabled"
    fi
    
    # Check if alerting is configured
    local alerting_configured=false
    
    if [ -n "${SLACK_WEBHOOK_URL:-}" ]; then
        log_success "Slack webhook configured"
        alerting_configured=true
    fi
    
    if [ -n "${PAGERDUTY_PAYMENT_KEY:-}" ]; then
        log_success "PagerDuty integration configured"
        alerting_configured=true
    fi
    
    if [ -n "${SMTP_PASSWORD:-}" ]; then
        log_success "Email alerting configured"
        alerting_configured=true
    fi
    
    if [ "$alerting_configured" = false ]; then
        log_warning "No alerting channels configured"
    fi
    
    return 0
}

# Generate deployment report
generate_deployment_report() {
    log_header "=== Deployment Health Report ==="
    
    local timestamp
    timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    echo "Deployment Health Check Report"
    echo "=============================="
    echo "Timestamp: $timestamp"
    echo "Environment: ${NODE_ENV:-unknown}"
    echo "Version: ${NEXT_PUBLIC_APP_VERSION:-unknown}"
    echo "Health Endpoint: $HEALTH_ENDPOINT"
    echo "Metrics Endpoint: $METRICS_ENDPOINT"
    echo ""
    
    # Summary of checks
    echo "Check Summary:"
    echo "- Environment Validation: ${ENV_CHECK_STATUS:-SKIPPED}"
    echo "- Basic Connectivity: ${BASIC_CHECK_STATUS:-FAILED}"
    echo "- Detailed Health Check: ${DETAILED_CHECK_STATUS:-FAILED}"
    echo "- Metrics Endpoint: ${METRICS_CHECK_STATUS:-FAILED}"
    echo "- Payment Functionality: ${PAYMENT_CHECK_STATUS:-FAILED}"
    echo "- Monitoring Integration: ${MONITORING_CHECK_STATUS:-FAILED}"
    echo ""
    
    if [ "${OVERALL_STATUS:-FAILED}" = "PASSED" ]; then
        echo "Overall Status: ✅ DEPLOYMENT READY"
    else
        echo "Overall Status: ❌ DEPLOYMENT NOT READY"
        echo ""
        echo "Please address the failed checks before deploying to production."
    fi
}

# Main execution function
main() {
    log_header "🚀 PI Lawyer AI - Deployment Health Check"
    echo "=================================================="
    
    local overall_success=true
    
    # Check prerequisites
    if ! check_prerequisites; then
        exit 1
    fi
    
    # Wait for service to be available
    if ! wait_for_service "$HEALTH_ENDPOINT" "Application"; then
        log_critical "Application is not responding, cannot proceed with health checks"
        exit 1
    fi
    
    # Run validation checks
    if validate_environment; then
        ENV_CHECK_STATUS="PASSED"
    else
        ENV_CHECK_STATUS="FAILED"
        overall_success=false
    fi
    
    if check_basic_connectivity; then
        BASIC_CHECK_STATUS="PASSED"
    else
        BASIC_CHECK_STATUS="FAILED"
        overall_success=false
    fi
    
    if detailed_health_check; then
        DETAILED_CHECK_STATUS="PASSED"
    else
        DETAILED_CHECK_STATUS="FAILED"
        overall_success=false
    fi
    
    if check_metrics_endpoint; then
        METRICS_CHECK_STATUS="PASSED"
    else
        METRICS_CHECK_STATUS="WARNING"
        # Metrics endpoint failure is not critical for basic functionality
    fi
    
    if test_payment_functionality; then
        PAYMENT_CHECK_STATUS="PASSED"
    else
        PAYMENT_CHECK_STATUS="WARNING"
        # Payment functionality test is basic connectivity only
    fi
    
    if check_monitoring_integration; then
        MONITORING_CHECK_STATUS="PASSED"
    else
        MONITORING_CHECK_STATUS="WARNING"
        # Monitoring integration issues are warnings, not failures
    fi
    
    # Set overall status
    if [ "$overall_success" = true ]; then
        OVERALL_STATUS="PASSED"
    else
        OVERALL_STATUS="FAILED"
    fi
    
    # Generate report
    generate_deployment_report
    
    # Exit with appropriate code
    if [ "$OVERALL_STATUS" = "PASSED" ]; then
        log_success "🎉 Deployment health check completed successfully!"
        exit 0
    else
        log_critical "💥 Deployment health check failed!"
        exit 1
    fi
}

# Export variables for report generation
export ENV_CHECK_STATUS BASIC_CHECK_STATUS DETAILED_CHECK_STATUS
export METRICS_CHECK_STATUS PAYMENT_CHECK_STATUS MONITORING_CHECK_STATUS
export OVERALL_STATUS

# Run main function
main "$@"
