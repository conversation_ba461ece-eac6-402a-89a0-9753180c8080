#!/usr/bin/env node

/**
 * Deployment Readiness Validation Script
 * 
 * Comprehensive validation to ensure the system is ready for production deployment
 * Combines environment validation, health checks, and deployment prerequisites
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// Colors for output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

// Logging functions
const log = {
  info: (msg) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`),
  critical: (msg) => console.log(`${colors.red}${colors.bold}[CRITICAL]${colors.reset} ${msg}`),
  header: (msg) => console.log(`${colors.magenta}${colors.bold}${msg}${colors.reset}`)
};

class DeploymentReadinessValidator {
  constructor() {
    this.results = {
      environment: { status: 'pending', details: {} },
      health: { status: 'pending', details: {} },
      security: { status: 'pending', details: {} },
      performance: { status: 'pending', details: {} },
      monitoring: { status: 'pending', details: {} }
    };
    this.overallStatus = 'pending';
    this.criticalIssues = [];
    this.warnings = [];
  }

  /**
   * Run external script and capture output
   */
  async runScript(scriptPath, args = []) {
    return new Promise((resolve, reject) => {
      const child = spawn('node', [scriptPath, ...args], {
        cwd: process.cwd(),
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let stdout = '';
      let stderr = '';

      child.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      child.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      child.on('close', (code) => {
        resolve({
          code,
          stdout,
          stderr,
          success: code === 0
        });
      });

      child.on('error', (error) => {
        reject(error);
      });
    });
  }

  /**
   * Make HTTP request with timeout
   */
  async makeRequest(url, options = {}) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), options.timeout || 10000);

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal
      });
      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  /**
   * Validate environment configuration
   */
  async validateEnvironment() {
    log.header('=== Environment Configuration Validation ===');
    
    try {
      const envValidatorPath = path.join(process.cwd(), 'scripts', 'validate-production-environment.js');
      
      if (!fs.existsSync(envValidatorPath)) {
        this.results.environment = {
          status: 'warning',
          details: { message: 'Environment validator script not found' }
        };
        this.warnings.push('Environment validator script not found');
        return;
      }

      log.info('Running environment validation...');
      const result = await this.runScript(envValidatorPath);

      if (result.success) {
        this.results.environment = {
          status: 'success',
          details: { 
            message: 'Environment validation passed',
            output: result.stdout
          }
        };
        log.success('Environment validation passed');
      } else {
        this.results.environment = {
          status: 'error',
          details: { 
            message: 'Environment validation failed',
            output: result.stderr || result.stdout
          }
        };
        this.criticalIssues.push('Environment validation failed');
        log.error('Environment validation failed');
      }

    } catch (error) {
      this.results.environment = {
        status: 'error',
        details: { message: `Environment validation error: ${error.message}` }
      };
      this.criticalIssues.push(`Environment validation error: ${error.message}`);
      log.error(`Environment validation error: ${error.message}`);
    }
  }

  /**
   * Validate health checks
   */
  async validateHealth() {
    log.header('=== Health Check Validation ===');
    
    try {
      const healthUrl = process.env.HEALTH_ENDPOINT || 'http://localhost:3000/api/health/detailed';
      
      log.info(`Testing health endpoint: ${healthUrl}`);
      const response = await this.makeRequest(healthUrl, { timeout: 15000 });
      
      if (response.ok) {
        const healthData = await response.json();
        
        const unhealthyServices = healthData.checks?.filter(check => check.status === 'unhealthy') || [];
        const degradedServices = healthData.checks?.filter(check => check.status === 'degraded') || [];
        
        if (unhealthyServices.length > 0) {
          this.results.health = {
            status: 'error',
            details: {
              message: `${unhealthyServices.length} services are unhealthy`,
              unhealthy_services: unhealthyServices.map(s => s.service),
              health_data: healthData
            }
          };
          this.criticalIssues.push(`Unhealthy services: ${unhealthyServices.map(s => s.service).join(', ')}`);
          log.error(`Health check failed: ${unhealthyServices.length} services unhealthy`);
        } else if (degradedServices.length > 0) {
          this.results.health = {
            status: 'warning',
            details: {
              message: `${degradedServices.length} services are degraded`,
              degraded_services: degradedServices.map(s => s.service),
              health_data: healthData
            }
          };
          this.warnings.push(`Degraded services: ${degradedServices.map(s => s.service).join(', ')}`);
          log.warning(`Health check warning: ${degradedServices.length} services degraded`);
        } else {
          this.results.health = {
            status: 'success',
            details: {
              message: 'All health checks passed',
              health_score: healthData.summary?.healthy_checks || 0,
              total_checks: healthData.summary?.total_checks || 0
            }
          };
          log.success('All health checks passed');
        }
      } else {
        this.results.health = {
          status: 'error',
          details: { message: `Health endpoint returned HTTP ${response.status}` }
        };
        this.criticalIssues.push(`Health endpoint returned HTTP ${response.status}`);
        log.error(`Health endpoint returned HTTP ${response.status}`);
      }

    } catch (error) {
      this.results.health = {
        status: 'error',
        details: { message: `Health check error: ${error.message}` }
      };
      this.criticalIssues.push(`Health check error: ${error.message}`);
      log.error(`Health check error: ${error.message}`);
    }
  }

  /**
   * Validate security configuration
   */
  async validateSecurity() {
    log.header('=== Security Configuration Validation ===');
    
    try {
      const securityChecks = [];
      
      // Check for production secrets
      const requiredSecrets = [
        'JWT_SECRET',
        'ENCRYPTION_KEY',
        'PAYMENT_ENCRYPTION_KEY',
        'PAYMENT_MASTER_KEY',
        'STRIPE_SECRET_KEY'
      ];
      
      const missingSecrets = requiredSecrets.filter(secret => !process.env[secret]);
      if (missingSecrets.length > 0) {
        securityChecks.push({
          check: 'required_secrets',
          status: 'error',
          message: `Missing required secrets: ${missingSecrets.join(', ')}`
        });
      } else {
        securityChecks.push({
          check: 'required_secrets',
          status: 'success',
          message: 'All required secrets configured'
        });
      }
      
      // Check for strong secrets (minimum length)
      const weakSecrets = requiredSecrets.filter(secret => {
        const value = process.env[secret];
        return value && value.length < 32;
      });
      
      if (weakSecrets.length > 0) {
        securityChecks.push({
          check: 'secret_strength',
          status: 'warning',
          message: `Weak secrets (< 32 chars): ${weakSecrets.join(', ')}`
        });
      } else {
        securityChecks.push({
          check: 'secret_strength',
          status: 'success',
          message: 'All secrets meet minimum length requirements'
        });
      }
      
      // Check for production Stripe keys
      const stripeKey = process.env.STRIPE_SECRET_KEY;
      if (stripeKey && stripeKey.startsWith('sk_test_')) {
        securityChecks.push({
          check: 'stripe_keys',
          status: 'warning',
          message: 'Using Stripe test keys in production'
        });
      } else if (stripeKey && stripeKey.startsWith('sk_live_')) {
        securityChecks.push({
          check: 'stripe_keys',
          status: 'success',
          message: 'Using Stripe live keys'
        });
      }
      
      // Check HTTPS enforcement
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_API_URL;
      
      if ((supabaseUrl && !supabaseUrl.startsWith('https://')) || 
          (backendUrl && !backendUrl.startsWith('https://'))) {
        securityChecks.push({
          check: 'https_enforcement',
          status: 'error',
          message: 'External URLs must use HTTPS in production'
        });
      } else {
        securityChecks.push({
          check: 'https_enforcement',
          status: 'success',
          message: 'HTTPS properly enforced'
        });
      }
      
      // Determine overall security status
      const errorChecks = securityChecks.filter(c => c.status === 'error');
      const warningChecks = securityChecks.filter(c => c.status === 'warning');
      
      if (errorChecks.length > 0) {
        this.results.security = {
          status: 'error',
          details: { checks: securityChecks, errors: errorChecks.length }
        };
        errorChecks.forEach(check => this.criticalIssues.push(check.message));
        log.error(`Security validation failed: ${errorChecks.length} critical issues`);
      } else if (warningChecks.length > 0) {
        this.results.security = {
          status: 'warning',
          details: { checks: securityChecks, warnings: warningChecks.length }
        };
        warningChecks.forEach(check => this.warnings.push(check.message));
        log.warning(`Security validation passed with warnings: ${warningChecks.length} issues`);
      } else {
        this.results.security = {
          status: 'success',
          details: { checks: securityChecks, message: 'All security checks passed' }
        };
        log.success('Security validation passed');
      }

    } catch (error) {
      this.results.security = {
        status: 'error',
        details: { message: `Security validation error: ${error.message}` }
      };
      this.criticalIssues.push(`Security validation error: ${error.message}`);
      log.error(`Security validation error: ${error.message}`);
    }
  }

  /**
   * Validate monitoring configuration
   */
  async validateMonitoring() {
    log.header('=== Monitoring Configuration Validation ===');
    
    try {
      const monitoringChecks = [];
      
      // Check Sentry configuration
      if (process.env.NEXT_PUBLIC_SENTRY_DSN) {
        monitoringChecks.push({
          check: 'sentry',
          status: 'success',
          message: 'Sentry error tracking configured'
        });
      } else {
        monitoringChecks.push({
          check: 'sentry',
          status: 'warning',
          message: 'Sentry error tracking not configured'
        });
      }
      
      // Check metrics endpoint
      try {
        const metricsUrl = process.env.METRICS_ENDPOINT || 'http://localhost:3000/api/metrics';
        const response = await this.makeRequest(metricsUrl, { timeout: 5000 });
        
        if (response.ok) {
          monitoringChecks.push({
            check: 'metrics',
            status: 'success',
            message: 'Metrics endpoint accessible'
          });
        } else {
          monitoringChecks.push({
            check: 'metrics',
            status: 'warning',
            message: `Metrics endpoint returned HTTP ${response.status}`
          });
        }
      } catch (error) {
        monitoringChecks.push({
          check: 'metrics',
          status: 'warning',
          message: 'Metrics endpoint not accessible'
        });
      }
      
      // Check alerting configuration
      const alertingChannels = [];
      if (process.env.SLACK_WEBHOOK_URL) alertingChannels.push('Slack');
      if (process.env.PAGERDUTY_PAYMENT_KEY) alertingChannels.push('PagerDuty');
      if (process.env.SMTP_PASSWORD) alertingChannels.push('Email');
      
      if (alertingChannels.length > 0) {
        monitoringChecks.push({
          check: 'alerting',
          status: 'success',
          message: `Alerting configured: ${alertingChannels.join(', ')}`
        });
      } else {
        monitoringChecks.push({
          check: 'alerting',
          status: 'warning',
          message: 'No alerting channels configured'
        });
      }
      
      // Determine overall monitoring status
      const warningChecks = monitoringChecks.filter(c => c.status === 'warning');
      
      if (warningChecks.length > 0) {
        this.results.monitoring = {
          status: 'warning',
          details: { checks: monitoringChecks, warnings: warningChecks.length }
        };
        warningChecks.forEach(check => this.warnings.push(check.message));
        log.warning(`Monitoring validation passed with warnings: ${warningChecks.length} issues`);
      } else {
        this.results.monitoring = {
          status: 'success',
          details: { checks: monitoringChecks, message: 'All monitoring checks passed' }
        };
        log.success('Monitoring validation passed');
      }

    } catch (error) {
      this.results.monitoring = {
        status: 'error',
        details: { message: `Monitoring validation error: ${error.message}` }
      };
      this.criticalIssues.push(`Monitoring validation error: ${error.message}`);
      log.error(`Monitoring validation error: ${error.message}`);
    }
  }

  /**
   * Generate deployment readiness report
   */
  generateReport() {
    const errorCount = Object.values(this.results).filter(r => r.status === 'error').length;
    const warningCount = Object.values(this.results).filter(r => r.status === 'warning').length;
    const successCount = Object.values(this.results).filter(r => r.status === 'success').length;
    
    this.overallStatus = errorCount > 0 ? 'not_ready' : 
                       warningCount > 0 ? 'ready_with_warnings' : 'ready';

    const report = {
      timestamp: new Date().toISOString(),
      overall_status: this.overallStatus,
      environment: process.env.NODE_ENV || 'unknown',
      version: process.env.NEXT_PUBLIC_APP_VERSION || 'unknown',
      results: this.results,
      summary: {
        total_checks: Object.keys(this.results).length,
        success_count: successCount,
        warning_count: warningCount,
        error_count: errorCount,
        critical_issues: this.criticalIssues,
        warnings: this.warnings
      }
    };

    this.printReport(report);
    return report;
  }

  /**
   * Print deployment readiness report
   */
  printReport(report) {
    console.log('\n' + '='.repeat(70));
    log.header('DEPLOYMENT READINESS VALIDATION REPORT');
    console.log('='.repeat(70));
    
    console.log(`Timestamp: ${colors.cyan}${report.timestamp}${colors.reset}`);
    console.log(`Environment: ${colors.cyan}${report.environment}${colors.reset}`);
    console.log(`Version: ${colors.cyan}${report.version}${colors.reset}`);
    
    // Overall status
    const statusColor = report.overall_status === 'ready' ? colors.green :
                       report.overall_status === 'ready_with_warnings' ? colors.yellow : colors.red;
    console.log(`Overall Status: ${statusColor}${report.overall_status.toUpperCase()}${colors.reset}`);
    
    // Results summary
    console.log(`\nValidation Results:`);
    console.log(`  ✅ Success: ${report.summary.success_count}`);
    console.log(`  ⚠️  Warnings: ${report.summary.warning_count}`);
    console.log(`  ❌ Errors: ${report.summary.error_count}`);
    
    // Critical issues
    if (report.summary.critical_issues.length > 0) {
      console.log(`\n${colors.red}${colors.bold}CRITICAL ISSUES:${colors.reset}`);
      report.summary.critical_issues.forEach((issue, index) => {
        console.log(`  ${index + 1}. ${issue}`);
      });
    }
    
    // Warnings
    if (report.summary.warnings.length > 0) {
      console.log(`\n${colors.yellow}${colors.bold}WARNINGS:${colors.reset}`);
      report.summary.warnings.forEach((warning, index) => {
        console.log(`  ${index + 1}. ${warning}`);
      });
    }
    
    console.log('\n' + '='.repeat(70));
    
    // Final recommendation
    if (report.overall_status === 'ready') {
      log.success('🎉 System is ready for production deployment!');
    } else if (report.overall_status === 'ready_with_warnings') {
      log.warning('⚠️  System is ready for deployment but has warnings. Review and address if needed.');
    } else {
      log.critical('❌ System is NOT ready for production deployment. Fix critical issues first.');
    }
  }

  /**
   * Run all validation checks
   */
  async validate() {
    log.header('🚀 PI Lawyer AI - Deployment Readiness Validation');
    console.log('='.repeat(70));
    
    await this.validateEnvironment();
    await this.validateHealth();
    await this.validateSecurity();
    await this.validateMonitoring();
    
    return this.generateReport();
  }
}

// Main execution
async function main() {
  const validator = new DeploymentReadinessValidator();
  const report = await validator.validate();
  
  // Exit with appropriate code
  process.exit(report.overall_status === 'not_ready' ? 1 : 0);
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { DeploymentReadinessValidator };
