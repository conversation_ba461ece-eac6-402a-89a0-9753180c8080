#!/usr/bin/env node

/**
 * Payment System Test Runner
 * 
 * Comprehensive test runner for payment system components with
 * coverage reporting, performance monitoring, and CI/CD integration.
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const TEST_CONFIG = {
  coverageThreshold: 90,
  testTimeout: 30000,
  maxWorkers: '50%',
  verbose: true
};

const TEST_SUITES = {
  unit: {
    pattern: 'src/**/__tests__/unit/**/*.test.ts',
    description: 'Unit tests for individual components'
  },
  integration: {
    pattern: 'src/**/__tests__/integration/**/*.test.ts',
    description: 'Integration tests for component interactions'
  },
  security: {
    pattern: 'src/**/__tests__/auth/**/*.test.ts src/**/__tests__/security/**/*.test.ts',
    description: 'Security and authentication tests'
  },
  payment: {
    pattern: 'src/**/__tests__/**/*payment*.test.ts src/**/__tests__/**/*stripe*.test.ts',
    description: 'Payment processing and validation tests'
  },
  compliance: {
    pattern: 'src/**/__tests__/**/*compliance*.test.ts',
    description: 'Compliance and regulatory tests'
  },
  all: {
    pattern: 'src/**/__tests__/**/*.test.ts',
    description: 'All test suites'
  }
};

/**
 * Parse command line arguments
 */
function parseArgs() {
  const args = process.argv.slice(2);
  const options = {
    suite: 'all',
    coverage: true,
    watch: false,
    verbose: false,
    ci: false,
    updateSnapshots: false,
    bail: false,
    silent: false,
    generateReport: false
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    switch (arg) {
      case '--suite':
      case '-s':
        options.suite = args[++i] || 'all';
        break;
      case '--no-coverage':
        options.coverage = false;
        break;
      case '--watch':
      case '-w':
        options.watch = true;
        break;
      case '--verbose':
      case '-v':
        options.verbose = true;
        break;
      case '--ci':
        options.ci = true;
        break;
      case '--update-snapshots':
      case '-u':
        options.updateSnapshots = true;
        break;
      case '--bail':
        options.bail = true;
        break;
      case '--silent':
        options.silent = true;
        break;
      case '--report':
        options.generateReport = true;
        break;
      case '--help':
      case '-h':
        showHelp();
        process.exit(0);
        break;
      default:
        if (arg.startsWith('--')) {
          console.warn(`Unknown option: ${arg}`);
        }
    }
  }

  return options;
}

/**
 * Show help information
 */
function showHelp() {
  console.log('Payment System Test Runner');
  console.log('');
  console.log('Usage: node scripts/run-payment-tests.js [options]');
  console.log('');
  console.log('Options:');
  console.log('  --suite, -s <suite>    Test suite to run (unit|integration|security|payment|compliance|all)');
  console.log('  --no-coverage          Disable coverage reporting');
  console.log('  --watch, -w            Watch mode for development');
  console.log('  --verbose, -v          Verbose output');
  console.log('  --ci                   CI mode with optimized settings');
  console.log('  --update-snapshots, -u Update test snapshots');
  console.log('  --bail                 Stop on first test failure');
  console.log('  --silent               Minimal output');
  console.log('  --report               Generate detailed HTML report');
  console.log('  --help, -h             Show this help message');
  console.log('');
  console.log('Available test suites:');
  Object.entries(TEST_SUITES).forEach(([name, config]) => {
    console.log(`  ${name.padEnd(12)} ${config.description}`);
  });
}

/**
 * Build Jest command arguments
 */
function buildJestArgs(options) {
  const args = [];

  // Test pattern
  const suite = TEST_SUITES[options.suite];
  if (!suite) {
    console.error(`Unknown test suite: ${options.suite}`);
    console.error(`Available suites: ${Object.keys(TEST_SUITES).join(', ')}`);
    process.exit(1);
  }

  if (suite.pattern !== TEST_SUITES.all.pattern) {
    args.push('--testPathPattern', suite.pattern);
  }

  // Coverage options
  if (options.coverage) {
    args.push('--coverage');
    args.push('--coverageReporters', 'text', 'text-summary', 'lcov', 'html');
    args.push('--coverageDirectory', 'coverage');
  }

  // Watch mode
  if (options.watch) {
    args.push('--watch');
  }

  // Verbose output
  if (options.verbose || TEST_CONFIG.verbose) {
    args.push('--verbose');
  }

  // CI mode
  if (options.ci) {
    args.push('--ci');
    args.push('--watchAll=false');
    args.push('--passWithNoTests');
  }

  // Update snapshots
  if (options.updateSnapshots) {
    args.push('--updateSnapshot');
  }

  // Bail on first failure
  if (options.bail) {
    args.push('--bail');
  }

  // Silent mode
  if (options.silent) {
    args.push('--silent');
  }

  // Performance options
  args.push('--maxWorkers', TEST_CONFIG.maxWorkers);
  args.push('--testTimeout', TEST_CONFIG.testTimeout.toString());

  return args;
}

/**
 * Run tests with Jest
 */
function runTests(options) {
  const jestArgs = buildJestArgs(options);
  
  console.log(`🧪 Running ${options.suite} tests...`);
  console.log(`📋 Test pattern: ${TEST_SUITES[options.suite].pattern}`);
  
  if (!options.silent) {
    console.log(`🔧 Jest args: ${jestArgs.join(' ')}`);
    console.log('');
  }

  try {
    // Change to frontend directory
    process.chdir(path.join(__dirname, '..', 'frontend'));
    
    // Run Jest
    const result = execSync(`npx jest ${jestArgs.join(' ')}`, {
      stdio: 'inherit',
      encoding: 'utf8'
    });

    console.log('');
    console.log('✅ Tests completed successfully!');
    
    if (options.coverage) {
      console.log('📊 Coverage report generated in coverage/ directory');
    }

    return true;

  } catch (error) {
    console.error('');
    console.error('❌ Tests failed!');
    console.error(`Exit code: ${error.status}`);
    return false;
  }
}

/**
 * Generate test report
 */
function generateReport(options) {
  if (!options.generateReport) {
    return;
  }

  console.log('📈 Generating detailed test report...');

  try {
    // Read coverage data
    const coveragePath = path.join(process.cwd(), 'frontend', 'coverage', 'coverage-summary.json');
    
    if (fs.existsSync(coveragePath)) {
      const coverage = JSON.parse(fs.readFileSync(coveragePath, 'utf8'));
      
      console.log('');
      console.log('📊 Coverage Summary:');
      console.log(`   Lines: ${coverage.total.lines.pct}%`);
      console.log(`   Functions: ${coverage.total.functions.pct}%`);
      console.log(`   Branches: ${coverage.total.branches.pct}%`);
      console.log(`   Statements: ${coverage.total.statements.pct}%`);
      
      // Check if coverage meets threshold
      const meetsThreshold = [
        coverage.total.lines.pct,
        coverage.total.functions.pct,
        coverage.total.branches.pct,
        coverage.total.statements.pct
      ].every(pct => pct >= TEST_CONFIG.coverageThreshold);
      
      if (meetsThreshold) {
        console.log(`✅ Coverage meets ${TEST_CONFIG.coverageThreshold}% threshold`);
      } else {
        console.log(`⚠️  Coverage below ${TEST_CONFIG.coverageThreshold}% threshold`);
      }
    }

    // Generate HTML report link
    const htmlReportPath = path.join(process.cwd(), 'frontend', 'coverage', 'lcov-report', 'index.html');
    if (fs.existsSync(htmlReportPath)) {
      console.log(`🌐 HTML report: file://${htmlReportPath}`);
    }

  } catch (error) {
    console.warn('⚠️  Could not generate detailed report:', error.message);
  }
}

/**
 * Validate environment
 */
function validateEnvironment() {
  const frontendDir = path.join(__dirname, '..', 'frontend');
  
  if (!fs.existsSync(frontendDir)) {
    console.error('❌ Frontend directory not found');
    process.exit(1);
  }

  const packageJsonPath = path.join(frontendDir, 'package.json');
  if (!fs.existsSync(packageJsonPath)) {
    console.error('❌ Frontend package.json not found');
    process.exit(1);
  }

  const jestConfigPath = path.join(frontendDir, 'jest.config.js');
  if (!fs.existsSync(jestConfigPath)) {
    console.error('❌ Jest configuration not found');
    process.exit(1);
  }
}

/**
 * Main function
 */
function main() {
  console.log('🔐 Payment System Test Runner');
  console.log('============================');
  console.log('');

  // Validate environment
  validateEnvironment();

  // Parse arguments
  const options = parseArgs();

  // Run tests
  const success = runTests(options);

  // Generate report
  generateReport(options);

  // Exit with appropriate code
  process.exit(success ? 0 : 1);
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  runTests,
  buildJestArgs,
  TEST_SUITES,
  TEST_CONFIG
};
