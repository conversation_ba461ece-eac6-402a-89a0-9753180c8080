#!/usr/bin/env node

/**
 * Production Environment Validation Script
 * 
 * Comprehensive validation of all environment variables and configuration
 * required for production deployment of the PI Lawyer AI payment system.
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// Colors for output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

// Logging functions
const log = {
  info: (msg) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`),
  critical: (msg) => console.log(`${colors.red}${colors.bold}[CRITICAL]${colors.reset} ${msg}`)
};

// Environment variable categories and requirements
const ENV_REQUIREMENTS = {
  // Core Application
  core: {
    required: [
      'NODE_ENV',
      'NEXT_PUBLIC_APP_VERSION',
      'NEXT_PUBLIC_SUPABASE_URL',
      'NEXT_PUBLIC_SUPABASE_ANON_KEY',
      'SUPABASE_SERVICE_KEY',
      'SUPABASE_JWT_SECRET'
    ],
    optional: [
      'PORT',
      'HOST'
    ]
  },
  
  // Payment System
  payment: {
    required: [
      'STRIPE_SECRET_KEY',
      'STRIPE_PUBLISHABLE_KEY',
      'STRIPE_WEBHOOK_SECRET',
      'PAYMENT_API_KEY',
      'PAYMENT_ENCRYPTION_KEY',
      'PAYMENT_MASTER_KEY'
    ],
    optional: [
      'STRIPE_CONNECT_CLIENT_ID'
    ]
  },
  
  // Security & Authentication
  security: {
    required: [
      'JWT_SECRET',
      'ENCRYPTION_KEY',
      'SESSION_SECRET'
    ],
    optional: [
      'CSRF_SECRET',
      'RATE_LIMIT_SECRET'
    ]
  },
  
  // Monitoring & Alerting
  monitoring: {
    required: [
      'NEXT_PUBLIC_SENTRY_DSN',
      'SENTRY_AUTH_TOKEN'
    ],
    optional: [
      'METRICS_API_KEY',
      'PROMETHEUS_ENDPOINT',
      'GRAFANA_API_KEY',
      'SLACK_WEBHOOK_URL',
      'PAGERDUTY_PAYMENT_KEY',
      'SMTP_PASSWORD'
    ]
  },
  
  // External Services
  external: {
    required: [
      'NEXT_PUBLIC_LAWS_API_BASE',
      'NEXT_PUBLIC_BACKEND_API_URL'
    ],
    optional: [
      'REDIS_URL',
      'REDIS_PASSWORD',
      'OPENAI_API_KEY',
      'PINECONE_API_KEY'
    ]
  }
};

// Security validation patterns
const SECURITY_PATTERNS = {
  stripe_secret: /^sk_(test_|live_)[a-zA-Z0-9]{24,}$/,
  stripe_publishable: /^pk_(test_|live_)[a-zA-Z0-9]{24,}$/,
  stripe_webhook: /^whsec_[a-zA-Z0-9+/=]{32,}$/,
  supabase_url: /^https:\/\/[a-z0-9]+\.supabase\.co$/,
  supabase_key: /^eyJ[a-zA-Z0-9+/=]+\.[a-zA-Z0-9+/=]+\.[a-zA-Z0-9+/=_-]+$/,
  sentry_dsn: /^https:\/\/[a-f0-9]+@[a-f0-9]+\.ingest\.sentry\.io\/[0-9]+$/,
  encryption_key: /^[a-f0-9]{64}$/,
  jwt_secret: /^[a-zA-Z0-9+/=]{32,}$/
};

// Production environment checks
const PRODUCTION_CHECKS = {
  node_env: (value) => value === 'production',
  stripe_live_keys: (env) => {
    const secretKey = env.STRIPE_SECRET_KEY;
    const publishableKey = env.STRIPE_PUBLISHABLE_KEY;
    return secretKey?.startsWith('sk_live_') && publishableKey?.startsWith('pk_live_');
  },
  secure_protocols: (env) => {
    const supabaseUrl = env.NEXT_PUBLIC_SUPABASE_URL;
    const backendUrl = env.NEXT_PUBLIC_BACKEND_API_URL;
    return supabaseUrl?.startsWith('https://') && backendUrl?.startsWith('https://');
  },
  strong_secrets: (env) => {
    const secrets = [env.JWT_SECRET, env.ENCRYPTION_KEY, env.SESSION_SECRET];
    return secrets.every(secret => secret && secret.length >= 32);
  }
};

class EnvironmentValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.info = [];
    this.env = process.env;
  }

  /**
   * Load environment variables from .env files
   */
  loadEnvironment() {
    const envFiles = ['.env.local', '.env.production', '.env'];
    
    for (const envFile of envFiles) {
      const envPath = path.join(process.cwd(), envFile);
      if (fs.existsSync(envPath)) {
        log.info(`Loading environment from ${envFile}`);
        require('dotenv').config({ path: envPath });
        break;
      }
    }
  }

  /**
   * Validate required environment variables
   */
  validateRequiredVariables() {
    log.info('Validating required environment variables...');
    
    for (const [category, config] of Object.entries(ENV_REQUIREMENTS)) {
      const missing = config.required.filter(varName => !this.env[varName]);
      
      if (missing.length > 0) {
        this.errors.push({
          category: 'missing_required',
          message: `Missing required ${category} variables: ${missing.join(', ')}`
        });
      }
      
      const missingOptional = config.optional.filter(varName => !this.env[varName]);
      if (missingOptional.length > 0) {
        this.warnings.push({
          category: 'missing_optional',
          message: `Missing optional ${category} variables: ${missingOptional.join(', ')}`
        });
      }
    }
  }

  /**
   * Validate security patterns and formats
   */
  validateSecurityPatterns() {
    log.info('Validating security patterns...');
    
    const validations = [
      { var: 'STRIPE_SECRET_KEY', pattern: 'stripe_secret' },
      { var: 'STRIPE_PUBLISHABLE_KEY', pattern: 'stripe_publishable' },
      { var: 'STRIPE_WEBHOOK_SECRET', pattern: 'stripe_webhook' },
      { var: 'NEXT_PUBLIC_SUPABASE_URL', pattern: 'supabase_url' },
      { var: 'NEXT_PUBLIC_SUPABASE_ANON_KEY', pattern: 'supabase_key' },
      { var: 'SUPABASE_SERVICE_KEY', pattern: 'supabase_key' },
      { var: 'NEXT_PUBLIC_SENTRY_DSN', pattern: 'sentry_dsn' },
      { var: 'PAYMENT_ENCRYPTION_KEY', pattern: 'encryption_key' },
      { var: 'SUPABASE_JWT_SECRET', pattern: 'jwt_secret' }
    ];

    for (const { var: varName, pattern } of validations) {
      const value = this.env[varName];
      if (value && !SECURITY_PATTERNS[pattern].test(value)) {
        this.errors.push({
          category: 'invalid_format',
          message: `Invalid format for ${varName}`
        });
      }
    }
  }

  /**
   * Validate production-specific requirements
   */
  validateProductionRequirements() {
    log.info('Validating production requirements...');
    
    if (!PRODUCTION_CHECKS.node_env(this.env.NODE_ENV)) {
      this.warnings.push({
        category: 'environment',
        message: 'NODE_ENV is not set to "production"'
      });
    }

    if (!PRODUCTION_CHECKS.stripe_live_keys(this.env)) {
      this.warnings.push({
        category: 'payment',
        message: 'Using Stripe test keys in production environment'
      });
    }

    if (!PRODUCTION_CHECKS.secure_protocols(this.env)) {
      this.errors.push({
        category: 'security',
        message: 'External URLs must use HTTPS in production'
      });
    }

    if (!PRODUCTION_CHECKS.strong_secrets(this.env)) {
      this.errors.push({
        category: 'security',
        message: 'Secrets must be at least 32 characters long'
      });
    }
  }

  /**
   * Validate payment system configuration
   */
  validatePaymentConfiguration() {
    log.info('Validating payment system configuration...');
    
    // Check payment encryption keys
    const encryptionKey = this.env.PAYMENT_ENCRYPTION_KEY;
    const masterKey = this.env.PAYMENT_MASTER_KEY;
    
    if (encryptionKey && encryptionKey.length !== 64) {
      this.errors.push({
        category: 'payment_security',
        message: 'PAYMENT_ENCRYPTION_KEY must be exactly 64 hex characters'
      });
    }
    
    if (masterKey && masterKey.length < 32) {
      this.errors.push({
        category: 'payment_security',
        message: 'PAYMENT_MASTER_KEY must be at least 32 characters'
      });
    }

    // Check Stripe webhook configuration
    if (this.env.STRIPE_WEBHOOK_SECRET && !this.env.STRIPE_WEBHOOK_SECRET.startsWith('whsec_')) {
      this.errors.push({
        category: 'payment_webhook',
        message: 'STRIPE_WEBHOOK_SECRET must start with "whsec_"'
      });
    }
  }

  /**
   * Validate monitoring configuration
   */
  validateMonitoringConfiguration() {
    log.info('Validating monitoring configuration...');
    
    // Check Sentry configuration
    if (!this.env.NEXT_PUBLIC_SENTRY_DSN) {
      this.warnings.push({
        category: 'monitoring',
        message: 'Sentry DSN not configured - error tracking disabled'
      });
    }

    // Check alerting configuration
    const alertingVars = ['SLACK_WEBHOOK_URL', 'PAGERDUTY_PAYMENT_KEY', 'SMTP_PASSWORD'];
    const missingAlerting = alertingVars.filter(varName => !this.env[varName]);
    
    if (missingAlerting.length === alertingVars.length) {
      this.warnings.push({
        category: 'alerting',
        message: 'No alerting channels configured (Slack, PagerDuty, Email)'
      });
    }
  }

  /**
   * Generate security recommendations
   */
  generateSecurityRecommendations() {
    const recommendations = [];
    
    // Check for weak secrets
    const secrets = ['JWT_SECRET', 'ENCRYPTION_KEY', 'SESSION_SECRET', 'PAYMENT_MASTER_KEY'];
    for (const secret of secrets) {
      const value = this.env[secret];
      if (value && value.length < 64) {
        recommendations.push(`Increase ${secret} length to at least 64 characters for better security`);
      }
    }

    // Check for test keys in production
    if (this.env.NODE_ENV === 'production') {
      if (this.env.STRIPE_SECRET_KEY?.startsWith('sk_test_')) {
        recommendations.push('Replace Stripe test keys with live keys for production');
      }
    }

    return recommendations;
  }

  /**
   * Run all validations
   */
  async validate() {
    log.info('Starting production environment validation...');
    
    this.loadEnvironment();
    this.validateRequiredVariables();
    this.validateSecurityPatterns();
    this.validateProductionRequirements();
    this.validatePaymentConfiguration();
    this.validateMonitoringConfiguration();
    
    return this.generateReport();
  }

  /**
   * Generate validation report
   */
  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      environment: this.env.NODE_ENV || 'unknown',
      errors: this.errors,
      warnings: this.warnings,
      info: this.info,
      recommendations: this.generateSecurityRecommendations(),
      summary: {
        total_errors: this.errors.length,
        total_warnings: this.warnings.length,
        validation_passed: this.errors.length === 0
      }
    };

    this.printReport(report);
    return report;
  }

  /**
   * Print validation report
   */
  printReport(report) {
    console.log('\n' + '='.repeat(60));
    console.log(`${colors.bold}PRODUCTION ENVIRONMENT VALIDATION REPORT${colors.reset}`);
    console.log('='.repeat(60));
    
    console.log(`Environment: ${colors.cyan}${report.environment}${colors.reset}`);
    console.log(`Timestamp: ${colors.cyan}${report.timestamp}${colors.reset}`);
    console.log(`Validation Status: ${report.summary.validation_passed ? 
      `${colors.green}PASSED${colors.reset}` : 
      `${colors.red}FAILED${colors.reset}`}`);
    
    if (report.errors.length > 0) {
      console.log(`\n${colors.red}${colors.bold}ERRORS (${report.errors.length}):${colors.reset}`);
      report.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. [${error.category}] ${error.message}`);
      });
    }
    
    if (report.warnings.length > 0) {
      console.log(`\n${colors.yellow}${colors.bold}WARNINGS (${report.warnings.length}):${colors.reset}`);
      report.warnings.forEach((warning, index) => {
        console.log(`  ${index + 1}. [${warning.category}] ${warning.message}`);
      });
    }
    
    if (report.recommendations.length > 0) {
      console.log(`\n${colors.blue}${colors.bold}RECOMMENDATIONS:${colors.reset}`);
      report.recommendations.forEach((rec, index) => {
        console.log(`  ${index + 1}. ${rec}`);
      });
    }
    
    console.log('\n' + '='.repeat(60));
    
    if (report.summary.validation_passed) {
      log.success('Environment validation passed! Ready for production deployment.');
    } else {
      log.critical(`Environment validation failed with ${report.errors.length} errors. Fix these before deploying.`);
    }
  }
}

// Main execution
async function main() {
  const validator = new EnvironmentValidator();
  const report = await validator.validate();
  
  // Exit with error code if validation failed
  process.exit(report.summary.validation_passed ? 0 : 1);
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { EnvironmentValidator, ENV_REQUIREMENTS, SECURITY_PATTERNS };
