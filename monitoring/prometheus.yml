# Prometheus Configuration for PI Lawyer AI Payment Monitoring
# This configuration sets up scraping for payment metrics and alerting rules

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'pi-lawyer-ai'
    environment: 'production'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'
rule_files:
  - "payment_alerts.yml"
  - "system_alerts.yml"

# Scrape configuration
scrape_configs:
  # PI Lawyer AI Application Metrics
  - job_name: 'pi-lawyer-ai-app'
    scrape_interval: 30s
    scrape_timeout: 10s
    metrics_path: '/api/metrics'
    static_configs:
      - targets: ['localhost:3000']  # Adjust for your deployment
    basic_auth:
      username: 'prometheus'
      password: 'your-metrics-password'  # Set in environment
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: localhost:3000

  # Node Exporter for system metrics
  - job_name: 'node-exporter'
    scrape_interval: 30s
    static_configs:
      - targets: ['localhost:9100']
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        regex: '([^:]+)(:[0-9]+)?'
        replacement: '${1}'

  # Prometheus itself
  - job_name: 'prometheus'
    scrape_interval: 30s
    static_configs:
      - targets: ['localhost:9090']

  # Alertmanager
  - job_name: 'alertmanager'
    scrape_interval: 30s
    static_configs:
      - targets: ['localhost:9093']

  # Grafana
  - job_name: 'grafana'
    scrape_interval: 30s
    static_configs:
      - targets: ['localhost:3001']  # Adjust for your Grafana port

  # Redis (if using Redis exporter)
  - job_name: 'redis'
    scrape_interval: 30s
    static_configs:
      - targets: ['localhost:9121']

  # PostgreSQL (if using postgres exporter)
  - job_name: 'postgres'
    scrape_interval: 30s
    static_configs:
      - targets: ['localhost:9187']

# Storage configuration
storage:
  tsdb:
    retention.time: 30d
    retention.size: 10GB
