# Alertmanager Configuration for PI Lawyer AI Payment Alerting
# Comprehensive alerting with multiple channels and escalation procedures

global:
  # SMTP configuration for email alerts
  smtp_smarthost: 'smtp.gmail.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: '${SMTP_PASSWORD}'
  smtp_require_tls: true

# Templates for alert formatting
templates:
  - '/etc/alertmanager/templates/*.tmpl'

# Routing configuration
route:
  group_by: ['alertname', 'service', 'severity']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default-receiver'
  
  routes:
    # Critical payment alerts - immediate notification
    - match:
        severity: critical
        service: payment
      receiver: 'payment-critical'
      group_wait: 0s
      repeat_interval: 5m
      continue: true
    
    # Payment SLA breaches - escalate to management
    - match:
        service: sla
      receiver: 'sla-breach'
      group_wait: 30s
      repeat_interval: 15m
      continue: true
    
    # Compliance violations - notify compliance team
    - match:
        service: compliance
      receiver: 'compliance-team'
      group_wait: 1m
      repeat_interval: 30m
      continue: true
    
    # Business alerts - notify business team
    - match:
        service: business
      receiver: 'business-team'
      group_wait: 5m
      repeat_interval: 2h
    
    # Warning level alerts
    - match:
        severity: warning
      receiver: 'warning-alerts'
      group_wait: 2m
      repeat_interval: 30m
    
    # Info level alerts
    - match:
        severity: info
      receiver: 'info-alerts'
      group_wait: 5m
      repeat_interval: 4h

# Inhibition rules to prevent alert spam
inhibit_rules:
  # Inhibit warning alerts if critical alerts are firing
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['service', 'alertname']
  
  # Inhibit info alerts if warning or critical alerts are firing
  - source_match:
      severity: 'warning'
    target_match:
      severity: 'info'
    equal: ['service', 'alertname']

# Receiver configurations
receivers:
  # Default receiver for unmatched alerts
  - name: 'default-receiver'
    email_configs:
      - to: '<EMAIL>'
        subject: '[PI Lawyer AI] Alert: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Labels: {{ range .Labels.SortedPairs }}{{ .Name }}={{ .Value }} {{ end }}
          {{ end }}

  # Critical payment alerts - multiple channels
  - name: 'payment-critical'
    email_configs:
      - to: '<EMAIL>,<EMAIL>,<EMAIL>'
        subject: '[CRITICAL] Payment System Alert: {{ .GroupLabels.alertname }}'
        body: |
          🚨 CRITICAL PAYMENT ALERT 🚨
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Severity: {{ .Labels.severity }}
          Service: {{ .Labels.service }}
          Time: {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          
          Labels: {{ range .Labels.SortedPairs }}{{ .Name }}={{ .Value }} {{ end }}
          {{ end }}
          
          Please investigate immediately and follow the incident response procedure.
    
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#payment-alerts'
        title: '🚨 Critical Payment Alert'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Severity:* {{ .Labels.severity }}
          *Time:* {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          {{ end }}
        send_resolved: true
    
    pagerduty_configs:
      - routing_key: '${PAGERDUTY_PAYMENT_KEY}'
        description: 'Critical Payment Alert: {{ .GroupLabels.alertname }}'
        severity: 'critical'
        details:
          summary: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
          source: 'PI Lawyer AI Payment System'

  # SLA breach alerts - escalate to management
  - name: 'sla-breach'
    email_configs:
      - to: '<EMAIL>,<EMAIL>'
        subject: '[SLA BREACH] Payment SLA Violation: {{ .GroupLabels.alertname }}'
        body: |
          ⚠️ SLA BREACH DETECTED ⚠️
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Time: {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          {{ end }}
          
          This requires immediate attention to maintain service quality commitments.
    
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#sla-monitoring'
        title: '⚠️ SLA Breach Alert'
        text: |
          {{ range .Alerts }}
          *SLA Breach:* {{ .Annotations.summary }}
          *Details:* {{ .Annotations.description }}
          *Time:* {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          {{ end }}

  # Compliance team alerts
  - name: 'compliance-team'
    email_configs:
      - to: '<EMAIL>,<EMAIL>'
        subject: '[COMPLIANCE] Payment Compliance Alert: {{ .GroupLabels.alertname }}'
        body: |
          🔒 COMPLIANCE ALERT 🔒
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Framework: {{ .Labels.framework }}
          Violation Type: {{ .Labels.violation_type }}
          Severity: {{ .Labels.severity }}
          Time: {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          {{ end }}
          
          Please review and ensure compliance requirements are met.

  # Business team alerts
  - name: 'business-team'
    email_configs:
      - to: '<EMAIL>,<EMAIL>'
        subject: '[BUSINESS] Payment Business Alert: {{ .GroupLabels.alertname }}'
        body: |
          📊 BUSINESS ALERT 📊
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Time: {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          {{ end }}
          
          This may impact business metrics and requires review.

  # Warning level alerts
  - name: 'warning-alerts'
    email_configs:
      - to: '<EMAIL>,<EMAIL>'
        subject: '[WARNING] Payment Warning: {{ .GroupLabels.alertname }}'
        body: |
          ⚠️ WARNING ALERT ⚠️
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Time: {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          {{ end }}
    
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#payment-warnings'
        title: '⚠️ Payment Warning'
        text: |
          {{ range .Alerts }}
          *Warning:* {{ .Annotations.summary }}
          *Details:* {{ .Annotations.description }}
          {{ end }}

  # Info level alerts
  - name: 'info-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[INFO] Payment Info: {{ .GroupLabels.alertname }}'
        body: |
          ℹ️ INFORMATION ALERT ℹ️
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Time: {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          {{ end }}
    
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#payment-info'
        title: 'ℹ️ Payment Information'
        text: |
          {{ range .Alerts }}
          *Info:* {{ .Annotations.summary }}
          *Details:* {{ .Annotations.description }}
          {{ end }}
