{{/* Payment Alert Templates for Alertmanager */}}

{{/* Critical Payment Alert Template */}}
{{ define "payment.critical.title" }}
🚨 CRITICAL: {{ .GroupLabels.alertname }} - PI Lawyer AI Payment System
{{ end }}

{{ define "payment.critical.body" }}
**CRITICAL PAYMENT SYSTEM ALERT**

{{ range .Alerts }}
**Alert:** {{ .Annotations.summary }}
**Description:** {{ .Annotations.description }}
**Severity:** {{ .Labels.severity | toUpper }}
**Service:** {{ .Labels.service }}
**Started:** {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
{{ if .Labels.payment_method }}**Payment Method:** {{ .Labels.payment_method }}{{ end }}
{{ if .Labels.region }}**Region:** {{ .Labels.region }}{{ end }}
{{ if .Labels.error_code }}**Error Code:** {{ .Labels.error_code }}{{ end }}
{{ if .Labels.tenant_id }}**Tenant ID:** {{ .Labels.tenant_id }}{{ end }}

**Runbook:** https://docs.pilawyer.ai/runbooks/payment-{{ .Labels.alertname | toLower }}
**Dashboard:** https://grafana.pilawyer.ai/d/payment-ops/payment-operations
**Logs:** https://logs.pilawyer.ai/app/discover#/?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-15m,to:now))&_a=(columns:!(message),filters:!(('$state':(store:appState),meta:(alias:!n,disabled:!f,index:'logstash-*',key:service,negate:!f,params:(query:payment),type:phrase),query:(match:(service:(query:payment,type:phrase))))),index:'logstash-*',interval:auto,query:(language:kuery,query:''),sort:!(!('@timestamp',desc)))

{{ end }}

**IMMEDIATE ACTION REQUIRED**
1. Check the payment system dashboard
2. Review recent deployments
3. Check Stripe status page
4. Follow incident response procedure
5. Update #payment-alerts channel with status

{{ if .Alerts.Resolved }}
**RESOLVED:** This alert has been resolved at {{ .Alerts.Resolved.ResolvedAt.Format "2006-01-02 15:04:05 UTC" }}
{{ end }}
{{ end }}

{{/* SLA Breach Alert Template */}}
{{ define "sla.breach.title" }}
⚠️ SLA BREACH: {{ .GroupLabels.alertname }} - PI Lawyer AI
{{ end }}

{{ define "sla.breach.body" }}
**PAYMENT SLA BREACH DETECTED**

{{ range .Alerts }}
**Alert:** {{ .Annotations.summary }}
**Description:** {{ .Annotations.description }}
**Started:** {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
{{ if .Labels.payment_method }}**Payment Method:** {{ .Labels.payment_method }}{{ end }}
{{ if .Labels.region }}**Region:** {{ .Labels.region }}{{ end }}

**SLA Impact:**
- **Availability Target:** 99.5%
- **Latency Target:** 3s P95
- **Current Performance:** {{ .Annotations.description }}

{{ end }}

**ESCALATION REQUIRED**
This breach affects our service level commitments and requires immediate management attention.

**Next Steps:**
1. Assess customer impact
2. Implement immediate mitigation
3. Prepare customer communication
4. Schedule post-incident review
{{ end }}

{{/* Compliance Alert Template */}}
{{ define "compliance.violation.title" }}
🔒 COMPLIANCE: {{ .GroupLabels.alertname }} - PI Lawyer AI
{{ end }}

{{ define "compliance.violation.body" }}
**PAYMENT COMPLIANCE VIOLATION**

{{ range .Alerts }}
**Alert:** {{ .Annotations.summary }}
**Description:** {{ .Annotations.description }}
**Framework:** {{ .Labels.framework }}
**Violation Type:** {{ .Labels.violation_type }}
**Severity:** {{ .Labels.severity | toUpper }}
**Region:** {{ .Labels.region }}
**Started:** {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}

{{ end }}

**COMPLIANCE REVIEW REQUIRED**
This violation may impact our regulatory compliance and requires immediate review.

**Action Items:**
1. Review compliance framework requirements
2. Assess violation impact and scope
3. Implement corrective measures
4. Document remediation steps
5. Update compliance monitoring
{{ end }}

{{/* Business Alert Template */}}
{{ define "business.alert.title" }}
📊 BUSINESS: {{ .GroupLabels.alertname }} - PI Lawyer AI
{{ end }}

{{ define "business.alert.body" }}
**PAYMENT BUSINESS ALERT**

{{ range .Alerts }}
**Alert:** {{ .Annotations.summary }}
**Description:** {{ .Annotations.description }}
**Started:** {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
{{ if .Labels.payment_method }}**Payment Method:** {{ .Labels.payment_method }}{{ end }}
{{ if .Labels.region }}**Region:** {{ .Labels.region }}{{ end }}

{{ end }}

**BUSINESS IMPACT REVIEW**
This alert indicates a change in payment patterns that may affect business metrics.

**Recommended Actions:**
1. Review payment analytics dashboard
2. Check for external factors (holidays, campaigns, etc.)
3. Assess customer communication needs
4. Monitor for continued trends
{{ end }}

{{/* Slack Alert Template */}}
{{ define "slack.payment.critical" }}
{
  "text": "🚨 *CRITICAL PAYMENT ALERT*",
  "attachments": [
    {
      "color": "danger",
      "fields": [
        {{ range .Alerts }}
        {
          "title": "{{ .Annotations.summary }}",
          "value": "{{ .Annotations.description }}",
          "short": false
        },
        {
          "title": "Details",
          "value": "Service: {{ .Labels.service }} | Severity: {{ .Labels.severity }}{{ if .Labels.payment_method }} | Method: {{ .Labels.payment_method }}{{ end }}{{ if .Labels.region }} | Region: {{ .Labels.region }}{{ end }}",
          "short": false
        },
        {
          "title": "Time",
          "value": "{{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}",
          "short": true
        }
        {{ end }}
      ],
      "actions": [
        {
          "type": "button",
          "text": "View Dashboard",
          "url": "https://grafana.pilawyer.ai/d/payment-ops/payment-operations"
        },
        {
          "type": "button",
          "text": "View Logs",
          "url": "https://logs.pilawyer.ai/app/discover"
        },
        {
          "type": "button",
          "text": "Runbook",
          "url": "https://docs.pilawyer.ai/runbooks/payment-alerts"
        }
      ]
    }
  ]
}
{{ end }}

{{/* Email Subject Templates */}}
{{ define "email.subject.critical" }}
[CRITICAL] {{ .GroupLabels.alertname }} - PI Lawyer AI Payment System
{{ end }}

{{ define "email.subject.warning" }}
[WARNING] {{ .GroupLabels.alertname }} - PI Lawyer AI Payment System
{{ end }}

{{ define "email.subject.info" }}
[INFO] {{ .GroupLabels.alertname }} - PI Lawyer AI Payment System
{{ end }}

{{/* Common Footer Template */}}
{{ define "alert.footer" }}
---
PI Lawyer AI Payment Monitoring System
Generated: {{ now.Format "2006-01-02 15:04:05 UTC" }}
Dashboard: https://grafana.pilawyer.ai/d/payment-ops/payment-operations
Runbooks: https://docs.pilawyer.ai/runbooks/
{{ end }}
