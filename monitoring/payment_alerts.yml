# Payment Operations Alerting Rules for Prometheus
# These rules define alerts for payment system monitoring

groups:
  - name: payment_critical_alerts
    rules:
      - alert: PaymentProcessingDown
        expr: rate(payment_processing_total[5m]) == 0
        for: 2m
        labels:
          severity: critical
          service: payment
        annotations:
          summary: "Payment processing has stopped"
          description: "No payment processing activity detected for 2 minutes"

      - alert: PaymentSuccessRateLow
        expr: (rate(payment_processing_total{status="success"}[5m]) / rate(payment_processing_total[5m])) * 100 < 95
        for: 5m
        labels:
          severity: critical
          service: payment
        annotations:
          summary: "Payment success rate is below 95%"
          description: "Payment success rate is {{ $value }}% for the last 5 minutes"

      - alert: PaymentMethodCreationFailureSpike
        expr: rate(payment_method_creation_total{status="failure"}[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
          service: payment
        annotations:
          summary: "High payment method creation failure rate"
          description: "Payment method creation failure rate is {{ $value }} failures/sec"

      - alert: PaymentErrorRateHigh
        expr: rate(payment_errors_total[5m]) > 0.05
        for: 3m
        labels:
          severity: critical
          service: payment
        annotations:
          summary: "High payment error rate detected"
          description: "Payment error rate is {{ $value }} errors/sec for {{ $labels.error_code }}"

  - name: payment_warning_alerts
    rules:
      - alert: PaymentProcessingLatencyHigh
        expr: histogram_quantile(0.95, rate(payment_processing_duration_seconds_bucket[5m])) > 10
        for: 5m
        labels:
          severity: warning
          service: payment
        annotations:
          summary: "Payment processing latency is high"
          description: "95th percentile payment processing latency is {{ $value }}s"

      - alert: PaymentMethodCreationLatencyHigh
        expr: histogram_quantile(0.95, rate(payment_method_creation_duration_seconds_bucket[5m])) > 5
        for: 5m
        labels:
          severity: warning
          service: payment
        annotations:
          summary: "Payment method creation latency is high"
          description: "95th percentile payment method creation latency is {{ $value }}s"

      - alert: ComplianceViolationDetected
        expr: rate(payment_compliance_violations_total[5m]) > 0
        for: 1m
        labels:
          severity: warning
          service: compliance
        annotations:
          summary: "Payment compliance violation detected"
          description: "Compliance violation: {{ $labels.violation_type }} in {{ $labels.framework }}"

      - alert: WebhookProcessingFailures
        expr: rate(payment_webhook_processing_total{status="failure"}[5m]) > 0.01
        for: 3m
        labels:
          severity: warning
          service: webhook
        annotations:
          summary: "Webhook processing failures detected"
          description: "Webhook processing failure rate is {{ $value }} failures/sec for {{ $labels.event_type }}"

  - name: payment_info_alerts
    rules:
      - alert: PaymentVolumeSpike
        expr: rate(payment_volume_cents[5m]) > 100000  # $1000/sec
        for: 2m
        labels:
          severity: info
          service: payment
        annotations:
          summary: "High payment volume detected"
          description: "Payment volume is {{ $value }} cents/sec"

      - alert: NewPaymentMethodRegion
        expr: increase(payment_method_creation_total[1h]) > 0 and on(region) (count by (region) (payment_method_creation_total) == 1)
        for: 0m
        labels:
          severity: info
          service: payment
        annotations:
          summary: "New payment method region detected"
          description: "First payment method created in region {{ $labels.region }}"

  - name: payment_business_alerts
    rules:
      - alert: PaymentVolumeDropSignificant
        expr: rate(payment_volume_cents[5m]) < rate(payment_volume_cents[1h] offset 1h) * 0.5
        for: 10m
        labels:
          severity: warning
          service: business
        annotations:
          summary: "Significant drop in payment volume"
          description: "Payment volume has dropped by more than 50% compared to the same time yesterday"

      - alert: PaymentMethodMixChange
        expr: abs(rate(payment_method_creation_total{payment_method="card"}[1h]) / rate(payment_method_creation_total[1h]) - rate(payment_method_creation_total{payment_method="card"}[1h] offset 24h) / rate(payment_method_creation_total[1h] offset 24h)) > 0.2
        for: 30m
        labels:
          severity: info
          service: business
        annotations:
          summary: "Significant change in payment method mix"
          description: "Card payment method percentage has changed by more than 20% compared to yesterday"

  - name: payment_sla_alerts
    rules:
      - alert: PaymentSLABreach
        expr: (rate(payment_processing_total{status="success"}[5m]) / rate(payment_processing_total[5m])) * 100 < 99.5
        for: 1m
        labels:
          severity: critical
          service: sla
        annotations:
          summary: "Payment SLA breach - 99.5% availability"
          description: "Payment success rate {{ $value }}% is below SLA threshold of 99.5%"

      - alert: PaymentLatencySLABreach
        expr: histogram_quantile(0.95, rate(payment_processing_duration_seconds_bucket[5m])) > 3
        for: 2m
        labels:
          severity: warning
          service: sla
        annotations:
          summary: "Payment latency SLA breach - 3s P95"
          description: "95th percentile payment latency {{ $value }}s exceeds SLA threshold of 3s"
