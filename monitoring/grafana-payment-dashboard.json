{"dashboard": {"id": null, "title": "PI Lawyer AI - Payment Operations Dashboard", "tags": ["payment", "monitoring", "pi-lawyer-ai"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "Payment Method Creation Rate", "type": "stat", "targets": [{"expr": "rate(payment_method_creation_total[5m])", "legendFormat": "{{payment_method}} - {{region}}"}], "fieldConfig": {"defaults": {"unit": "reqps", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "Payment Processing Rate", "type": "stat", "targets": [{"expr": "rate(payment_processing_total[5m])", "legendFormat": "{{payment_method}} - {{region}}"}], "fieldConfig": {"defaults": {"unit": "reqps", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}}, {"id": 3, "title": "Payment Success Rate", "type": "stat", "targets": [{"expr": "rate(payment_processing_total{status=\"success\"}[5m]) / rate(payment_processing_total[5m]) * 100", "legendFormat": "Success Rate"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "max": 100, "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 95}, {"color": "green", "value": 99}]}}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}}, {"id": 4, "title": "Payment Volume (Last Hour)", "type": "stat", "targets": [{"expr": "increase(payment_volume_cents[1h]) / 100", "legendFormat": "{{currency}}"}], "fieldConfig": {"defaults": {"unit": "currencyUSD", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}}, {"id": 5, "title": "Payment Method Creation Duration", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, rate(payment_method_creation_duration_seconds_bucket[5m]))", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.50, rate(payment_method_creation_duration_seconds_bucket[5m]))", "legendFormat": "50th percentile"}], "yAxes": [{"unit": "s", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 6, "title": "Payment Processing Duration", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, rate(payment_processing_duration_seconds_bucket[5m]))", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.50, rate(payment_processing_duration_seconds_bucket[5m]))", "legendFormat": "50th percentile"}], "yAxes": [{"unit": "s", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 7, "title": "Payment Errors by Type", "type": "graph", "targets": [{"expr": "rate(payment_errors_total[5m])", "legendFormat": "{{error_code}} - {{payment_method}}"}], "yAxes": [{"unit": "reqps", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 8, "title": "Compliance Violations", "type": "graph", "targets": [{"expr": "rate(payment_compliance_violations_total[5m])", "legendFormat": "{{framework}} - {{violation_type}}"}], "yAxes": [{"unit": "reqps", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 9, "title": "Payment Methods by Region", "type": "piechart", "targets": [{"expr": "sum by (region) (rate(payment_method_creation_total[5m]))", "legendFormat": "{{region}}"}], "gridPos": {"h": 8, "w": 8, "x": 0, "y": 24}}, {"id": 10, "title": "Payment Methods by Type", "type": "piechart", "targets": [{"expr": "sum by (payment_method) (rate(payment_method_creation_total[5m]))", "legendFormat": "{{payment_method}}"}], "gridPos": {"h": 8, "w": 8, "x": 8, "y": 24}}, {"id": 11, "title": "Webhook Processing Rate", "type": "graph", "targets": [{"expr": "rate(payment_webhook_processing_total[5m])", "legendFormat": "{{event_type}} - {{status}}"}], "yAxes": [{"unit": "reqps", "min": 0}], "gridPos": {"h": 8, "w": 8, "x": 16, "y": 24}}], "templating": {"list": [{"name": "region", "type": "query", "query": "label_values(payment_method_creation_total, region)", "refresh": 1, "includeAll": true, "allValue": ".*"}, {"name": "payment_method", "type": "query", "query": "label_values(payment_method_creation_total, payment_method)", "refresh": 1, "includeAll": true, "allValue": ".*"}, {"name": "tenant_id", "type": "query", "query": "label_values(payment_method_creation_total, tenant_id)", "refresh": 1, "includeAll": true, "allValue": ".*"}]}, "annotations": {"list": [{"name": "Deployments", "datasource": "prometheus", "expr": "pi_lawyer_ai_build_info", "titleFormat": "Deployment", "textFormat": "Version: {{version}}"}]}}}