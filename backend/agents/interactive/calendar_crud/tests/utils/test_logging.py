"""
Tests for the logging utilities.
"""

import logging
import os
from unittest.mock import MagicMock, patch

from backend.agents.interactive.calendar_crud.utils.logging import (
    get_logger,
    log_security_event,
)


def test_get_logger():
    """Test the get_logger function."""
    # Test with default log level (ensure LOG_LEVEL is not set)
    with patch.dict(os.environ, {}, clear=False):
        # Remove LOG_LEVEL if it exists to test default behavior
        if "LOG_LEVEL" in os.environ:
            del os.environ["LOG_LEVEL"]
        logger = get_logger("test_logger")
        assert logger.name == "test_logger"
        assert logger.level == logging.INFO
    assert len(logger.handlers) == 1
    assert isinstance(logger.handlers[0], logging.StreamHandler)

    # Test with custom log level
    with patch.dict(os.environ, {"LOG_LEVEL": "DEBUG"}):
        logger = get_logger("test_logger_debug")
        assert logger.name == "test_logger_debug"
        assert logger.level == logging.DEBUG
        assert len(logger.handlers) == 1
        assert isinstance(logger.handlers[0], logging.StreamHandler)

    # Test with existing logger (should not add another handler)
    existing_logger = logging.getLogger("existing_logger")
    handler = logging.StreamHandler()
    existing_logger.addHandler(handler)

    logger = get_logger("existing_logger")
    assert logger.name == "existing_logger"
    assert len(logger.handlers) == 1  # Should not add another handler


def test_log_security_event_success():
    """Test the log_security_event function with success=True."""
    # Mock the logger
    mock_logger = MagicMock()

    with patch(
        "backend.agents.interactive.calendar_crud.utils.logging.get_logger",
        return_value=mock_logger,
    ):
        # Call the function
        log_security_event(
            event_type="authentication",
            user_id="test-user",
            firm_id="test-firm",
            details={"ip": "127.0.0.1"},
            success=True,
            source="test",
        )

        # Check that the logger.info was called with the correct arguments
        mock_logger.info.assert_called_once()
        args, kwargs = mock_logger.info.call_args
        assert args[0] == "Security event: authentication"
        assert "event" in kwargs["extra"]
        event_data = kwargs["extra"]["event"]
        assert event_data["event_type"] == "authentication"
        assert event_data["user_id"] == "test-user"
        assert event_data["firm_id"] == "test-firm"
        assert event_data["details"] == {"ip": "127.0.0.1"}
        assert event_data["success"] is True
        assert event_data["source"] == "test"
        assert "timestamp" in event_data


def test_log_security_event_failure():
    """Test the log_security_event function with success=False."""
    # Mock the logger
    mock_logger = MagicMock()

    with patch(
        "backend.agents.interactive.calendar_crud.utils.logging.get_logger",
        return_value=mock_logger,
    ):
        # Call the function
        log_security_event(
            event_type="authorization",
            user_id="test-user",
            firm_id="test-firm",
            details={"resource": "calendar"},
            success=False,
            source="test",
        )

        # Check that the logger.warning was called with the correct arguments
        mock_logger.warning.assert_called_once()
        args, kwargs = mock_logger.warning.call_args
        assert args[0] == "Security event failure: authorization"
        assert "event" in kwargs["extra"]
        event_data = kwargs["extra"]["event"]
        assert event_data["event_type"] == "authorization"
        assert event_data["user_id"] == "test-user"
        assert event_data["firm_id"] == "test-firm"
        assert event_data["details"] == {"resource": "calendar"}
        assert event_data["success"] is False
        assert event_data["source"] == "test"
        assert "timestamp" in event_data


def test_log_security_event_default_values():
    """Test the log_security_event function with default values."""
    # Mock the logger
    mock_logger = MagicMock()

    with patch(
        "backend.agents.interactive.calendar_crud.utils.logging.get_logger",
        return_value=mock_logger,
    ):
        # Call the function with minimal arguments
        log_security_event(event_type="test_event")

        # Check that the logger.info was called with the correct arguments
        mock_logger.info.assert_called_once()
        args, kwargs = mock_logger.info.call_args
        assert args[0] == "Security event: test_event"
        assert "event" in kwargs["extra"]
        event_data = kwargs["extra"]["event"]
        assert event_data["event_type"] == "test_event"
        assert event_data["user_id"] is None
        assert event_data["firm_id"] is None
        assert event_data["details"] == {}
        assert event_data["success"] is True
        assert event_data["source"] == "calendar_crud"
        assert "timestamp" in event_data
