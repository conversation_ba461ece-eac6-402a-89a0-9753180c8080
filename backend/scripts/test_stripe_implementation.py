#!/usr/bin/env python3
"""
Stripe Implementation Test Suite

Comprehensive test script to verify all implemented Stripe functionality
including checkout, webhooks, rate limiting, and security measures.
"""

import asyncio
import json
import time
import requests
from datetime import datetime
from typing import Dict, Any, List

# Add the project root to the Python path
import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.utils.logging import get_logger

logger = get_logger(__name__)


class StripeImplementationTester:
    """Test suite for Stripe implementation."""

    def __init__(self, base_url: str = "http://localhost:3000"):
        self.base_url = base_url
        self.test_results = {
            "timestamp": datetime.now().isoformat(),
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0,
            "test_details": [],
            "implementation_score": 0,
            "critical_issues": [],
            "recommendations": [],
        }

    def run_comprehensive_tests(self) -> Dict[str, Any]:
        """Run comprehensive Stripe implementation tests."""
        logger.info("🚀 Starting Stripe implementation test suite...")

        try:
            # Test 1: Checkout API Endpoint
            self._test_checkout_endpoint()

            # Test 2: Session Verification API
            self._test_session_verification()

            # Test 3: Rate Limiting API
            self._test_rate_limiting_api()

            # Test 4: Frontend Rate Limiting
            self._test_frontend_rate_limiting()

            # Test 5: Success/Cancel Pages
            self._test_success_cancel_pages()

            # Test 6: Error Handling
            self._test_error_handling()

            # Calculate implementation score
            self._calculate_implementation_score()

            # Generate recommendations
            self._generate_recommendations()

            return self.test_results

        except Exception as e:
            logger.error(f"Critical error in implementation tests: {e}", exc_info=True)
            self.test_results["critical_issues"].append(f"Test execution failed: {e}")
            return self.test_results

    def _test_checkout_endpoint(self):
        """Test the checkout API endpoint."""
        test_name = "Checkout API Endpoint"
        logger.info(f"Testing {test_name}...")

        try:
            # Test missing price_id parameter
            response = requests.get(f"{self.base_url}/api/billing/checkout")

            if response.status_code == 400:
                self._record_test_success(
                    test_name, "Correctly rejects missing price_id"
                )
            else:
                self._record_test_failure(
                    test_name, f"Expected 400, got {response.status_code}"
                )

            # Test with price_id parameter (will fail auth, but endpoint should exist)
            response = requests.get(
                f"{self.base_url}/api/billing/checkout?price_id=price_test_123"
            )

            if response.status_code in [401, 403]:
                self._record_test_success(
                    test_name, "Endpoint exists and requires authentication"
                )
            elif response.status_code == 404:
                self._record_test_failure(test_name, "Checkout endpoint not found")
            else:
                self._record_test_success(
                    test_name, f"Endpoint responds (status: {response.status_code})"
                )

        except requests.exceptions.ConnectionError:
            self._record_test_failure(
                test_name, "Cannot connect to application - is it running?"
            )
        except Exception as e:
            self._record_test_failure(test_name, f"Unexpected error: {e}")

    def _test_session_verification(self):
        """Test the session verification API endpoint."""
        test_name = "Session Verification API"
        logger.info(f"Testing {test_name}...")

        try:
            # Test missing session_id parameter
            response = requests.get(f"{self.base_url}/api/billing/verify-session")

            if response.status_code == 400:
                self._record_test_success(
                    test_name, "Correctly rejects missing session_id"
                )
            else:
                self._record_test_failure(
                    test_name, f"Expected 400, got {response.status_code}"
                )

            # Test with session_id parameter
            response = requests.get(
                f"{self.base_url}/api/billing/verify-session?session_id=cs_test_123"
            )

            if response.status_code in [401, 403]:
                self._record_test_success(
                    test_name, "Endpoint exists and requires authentication"
                )
            elif response.status_code == 404:
                self._record_test_failure(test_name, "Verification endpoint not found")
            else:
                self._record_test_success(
                    test_name, f"Endpoint responds (status: {response.status_code})"
                )

        except requests.exceptions.ConnectionError:
            self._record_test_failure(test_name, "Cannot connect to application")
        except Exception as e:
            self._record_test_failure(test_name, f"Unexpected error: {e}")

    def _test_rate_limiting_api(self):
        """Test the rate limiting API endpoint."""
        test_name = "Rate Limiting API"
        logger.info(f"Testing {test_name}...")

        try:
            # Test missing operation parameter
            response = requests.get(f"{self.base_url}/api/rate-limit/status")

            if response.status_code == 400:
                self._record_test_success(
                    test_name, "Correctly rejects missing operation"
                )
            else:
                self._record_test_failure(
                    test_name, f"Expected 400, got {response.status_code}"
                )

            # Test with valid operation
            response = requests.get(
                f"{self.base_url}/api/rate-limit/status?operation=checkout"
            )

            if response.status_code in [200, 401, 403]:
                self._record_test_success(test_name, "Rate limiting endpoint exists")
            elif response.status_code == 404:
                self._record_test_failure(test_name, "Rate limiting endpoint not found")
            else:
                self._record_test_success(
                    test_name, f"Endpoint responds (status: {response.status_code})"
                )

        except requests.exceptions.ConnectionError:
            self._record_test_failure(test_name, "Cannot connect to application")
        except Exception as e:
            self._record_test_failure(test_name, f"Unexpected error: {e}")

    def _test_frontend_rate_limiting(self):
        """Test frontend rate limiting functionality."""
        test_name = "Frontend Rate Limiting"
        logger.info(f"Testing {test_name}...")

        try:
            # Check if rate limiting file exists and is properly structured
            rate_limit_file = os.path.join(
                os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                "frontend",
                "src",
                "lib",
                "rate-limit.ts",
            )

            if os.path.exists(rate_limit_file):
                with open(rate_limit_file, "r") as f:
                    content = f.read()

                # Check for key functions and improvements
                if "checkPaymentRateLimit" in content:
                    self._record_test_success(
                        test_name, "Payment rate limiting function exists"
                    )
                else:
                    self._record_test_failure(
                        test_name, "Payment rate limiting function missing"
                    )

                if "RateLimitResult" in content:
                    self._record_test_success(
                        test_name, "Rate limit result interface exists"
                    )
                else:
                    self._record_test_failure(
                        test_name, "Rate limit result interface missing"
                    )
            else:
                self._record_test_failure(test_name, "Rate limiting file not found")

        except Exception as e:
            self._record_test_failure(test_name, f"Error checking rate limiting: {e}")

    def _test_success_cancel_pages(self):
        """Test success and cancel page endpoints."""
        test_name = "Success/Cancel Pages"
        logger.info(f"Testing {test_name}...")

        try:
            # Test success page
            response = requests.get(f"{self.base_url}/subscription/success")

            if response.status_code in [200, 401, 403]:
                self._record_test_success(test_name, "Success page exists")
            elif response.status_code == 404:
                self._record_test_failure(test_name, "Success page not found")
            else:
                self._record_test_success(
                    test_name, f"Success page responds (status: {response.status_code})"
                )

            # Test cancel page
            response = requests.get(f"{self.base_url}/subscription/cancel")

            if response.status_code in [200, 401, 403]:
                self._record_test_success(test_name, "Cancel page exists")
            elif response.status_code == 404:
                self._record_test_failure(test_name, "Cancel page not found")
            else:
                self._record_test_success(
                    test_name, f"Cancel page responds (status: {response.status_code})"
                )

        except requests.exceptions.ConnectionError:
            self._record_test_failure(test_name, "Cannot connect to application")
        except Exception as e:
            self._record_test_failure(test_name, f"Unexpected error: {e}")

    def _test_error_handling(self):
        """Test error handling in various scenarios."""
        test_name = "Error Handling"
        logger.info(f"Testing {test_name}...")

        try:
            # Test invalid price_id
            response = requests.get(
                f"{self.base_url}/api/billing/checkout?price_id=invalid_price"
            )

            if response.status_code in [400, 401, 403, 404]:
                self._record_test_success(
                    test_name, "Handles invalid price_id appropriately"
                )
            else:
                self._record_test_failure(
                    test_name,
                    f"Unexpected response to invalid price_id: {response.status_code}",
                )

            # Test invalid session_id
            response = requests.get(
                f"{self.base_url}/api/billing/verify-session?session_id=invalid_session"
            )

            if response.status_code in [400, 401, 403, 404]:
                self._record_test_success(
                    test_name, "Handles invalid session_id appropriately"
                )
            else:
                self._record_test_failure(
                    test_name,
                    f"Unexpected response to invalid session_id: {response.status_code}",
                )

        except requests.exceptions.ConnectionError:
            self._record_test_failure(test_name, "Cannot connect to application")
        except Exception as e:
            self._record_test_failure(test_name, f"Unexpected error: {e}")

    def _record_test_success(self, test_name: str, message: str):
        """Record a successful test."""
        self.test_results["total_tests"] += 1
        self.test_results["passed_tests"] += 1
        self.test_results["test_details"].append(
            {"test": test_name, "status": "PASS", "message": message}
        )
        logger.info(f"✅ {test_name}: {message}")

    def _record_test_failure(self, test_name: str, message: str):
        """Record a failed test."""
        self.test_results["total_tests"] += 1
        self.test_results["failed_tests"] += 1
        self.test_results["test_details"].append(
            {"test": test_name, "status": "FAIL", "message": message}
        )
        self.test_results["critical_issues"].append(f"{test_name}: {message}")
        logger.error(f"❌ {test_name}: {message}")

    def _calculate_implementation_score(self):
        """Calculate overall implementation score."""
        if self.test_results["total_tests"] == 0:
            self.test_results["implementation_score"] = 0
            return

        pass_rate = self.test_results["passed_tests"] / self.test_results["total_tests"]
        self.test_results["implementation_score"] = round(pass_rate * 100, 1)

    def _generate_recommendations(self):
        """Generate implementation recommendations."""
        recommendations = []

        if self.test_results["implementation_score"] >= 90:
            recommendations.append(
                "✅ EXCELLENT: Stripe implementation is comprehensive and functional"
            )
        elif self.test_results["implementation_score"] >= 80:
            recommendations.append(
                "✅ GOOD: Stripe implementation is solid with minor issues"
            )
        elif self.test_results["implementation_score"] >= 70:
            recommendations.append(
                "⚠️ FAIR: Stripe implementation needs some improvements"
            )
        else:
            recommendations.append(
                "❌ POOR: Stripe implementation has significant issues"
            )

        if self.test_results["failed_tests"] > 0:
            recommendations.append(
                f"🔧 Fix {self.test_results['failed_tests']} failed test(s)"
            )

        if len(self.test_results["critical_issues"]) > 0:
            recommendations.append(
                "🚨 Address critical issues before production deployment"
            )

        self.test_results["recommendations"] = recommendations


def main():
    """Main function to run Stripe implementation tests."""
    tester = StripeImplementationTester()
    results = tester.run_comprehensive_tests()

    print("\n" + "=" * 60)
    print("STRIPE IMPLEMENTATION TEST RESULTS")
    print("=" * 60)
    print(f"Implementation Score: {results['implementation_score']}/100")
    print(f"Tests Passed: {results['passed_tests']}/{results['total_tests']}")

    if results["critical_issues"]:
        print(f"\nCritical Issues: {len(results['critical_issues'])}")
        for issue in results["critical_issues"]:
            print(f"  - {issue}")

    print(f"\nRecommendations: {len(results['recommendations'])}")
    for rec in results["recommendations"]:
        print(f"  - {rec}")

    print("=" * 60)

    # Save detailed results
    with open("stripe_implementation_test_results.json", "w") as f:
        json.dump(results, f, indent=2)

    print(f"\nDetailed results saved to: stripe_implementation_test_results.json")

    # Exit with appropriate code
    if results["implementation_score"] >= 80:
        sys.exit(0)
    else:
        sys.exit(1)


if __name__ == "__main__":
    main()
