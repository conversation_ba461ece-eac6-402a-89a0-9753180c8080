#!/usr/bin/env python3
"""
Webhook Security Test Runner

Executes comprehensive webhook security tests and generates a detailed report
on the security posture of the webhook implementation.
"""

import subprocess
import sys
import json
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from utils.logging import get_logger

logger = get_logger(__name__)


class WebhookSecurityTestRunner:
    """Runner for webhook security tests."""

    def __init__(self):
        self.test_results = {
            "timestamp": datetime.now().isoformat(),
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0,
            "skipped_tests": 0,
            "test_categories": {},
            "security_score": 0,
            "critical_issues": [],
            "recommendations": [],
        }

    def run_security_tests(self) -> Dict[str, Any]:
        """Run all webhook security tests."""
        logger.info("🔒 Starting webhook security test suite...")

        try:
            # Run pytest with specific test file
            test_file = project_root / "tests" / "test_webhook_security.py"

            if not test_file.exists():
                raise FileNotFoundError(f"Test file not found: {test_file}")

            # Execute tests with detailed output
            cmd = [
                sys.executable,
                "-m",
                "pytest",
                str(test_file),
                "-v",
                "--tb=short",
                "--json-report",
                "--json-report-file=webhook_security_test_results.json",
            ]

            logger.info(f"Running command: {' '.join(cmd)}")

            result = subprocess.run(
                cmd,
                cwd=project_root,
                capture_output=True,
                text=True,
                timeout=300,  # 5 minute timeout
            )

            # Parse results
            self._parse_test_results(result)

            # Load JSON report if available
            json_report_path = project_root / "webhook_security_test_results.json"
            if json_report_path.exists():
                self._parse_json_report(json_report_path)

            # Calculate security score
            self._calculate_security_score()

            # Generate recommendations
            self._generate_recommendations()

            # Save detailed report
            self._save_detailed_report()

            return self.test_results

        except subprocess.TimeoutExpired:
            logger.error("Webhook security tests timed out")
            self.test_results["critical_issues"].append("Test execution timed out")
            return self.test_results

        except Exception as e:
            logger.error(f"Error running webhook security tests: {e}", exc_info=True)
            self.test_results["critical_issues"].append(f"Test execution failed: {e}")
            return self.test_results

    def _parse_test_results(self, result: subprocess.CompletedProcess):
        """Parse test results from subprocess output."""
        self.test_results["return_code"] = result.returncode
        self.test_results["stdout"] = result.stdout
        self.test_results["stderr"] = result.stderr

        # Parse basic statistics from output
        output_lines = result.stdout.split("\n")

        for line in output_lines:
            if "passed" in line and "failed" in line:
                # Parse pytest summary line
                parts = line.split()
                for i, part in enumerate(parts):
                    if part == "passed":
                        self.test_results["passed_tests"] = int(parts[i - 1])
                    elif part == "failed":
                        self.test_results["failed_tests"] = int(parts[i - 1])
                    elif part == "skipped":
                        self.test_results["skipped_tests"] = int(parts[i - 1])

        self.test_results["total_tests"] = (
            self.test_results["passed_tests"]
            + self.test_results["failed_tests"]
            + self.test_results["skipped_tests"]
        )

    def _parse_json_report(self, json_path: Path):
        """Parse detailed JSON test report."""
        try:
            with open(json_path, "r") as f:
                json_data = json.load(f)

            # Extract test categories
            for test in json_data.get("tests", []):
                test_name = test.get("nodeid", "")
                outcome = test.get("outcome", "unknown")

                # Categorize tests
                if "TestWebhookSecurity" in test_name:
                    category = "basic_security"
                elif "TestWebhookSignatureVerification" in test_name:
                    category = "signature_verification"
                elif "TestWebhookReplayAttackPrevention" in test_name:
                    category = "replay_prevention"
                else:
                    category = "other"

                if category not in self.test_results["test_categories"]:
                    self.test_results["test_categories"][category] = {
                        "passed": 0,
                        "failed": 0,
                        "skipped": 0,
                    }

                self.test_results["test_categories"][category][outcome] += 1

                # Track critical failures
                if outcome == "failed" and any(
                    keyword in test_name.lower()
                    for keyword in ["signature", "replay", "security", "validation"]
                ):
                    self.test_results["critical_issues"].append(
                        f"Critical security test failed: {test_name}"
                    )

        except Exception as e:
            logger.warning(f"Could not parse JSON report: {e}")

    def _calculate_security_score(self):
        """Calculate overall security score based on test results."""
        if self.test_results["total_tests"] == 0:
            self.test_results["security_score"] = 0
            return

        # Base score from pass rate
        pass_rate = self.test_results["passed_tests"] / self.test_results["total_tests"]
        base_score = pass_rate * 100

        # Deduct points for critical issues
        critical_deduction = len(self.test_results["critical_issues"]) * 10

        # Bonus for comprehensive coverage
        category_bonus = len(self.test_results["test_categories"]) * 2

        final_score = max(0, min(100, base_score - critical_deduction + category_bonus))
        self.test_results["security_score"] = round(final_score, 1)

    def _generate_recommendations(self):
        """Generate security recommendations based on test results."""
        recommendations = []

        # Check for failed signature verification tests
        sig_category = self.test_results["test_categories"].get(
            "signature_verification", {}
        )
        if sig_category.get("failed", 0) > 0:
            recommendations.append(
                "🔴 CRITICAL: Fix webhook signature verification implementation"
            )

        # Check for failed replay prevention tests
        replay_category = self.test_results["test_categories"].get(
            "replay_prevention", {}
        )
        if replay_category.get("failed", 0) > 0:
            recommendations.append(
                "🟡 HIGH: Implement proper replay attack prevention mechanisms"
            )

        # Check overall security score
        if self.test_results["security_score"] < 80:
            recommendations.append(
                "🟡 MEDIUM: Security score below 80% - review failed tests"
            )

        # Check for missing test categories
        expected_categories = [
            "basic_security",
            "signature_verification",
            "replay_prevention",
        ]
        missing_categories = [
            cat
            for cat in expected_categories
            if cat not in self.test_results["test_categories"]
        ]

        if missing_categories:
            recommendations.append(
                f"🟡 MEDIUM: Missing test coverage for: {', '.join(missing_categories)}"
            )

        # Add general recommendations
        if self.test_results["security_score"] >= 90:
            recommendations.append(
                "✅ EXCELLENT: Webhook security implementation is robust"
            )
        elif self.test_results["security_score"] >= 80:
            recommendations.append(
                "✅ GOOD: Webhook security is adequate with minor improvements needed"
            )
        else:
            recommendations.append(
                "❌ POOR: Webhook security needs significant improvements"
            )

        self.test_results["recommendations"] = recommendations

    def _save_detailed_report(self):
        """Save detailed security test report."""
        report_path = project_root / "webhook_security_report.json"

        try:
            with open(report_path, "w") as f:
                json.dump(self.test_results, f, indent=2)

            logger.info(f"Detailed security report saved to: {report_path}")

            # Also create a markdown summary
            self._create_markdown_summary()

        except Exception as e:
            logger.error(f"Failed to save security report: {e}")

    def _create_markdown_summary(self):
        """Create a markdown summary of security test results."""
        summary_path = project_root / "webhook_security_summary.md"

        try:
            with open(summary_path, "w") as f:
                f.write("# Webhook Security Test Summary\n\n")
                f.write(f"**Generated:** {self.test_results['timestamp']}\n\n")
                f.write(
                    f"**Security Score:** {self.test_results['security_score']}/100\n\n"
                )

                f.write("## Test Results\n\n")
                f.write(f"- **Total Tests:** {self.test_results['total_tests']}\n")
                f.write(f"- **Passed:** {self.test_results['passed_tests']}\n")
                f.write(f"- **Failed:** {self.test_results['failed_tests']}\n")
                f.write(f"- **Skipped:** {self.test_results['skipped_tests']}\n\n")

                if self.test_results["test_categories"]:
                    f.write("## Test Categories\n\n")
                    for category, results in self.test_results[
                        "test_categories"
                    ].items():
                        f.write(f"### {category.replace('_', ' ').title()}\n")
                        f.write(f"- Passed: {results.get('passed', 0)}\n")
                        f.write(f"- Failed: {results.get('failed', 0)}\n")
                        f.write(f"- Skipped: {results.get('skipped', 0)}\n\n")

                if self.test_results["critical_issues"]:
                    f.write("## Critical Issues\n\n")
                    for issue in self.test_results["critical_issues"]:
                        f.write(f"- {issue}\n")
                    f.write("\n")

                if self.test_results["recommendations"]:
                    f.write("## Recommendations\n\n")
                    for rec in self.test_results["recommendations"]:
                        f.write(f"- {rec}\n")
                    f.write("\n")

            logger.info(f"Security summary saved to: {summary_path}")

        except Exception as e:
            logger.error(f"Failed to create markdown summary: {e}")


def main():
    """Main function to run webhook security tests."""
    runner = WebhookSecurityTestRunner()
    results = runner.run_security_tests()

    print("\n" + "=" * 60)
    print("WEBHOOK SECURITY TEST RESULTS")
    print("=" * 60)
    print(f"Security Score: {results['security_score']}/100")
    print(f"Tests Passed: {results['passed_tests']}/{results['total_tests']}")

    if results["critical_issues"]:
        print(f"\nCritical Issues: {len(results['critical_issues'])}")
        for issue in results["critical_issues"]:
            print(f"  - {issue}")

    print(f"\nRecommendations: {len(results['recommendations'])}")
    for rec in results["recommendations"]:
        print(f"  - {rec}")

    print("=" * 60)

    # Exit with error code if security score is too low
    if results["security_score"] < 80:
        sys.exit(1)
    else:
        sys.exit(0)


if __name__ == "__main__":
    main()
