"""
Database Health Check API Routes

Provides comprehensive database connection pool monitoring and health check endpoints
for production monitoring and debugging of database connectivity issues.
"""

import logging
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel

from backend.api.dependencies.auth import get_current_user, UserContext, require_role
from backend.db.session import (
    check_database_health,
    get_connection_pool_stats,
    pool_monitor,
    engine,
    POOL_SIZE,
    MAX_OVERFLOW,
    POOL_TIMEOUT,
    POOL_RECYCLE,
    ENVIRONMENT,
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/database", tags=["Database Health"])


# =====================================================
# Pydantic Models
# =====================================================


class DatabaseHealthResponse(BaseModel):
    """Database health check response model."""

    status: str
    connection_time_ms: float
    database_url: str
    pool_status: Dict[str, Any]
    environment: str
    timestamp: float
    error: str = None


class ConnectionPoolStatsResponse(BaseModel):
    """Connection pool statistics response model."""

    configuration: Dict[str, Any]
    current_status: Dict[str, Any]
    health_indicators: Dict[str, Any]
    recommendations: list


class DatabaseConfigResponse(BaseModel):
    """Database configuration response model."""

    pool_size: int
    max_overflow: int
    pool_timeout: int
    pool_recycle: int
    environment: str
    engine_info: Dict[str, Any]


# =====================================================
# API Endpoints
# =====================================================


@router.get("/health", response_model=DatabaseHealthResponse)
async def get_database_health():
    """
    Get database connection health status.

    This endpoint checks database connectivity and returns pool statistics.
    Available to all authenticated users for basic health monitoring.
    """
    try:
        health_data = await check_database_health()

        logger.info(
            f"Database health check completed",
            extra={
                "status": health_data["status"],
                "connection_time_ms": health_data.get("connection_time_ms", 0),
                "environment": ENVIRONMENT,
            },
        )

        return DatabaseHealthResponse(**health_data)

    except Exception as e:
        logger.error(
            f"Database health check failed",
            extra={"error": str(e), "environment": ENVIRONMENT},
            exc_info=True,
        )
        raise HTTPException(
            status_code=500, detail=f"Database health check failed: {str(e)}"
        )


@router.get("/pool/stats", response_model=ConnectionPoolStatsResponse)
async def get_pool_statistics(
    current_user: UserContext = Depends(require_role(["superadmin", "admin"]))
):
    """
    Get detailed connection pool statistics.

    This endpoint provides comprehensive pool metrics and recommendations.
    Requires admin or superadmin role for security.
    """
    try:
        pool_stats = await get_connection_pool_stats()

        logger.info(
            f"Connection pool statistics retrieved by user {current_user.id}",
            extra={
                "user_id": str(current_user.id),
                "pool_utilization": pool_stats["health_indicators"]["pool_utilization"],
                "success_rate": pool_stats["health_indicators"][
                    "connection_success_rate"
                ],
            },
        )

        return ConnectionPoolStatsResponse(**pool_stats)

    except Exception as e:
        logger.error(
            f"Failed to get pool statistics",
            extra={"error": str(e), "user_id": str(current_user.id)},
            exc_info=True,
        )
        raise HTTPException(
            status_code=500, detail=f"Failed to retrieve pool statistics: {str(e)}"
        )


@router.get("/config", response_model=DatabaseConfigResponse)
async def get_database_config(
    current_user: UserContext = Depends(require_role(["superadmin"])),
):
    """
    Get database configuration details.

    This endpoint provides database configuration information.
    Requires superadmin role for security.
    """
    try:
        # Get engine information
        engine_info = {
            "driver": str(engine.dialect.driver),
            "dialect": str(engine.dialect.name),
            "pool_class": str(type(engine.pool).__name__),
            "echo": engine.echo,
            "url_database": engine.url.database,
            "url_host": engine.url.host,
            "url_port": engine.url.port,
        }

        config_data = {
            "pool_size": POOL_SIZE,
            "max_overflow": MAX_OVERFLOW,
            "pool_timeout": POOL_TIMEOUT,
            "pool_recycle": POOL_RECYCLE,
            "environment": ENVIRONMENT,
            "engine_info": engine_info,
        }

        logger.info(
            f"Database configuration retrieved by user {current_user.id}",
            extra={"user_id": str(current_user.id), "environment": ENVIRONMENT},
        )

        return DatabaseConfigResponse(**config_data)

    except Exception as e:
        logger.error(
            f"Failed to get database configuration",
            extra={"error": str(e), "user_id": str(current_user.id)},
            exc_info=True,
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve database configuration: {str(e)}",
        )


@router.get("/pool/status")
async def get_pool_status(
    current_user: UserContext = Depends(require_role(["superadmin", "admin"]))
):
    """
    Get real-time connection pool status.

    This endpoint provides current pool status without detailed statistics.
    Requires admin or superadmin role.
    """
    try:
        pool_status = pool_monitor.get_pool_status()

        # Add timestamp and environment info
        response_data = {
            **pool_status,
            "timestamp": datetime.utcnow().isoformat(),
            "environment": ENVIRONMENT,
            "configuration": {
                "pool_size": POOL_SIZE,
                "max_overflow": MAX_OVERFLOW,
                "pool_timeout": POOL_TIMEOUT,
                "pool_recycle": POOL_RECYCLE,
            },
        }

        logger.debug(
            f"Pool status retrieved by user {current_user.id}",
            extra={
                "user_id": str(current_user.id),
                "checked_out": pool_status["checked_out"],
                "pool_size": pool_status["pool_size"],
            },
        )

        return response_data

    except Exception as e:
        logger.error(
            f"Failed to get pool status",
            extra={"error": str(e), "user_id": str(current_user.id)},
            exc_info=True,
        )
        raise HTTPException(
            status_code=500, detail=f"Failed to retrieve pool status: {str(e)}"
        )


@router.post("/pool/reset")
async def reset_pool_statistics(
    current_user: UserContext = Depends(require_role(["superadmin"])),
):
    """
    Reset connection pool statistics.

    This endpoint resets the pool monitoring statistics.
    Requires superadmin role for security.
    """
    try:
        # Reset pool monitor statistics
        pool_monitor._total_connections = 0
        pool_monitor._failed_connections = 0
        pool_monitor._connection_times = []

        logger.info(
            f"Pool statistics reset by user {current_user.id}",
            extra={"user_id": str(current_user.id), "environment": ENVIRONMENT},
        )

        return {
            "message": "Pool statistics reset successfully",
            "timestamp": datetime.utcnow().isoformat(),
            "reset_by": str(current_user.id),
        }

    except Exception as e:
        logger.error(
            f"Failed to reset pool statistics",
            extra={"error": str(e), "user_id": str(current_user.id)},
            exc_info=True,
        )
        raise HTTPException(
            status_code=500, detail=f"Failed to reset pool statistics: {str(e)}"
        )


@router.get("/metrics")
async def get_database_metrics():
    """
    Get database metrics for monitoring systems.

    This endpoint provides metrics in a format suitable for monitoring systems.
    Available to all authenticated users.
    """
    try:
        health_data = await check_database_health()
        pool_status = pool_monitor.get_pool_status()

        metrics = {
            "database_healthy": 1 if health_data["status"] == "healthy" else 0,
            "connection_time_ms": health_data.get("connection_time_ms", 0),
            "pool_size": pool_status["pool_size"],
            "connections_checked_out": pool_status["checked_out"],
            "connections_checked_in": pool_status["checked_in"],
            "connections_overflow": pool_status["overflow"],
            "connections_invalid": pool_status["invalid"],
            "total_connections": pool_status["total_connections"],
            "failed_connections": pool_status["failed_connections"],
            "connection_success_rate": pool_status["success_rate"],
            "avg_connection_time_ms": round(
                pool_status["avg_connection_time"] * 1000, 2
            ),
            "pool_utilization_percent": (
                pool_status["checked_out"] / max(POOL_SIZE + MAX_OVERFLOW, 1) * 100
            ),
            "timestamp": datetime.utcnow().isoformat(),
            "environment": ENVIRONMENT,
        }

        return metrics

    except Exception as e:
        logger.error(
            f"Failed to get database metrics", extra={"error": str(e)}, exc_info=True
        )
        raise HTTPException(
            status_code=500, detail=f"Failed to retrieve database metrics: {str(e)}"
        )
