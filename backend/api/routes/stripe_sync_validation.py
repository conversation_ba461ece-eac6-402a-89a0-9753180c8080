"""
Stripe Sync Validation API Routes

Provides comprehensive API endpoints for validating data consistency between
local database and Stripe, with discrepancy detection and reconciliation capabilities.
"""

import logging
from datetime import datetime
from typing import Dict, Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from pydantic import BaseModel

from backend.api.dependencies.auth import get_current_user, UserContext, require_role
from backend.services.stripe_sync_validator import (
    stripe_sync_validator,
    SyncDiscrepancy,
    ValidationResult,
    DiscrepancyType,
    SeverityLevel,
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/stripe/sync", tags=["Stripe Sync Validation"])


# =====================================================
# Pydantic Models
# =====================================================


class DiscrepancyResponse(BaseModel):
    """Sync discrepancy response model."""

    type: str
    entity_type: str
    entity_id: str
    local_data: Optional[Dict[str, Any]]
    stripe_data: Optional[Dict[str, Any]]
    severity: str
    description: str
    detected_at: str
    field_differences: Optional[Dict[str, Any]] = None
    recommended_action: Optional[str] = None


class ValidationResultResponse(BaseModel):
    """Validation result response model."""

    entity_type: str
    total_local: int
    total_stripe: int
    matched_entities: int
    discrepancies_count: int
    discrepancies: List[DiscrepancyResponse]
    validation_time: str
    duration_seconds: float


class ComprehensiveValidationResponse(BaseModel):
    """Comprehensive validation response model."""

    products: ValidationResultResponse
    prices: ValidationResultResponse
    customers: ValidationResultResponse
    subscriptions: ValidationResultResponse
    summary: Dict[str, Any]
    validation_metadata: Dict[str, Any]


class ValidationSummaryResponse(BaseModel):
    """Validation summary response model."""

    overall_status: str
    total_discrepancies: int
    severity_breakdown: Dict[str, int]
    entity_summary: Dict[str, Dict[str, int]]
    recommendations: List[str]
    next_validation_recommended: str


# =====================================================
# API Endpoints
# =====================================================


@router.post("/validate/all", response_model=ComprehensiveValidationResponse)
async def run_comprehensive_validation(
    background_tasks: BackgroundTasks,
    current_user: UserContext = Depends(require_role(["superadmin", "admin"])),
):
    """
    Run comprehensive Stripe sync validation.

    This endpoint performs a complete validation of all data between
    local database and Stripe, including products, prices, customers, and subscriptions.

    Requires admin or superadmin role for security.
    """
    try:
        logger.info(
            f"Comprehensive Stripe sync validation initiated by user {current_user.id}",
            extra={"user_id": str(current_user.id), "user_email": current_user.email},
        )

        # Run validation
        validation_results = await stripe_sync_validator.validate_all_data()

        # Convert results to response models
        response_data = {
            "products": _convert_validation_result(validation_results["products"]),
            "prices": _convert_validation_result(validation_results["prices"]),
            "customers": _convert_validation_result(validation_results["customers"]),
            "subscriptions": _convert_validation_result(
                validation_results["subscriptions"]
            ),
            "summary": validation_results["summary"],
            "validation_metadata": validation_results["validation_metadata"],
        }

        logger.info(
            f"Comprehensive validation completed by user {current_user.id}",
            extra={
                "user_id": str(current_user.id),
                "total_discrepancies": validation_results["validation_metadata"][
                    "total_discrepancies"
                ],
                "critical_issues": validation_results["validation_metadata"][
                    "critical_issues"
                ],
                "duration_seconds": validation_results["validation_metadata"][
                    "duration_seconds"
                ],
            },
        )

        return ComprehensiveValidationResponse(**response_data)

    except Exception as e:
        logger.error(
            f"Comprehensive validation failed for user {current_user.id}",
            extra={"error": str(e), "user_id": str(current_user.id)},
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail=f"Validation failed: {str(e)}")


@router.post("/validate/products", response_model=ValidationResultResponse)
async def validate_products(
    current_user: UserContext = Depends(require_role(["superadmin", "admin"]))
):
    """
    Validate product data consistency between local database and Stripe.

    This endpoint specifically validates products (subscription plans and add-ons)
    and detects any discrepancies in product data.
    """
    try:
        logger.info(f"Product validation initiated by user {current_user.id}")

        validation_result = await stripe_sync_validator.validate_products()
        response_data = _convert_validation_result(validation_result)

        logger.info(
            f"Product validation completed by user {current_user.id}",
            extra={
                "user_id": str(current_user.id),
                "discrepancies_found": validation_result.discrepancies_count,
                "duration_seconds": validation_result.duration_seconds,
            },
        )

        return ValidationResultResponse(**response_data)

    except Exception as e:
        logger.error(
            f"Product validation failed for user {current_user.id}",
            extra={"error": str(e), "user_id": str(current_user.id)},
            exc_info=True,
        )
        raise HTTPException(
            status_code=500, detail=f"Product validation failed: {str(e)}"
        )


@router.post("/validate/prices", response_model=ValidationResultResponse)
async def validate_prices(
    current_user: UserContext = Depends(require_role(["superadmin", "admin"]))
):
    """
    Validate pricing data consistency between local database and Stripe.

    This endpoint validates price objects and detects pricing discrepancies
    that could affect billing accuracy.
    """
    try:
        logger.info(f"Price validation initiated by user {current_user.id}")

        validation_result = await stripe_sync_validator.validate_prices()
        response_data = _convert_validation_result(validation_result)

        logger.info(
            f"Price validation completed by user {current_user.id}",
            extra={
                "user_id": str(current_user.id),
                "discrepancies_found": validation_result.discrepancies_count,
                "duration_seconds": validation_result.duration_seconds,
            },
        )

        return ValidationResultResponse(**response_data)

    except Exception as e:
        logger.error(
            f"Price validation failed for user {current_user.id}",
            extra={"error": str(e), "user_id": str(current_user.id)},
            exc_info=True,
        )
        raise HTTPException(
            status_code=500, detail=f"Price validation failed: {str(e)}"
        )


@router.post("/validate/customers", response_model=ValidationResultResponse)
async def validate_customers(
    current_user: UserContext = Depends(require_role(["superadmin", "admin"]))
):
    """
    Validate customer data consistency between local database and Stripe.

    This endpoint validates customer records and detects discrepancies
    in customer information.
    """
    try:
        logger.info(f"Customer validation initiated by user {current_user.id}")

        validation_result = await stripe_sync_validator.validate_customers()
        response_data = _convert_validation_result(validation_result)

        logger.info(
            f"Customer validation completed by user {current_user.id}",
            extra={
                "user_id": str(current_user.id),
                "discrepancies_found": validation_result.discrepancies_count,
                "duration_seconds": validation_result.duration_seconds,
            },
        )

        return ValidationResultResponse(**response_data)

    except Exception as e:
        logger.error(
            f"Customer validation failed for user {current_user.id}",
            extra={"error": str(e), "user_id": str(current_user.id)},
            exc_info=True,
        )
        raise HTTPException(
            status_code=500, detail=f"Customer validation failed: {str(e)}"
        )


@router.post("/validate/subscriptions", response_model=ValidationResultResponse)
async def validate_subscriptions(
    current_user: UserContext = Depends(require_role(["superadmin", "admin"]))
):
    """
    Validate subscription data consistency between local database and Stripe.

    This endpoint validates subscription records and detects critical discrepancies
    that could affect billing and service delivery.
    """
    try:
        logger.info(f"Subscription validation initiated by user {current_user.id}")

        validation_result = await stripe_sync_validator.validate_subscriptions()
        response_data = _convert_validation_result(validation_result)

        logger.info(
            f"Subscription validation completed by user {current_user.id}",
            extra={
                "user_id": str(current_user.id),
                "discrepancies_found": validation_result.discrepancies_count,
                "duration_seconds": validation_result.duration_seconds,
            },
        )

        return ValidationResultResponse(**response_data)

    except Exception as e:
        logger.error(
            f"Subscription validation failed for user {current_user.id}",
            extra={"error": str(e), "user_id": str(current_user.id)},
            exc_info=True,
        )
        raise HTTPException(
            status_code=500, detail=f"Subscription validation failed: {str(e)}"
        )


@router.get("/status")
async def get_sync_status(
    current_user: UserContext = Depends(require_role(["superadmin", "admin"]))
):
    """
    Get current sync status and recent validation results.

    This endpoint provides a quick overview of the sync status
    without running a full validation.
    """
    try:
        # TODO: Implement sync status tracking
        # For now, return basic status information

        status_data = {
            "last_validation": None,  # TODO: Get from database
            "sync_health": "unknown",
            "pending_validations": 0,
            "recent_discrepancies": 0,
            "recommendations": ["Run comprehensive validation to check sync status"],
            "timestamp": datetime.utcnow().isoformat(),
        }

        logger.info(f"Sync status requested by user {current_user.id}")

        return status_data

    except Exception as e:
        logger.error(
            f"Failed to get sync status for user {current_user.id}",
            extra={"error": str(e), "user_id": str(current_user.id)},
            exc_info=True,
        )
        raise HTTPException(
            status_code=500, detail=f"Failed to get sync status: {str(e)}"
        )


# =====================================================
# Helper Functions
# =====================================================


def _convert_validation_result(validation_result: ValidationResult) -> Dict[str, Any]:
    """Convert ValidationResult to response dictionary."""
    return {
        "entity_type": validation_result.entity_type,
        "total_local": validation_result.total_local,
        "total_stripe": validation_result.total_stripe,
        "matched_entities": validation_result.matched_entities,
        "discrepancies_count": validation_result.discrepancies_count,
        "discrepancies": [
            _convert_discrepancy(d) for d in validation_result.discrepancies
        ],
        "validation_time": validation_result.validation_time.isoformat(),
        "duration_seconds": validation_result.duration_seconds,
    }


def _convert_discrepancy(discrepancy: SyncDiscrepancy) -> Dict[str, Any]:
    """Convert SyncDiscrepancy to response dictionary."""
    return {
        "type": discrepancy.type.value,
        "entity_type": discrepancy.entity_type,
        "entity_id": discrepancy.entity_id,
        "local_data": discrepancy.local_data,
        "stripe_data": discrepancy.stripe_data,
        "severity": discrepancy.severity.value,
        "description": discrepancy.description,
        "detected_at": discrepancy.detected_at.isoformat(),
        "field_differences": discrepancy.field_differences,
        "recommended_action": discrepancy.recommended_action,
    }
