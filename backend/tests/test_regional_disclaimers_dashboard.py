"""
Regional Disclaimers Dashboard Tests

Tests for the comprehensive regional disclaimers compliance dashboard
including API endpoints, data models, and frontend integration.
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock, AsyncMock
from fastapi.testclient import TestClient
from fastapi import FastAPI

from backend.api.routes.regional_disclaimers_dashboard import (
    router,
    DashboardMetrics,
    RegionalSummary,
    DisclaimerStats,
    AuditLogEntry,
    ComplianceReport,
)


class TestDashboardModels:
    """Test dashboard data models."""

    def test_dashboard_metrics_model(self):
        """Test DashboardMetrics model creation and validation."""
        metrics = DashboardMetrics(
            total_disclaimers=12,
            total_regions=2,
            total_views_today=156,
            total_acknowledgments_today=142,
            overall_compliance_rate=91.0,
            critical_issues=0,
            pending_reviews=2,
            last_updated=datetime.utcnow(),
        )

        assert metrics.total_disclaimers == 12
        assert metrics.total_regions == 2
        assert metrics.overall_compliance_rate == 91.0
        assert metrics.critical_issues == 0

    def test_regional_summary_model(self):
        """Test RegionalSummary model creation and validation."""
        summary = RegionalSummary(
            region="US",
            region_name="United States",
            total_disclaimers=7,
            required_disclaimers=5,
            optional_disclaimers=2,
            total_views=1250,
            total_acknowledgments=1142,
            overall_acknowledgment_rate=91.4,
            compliance_frameworks=["ABA Model Rules", "State Bar Rules"],
            last_updated=datetime.utcnow(),
        )

        assert summary.region == "US"
        assert summary.region_name == "United States"
        assert summary.total_disclaimers == 7
        assert summary.required_disclaimers == 5
        assert summary.optional_disclaimers == 2
        assert summary.overall_acknowledgment_rate == 91.4
        assert len(summary.compliance_frameworks) == 2

    def test_disclaimer_stats_model(self):
        """Test DisclaimerStats model creation and validation."""
        stats = DisclaimerStats(
            disclaimer_id="1",
            title="AI Assistant Disclaimer",
            disclaimer_type="no_legal_advice",
            region="US",
            placement=["modal", "footer"],
            is_required=True,
            total_views=450,
            total_acknowledgments=412,
            acknowledgment_rate=91.6,
            last_viewed=datetime.utcnow() - timedelta(minutes=15),
            last_acknowledged=datetime.utcnow() - timedelta(minutes=20),
        )

        assert stats.disclaimer_id == "1"
        assert stats.title == "AI Assistant Disclaimer"
        assert stats.disclaimer_type == "no_legal_advice"
        assert stats.region == "US"
        assert stats.placement == ["modal", "footer"]
        assert stats.is_required == True
        assert stats.total_views == 450
        assert stats.total_acknowledgments == 412
        assert stats.acknowledgment_rate == 91.6

    def test_audit_log_entry_model(self):
        """Test AuditLogEntry model creation and validation."""
        entry = AuditLogEntry(
            id="log_1",
            event_type="disclaimer_viewed",
            disclaimer_id="1",
            disclaimer_title="AI Assistant Disclaimer",
            user_id="user_123",
            region="US",
            placement="modal",
            practice_area="personal_injury",
            jurisdiction="california",
            event_timestamp=datetime.utcnow(),
            ip_address_hash="hash_abc123",
            request_path="/dashboard",
            metadata={"user_agent": "Chrome", "session_id": "sess_456"},
        )

        assert entry.id == "log_1"
        assert entry.event_type == "disclaimer_viewed"
        assert entry.disclaimer_id == "1"
        assert entry.disclaimer_title == "AI Assistant Disclaimer"
        assert entry.user_id == "user_123"
        assert entry.region == "US"
        assert entry.placement == "modal"
        assert entry.practice_area == "personal_injury"
        assert entry.jurisdiction == "california"
        assert entry.metadata["user_agent"] == "Chrome"

    def test_compliance_report_model(self):
        """Test ComplianceReport model creation and validation."""
        regional_summaries = [
            RegionalSummary(
                region="US",
                region_name="United States",
                total_disclaimers=7,
                required_disclaimers=5,
                optional_disclaimers=2,
                total_views=1250,
                total_acknowledgments=1142,
                overall_acknowledgment_rate=91.4,
                compliance_frameworks=["ABA Model Rules"],
                last_updated=datetime.utcnow(),
            )
        ]

        disclaimer_stats = [
            DisclaimerStats(
                disclaimer_id="1",
                title="AI Assistant Disclaimer",
                disclaimer_type="no_legal_advice",
                region="US",
                placement=["modal"],
                is_required=True,
                total_views=450,
                total_acknowledgments=412,
                acknowledgment_rate=91.6,
                last_viewed=datetime.utcnow(),
                last_acknowledged=datetime.utcnow(),
            )
        ]

        report = ComplianceReport(
            report_id="report_20240101_120000",
            generated_at=datetime.utcnow(),
            period_start=datetime.utcnow() - timedelta(days=30),
            period_end=datetime.utcnow(),
            regional_summaries=regional_summaries,
            disclaimer_stats=disclaimer_stats,
            total_events=1250,
            compliance_score=91.4,
            recommendations=["Maintain current practices"],
        )

        assert report.report_id == "report_20240101_120000"
        assert len(report.regional_summaries) == 1
        assert len(report.disclaimer_stats) == 1
        assert report.total_events == 1250
        assert report.compliance_score == 91.4
        assert len(report.recommendations) == 1


class TestDashboardAPIEndpoints:
    """Test dashboard API endpoints."""

    def test_router_creation(self):
        """Test that router is created correctly."""
        assert router is not None
        assert router.prefix == "/regional-disclaimers-dashboard"
        assert "Regional Disclaimers Dashboard" in router.tags

    def test_health_endpoint_structure(self):
        """Test health endpoint structure."""
        # Check that health endpoint exists in router
        health_routes = [
            route for route in router.routes if "/health" in str(route.path)
        ]
        assert len(health_routes) > 0

    def test_metrics_endpoint_structure(self):
        """Test metrics endpoint structure."""
        # Check that metrics endpoint exists in router
        metrics_routes = [
            route for route in router.routes if "/metrics" in str(route.path)
        ]
        assert len(metrics_routes) > 0

    def test_regional_summary_endpoint_structure(self):
        """Test regional summary endpoint structure."""
        # Check that regional summary endpoint exists in router
        summary_routes = [
            route for route in router.routes if "/regional-summary" in str(route.path)
        ]
        assert len(summary_routes) > 0

    def test_disclaimer_stats_endpoint_structure(self):
        """Test disclaimer stats endpoint structure."""
        # Check that disclaimer stats endpoint exists in router
        stats_routes = [
            route for route in router.routes if "/disclaimer-stats" in str(route.path)
        ]
        assert len(stats_routes) > 0

    def test_audit_log_endpoint_structure(self):
        """Test audit log endpoint structure."""
        # Check that audit log endpoint exists in router
        audit_routes = [
            route for route in router.routes if "/audit-log" in str(route.path)
        ]
        assert len(audit_routes) > 0

    def test_generate_report_endpoint_structure(self):
        """Test generate report endpoint structure."""
        # Check that generate report endpoint exists in router
        report_routes = [
            route for route in router.routes if "/generate-report" in str(route.path)
        ]
        assert len(report_routes) > 0


class TestDashboardIntegration:
    """Test dashboard integration with main application."""

    def test_frontend_dashboard_exists(self):
        """Test that frontend dashboard page exists."""
        import os

        dashboard_path = "frontend/src/app/superadmin/compliance/disclaimers/page.tsx"
        assert os.path.exists(dashboard_path)

        # Check that file contains expected components
        with open(dashboard_path, "r") as f:
            content = f.read()
            assert "DisclaimerCompliancePage" in content
            assert "DashboardMetrics" in content
            assert "RegionalSummary" in content
            assert "AuditLogEntry" in content

    def test_api_integration_in_main_app(self):
        """Test that API routes are integrated in main application."""
        import os

        main_app_path = "backend/api/main.py"
        assert os.path.exists(main_app_path)

        # Check that router is imported and registered
        with open(main_app_path, "r") as f:
            content = f.read()
            assert "regional_disclaimers_dashboard_router" in content
            assert "include_router(regional_disclaimers_dashboard_router" in content


class TestDashboardFunctionality:
    """Test dashboard functionality and business logic."""

    def test_compliance_rate_calculation(self):
        """Test compliance rate calculation logic."""
        # Test with sample data
        total_views = 1000
        total_acknowledgments = 910
        expected_rate = 91.0

        calculated_rate = (
            (total_acknowledgments / total_views * 100) if total_views > 0 else 0
        )
        assert calculated_rate == expected_rate

    def test_regional_filtering(self):
        """Test regional data filtering logic."""
        # Sample data
        all_stats = [
            {"region": "US", "disclaimer_id": "1", "title": "US Disclaimer"},
            {"region": "EU", "disclaimer_id": "2", "title": "EU Disclaimer"},
            {"region": "US", "disclaimer_id": "3", "title": "US Disclaimer 2"},
        ]

        # Filter by US region
        us_stats = [s for s in all_stats if s["region"] == "US"]
        assert len(us_stats) == 2

        # Filter by EU region
        eu_stats = [s for s in all_stats if s["region"] == "EU"]
        assert len(eu_stats) == 1

    def test_audit_log_pagination(self):
        """Test audit log pagination logic."""
        # Sample audit entries
        all_entries = [{"id": f"log_{i}", "event_type": "test"} for i in range(100)]

        # Test pagination
        limit = 50
        offset = 0
        paginated_entries = all_entries[offset : offset + limit]
        assert len(paginated_entries) == 50

        # Test second page
        offset = 50
        paginated_entries = all_entries[offset : offset + limit]
        assert len(paginated_entries) == 50

    def test_date_filtering(self):
        """Test date-based filtering logic."""
        from datetime import datetime, timedelta

        now = datetime.utcnow()
        yesterday = now - timedelta(days=1)
        tomorrow = now + timedelta(days=1)

        # Sample entries with different dates
        entries = [
            {"id": "1", "timestamp": yesterday},
            {"id": "2", "timestamp": now},
            {"id": "3", "timestamp": tomorrow},
        ]

        # Filter entries from yesterday onwards
        filtered = [e for e in entries if e["timestamp"] >= yesterday]
        assert len(filtered) == 3

        # Filter entries up to now
        filtered = [e for e in entries if e["timestamp"] <= now]
        assert len(filtered) == 2


class TestDashboardErrorHandling:
    """Test dashboard error handling and edge cases."""

    def test_empty_data_handling(self):
        """Test handling of empty data sets."""
        # Test with empty regional summaries
        summaries = []
        total_views = sum(r.get("total_views", 0) for r in summaries)
        assert total_views == 0

        # Test compliance rate with zero views
        compliance_rate = (0 / 1 * 100) if 1 > 0 else 0
        assert compliance_rate == 0

    def test_invalid_region_filtering(self):
        """Test filtering with invalid region."""
        all_stats = [
            {"region": "US", "disclaimer_id": "1"},
            {"region": "EU", "disclaimer_id": "2"},
        ]

        # Filter by non-existent region
        filtered = [s for s in all_stats if s["region"] == "INVALID"]
        assert len(filtered) == 0

    def test_negative_pagination_values(self):
        """Test pagination with negative values."""
        all_entries = [{"id": f"log_{i}"} for i in range(10)]

        # Test with negative offset (should be treated as 0)
        offset = max(0, -5)
        limit = 5
        paginated = all_entries[offset : offset + limit]
        assert len(paginated) == 5

    def test_large_pagination_values(self):
        """Test pagination with values larger than dataset."""
        all_entries = [{"id": f"log_{i}"} for i in range(10)]

        # Test with offset larger than dataset
        offset = 20
        limit = 5
        paginated = all_entries[offset : offset + limit]
        assert len(paginated) == 0


if __name__ == "__main__":
    # Run basic tests
    test_models = TestDashboardModels()
    test_models.test_dashboard_metrics_model()
    test_models.test_regional_summary_model()
    test_models.test_disclaimer_stats_model()
    test_models.test_audit_log_entry_model()
    test_models.test_compliance_report_model()

    test_endpoints = TestDashboardAPIEndpoints()
    test_endpoints.test_router_creation()

    test_functionality = TestDashboardFunctionality()
    test_functionality.test_compliance_rate_calculation()
    test_functionality.test_regional_filtering()
    test_functionality.test_audit_log_pagination()
    test_functionality.test_date_filtering()

    test_errors = TestDashboardErrorHandling()
    test_errors.test_empty_data_handling()
    test_errors.test_invalid_region_filtering()
    test_errors.test_negative_pagination_values()
    test_errors.test_large_pagination_values()

    print("✅ All Regional Disclaimers Dashboard tests passed!")
