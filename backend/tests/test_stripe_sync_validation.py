"""
Stripe Sync Validation Tests

Comprehensive tests for the Stripe sync validation system including
validation logic, discrepancy detection, and reconciliation processes.
"""

import pytest
import asyncio
from unittest.mock import patch, MagicMock, AsyncMock
from datetime import datetime
from typing import List, Dict, Any

from backend.services.stripe_sync_validator import (
    StripeSyncValidator,
    SyncDiscrepancy,
    ValidationResult,
    DiscrepancyType,
    SeverityLevel,
)
from backend.services.stripe_sync_reconciliation import (
    StripeSyncReconciliation,
    ReconciliationAction,
    ReconciliationStrategy,
    ReconciliationResult,
)


class TestStripeSyncValidator:
    """Test Stripe sync validation functionality."""

    def setup_method(self):
        """Set up test fixtures."""
        self.validator = StripeSyncValidator()

    @pytest.mark.asyncio
    async def test_validator_initialization(self):
        """Test validator initialization."""
        assert self.validator is not None
        assert self.validator.discrepancies == []
        assert self.validator.validation_cache == {}

    @pytest.mark.asyncio
    async def test_product_validation_no_discrepancies(self):
        """Test product validation with matching data."""
        # Mock data
        local_products = [
            {
                "id": "1",
                "name": "Solo Plan",
                "stripe_product_id": "prod_123",
                "is_active": True,
                "description": "Solo plan description",
            }
        ]

        stripe_products = [
            MagicMock(
                id="prod_123",
                name="Solo Plan",
                description="Solo plan description",
                active=True,
            )
        ]

        with patch.object(
            self.validator, "_get_local_products", return_value=local_products
        ):
            with patch.object(
                self.validator, "_get_stripe_products", return_value=stripe_products
            ):
                result = await self.validator.validate_products()

                assert result.entity_type == "products"
                assert result.total_local == 1
                assert result.total_stripe == 1
                assert result.matched_entities == 1
                assert result.discrepancies_count == 0
                assert len(result.discrepancies) == 0

    @pytest.mark.asyncio
    async def test_product_validation_missing_local(self):
        """Test product validation with missing local product."""
        local_products = []

        stripe_products = [
            MagicMock(
                id="prod_123",
                name="Solo Plan",
                description="Solo plan description",
                active=True,
            )
        ]

        with patch.object(
            self.validator, "_get_local_products", return_value=local_products
        ):
            with patch.object(
                self.validator, "_get_stripe_products", return_value=stripe_products
            ):
                result = await self.validator.validate_products()

                assert result.discrepancies_count == 1
                assert result.discrepancies[0].type == DiscrepancyType.MISSING_LOCAL
                assert result.discrepancies[0].severity == SeverityLevel.HIGH
                assert result.discrepancies[0].entity_id == "prod_123"

    @pytest.mark.asyncio
    async def test_product_validation_missing_stripe(self):
        """Test product validation with missing Stripe product."""
        local_products = [
            {
                "id": "1",
                "name": "Solo Plan",
                "stripe_product_id": "prod_123",
                "is_active": True,
                "description": "Solo plan description",
            }
        ]

        stripe_products = []

        with patch.object(
            self.validator, "_get_local_products", return_value=local_products
        ):
            with patch.object(
                self.validator, "_get_stripe_products", return_value=stripe_products
            ):
                result = await self.validator.validate_products()

                assert result.discrepancies_count == 1
                assert result.discrepancies[0].type == DiscrepancyType.MISSING_STRIPE
                assert result.discrepancies[0].severity == SeverityLevel.CRITICAL
                assert result.discrepancies[0].entity_id == "prod_123"

    @pytest.mark.asyncio
    async def test_product_validation_data_mismatch(self):
        """Test product validation with data mismatch."""
        local_products = [
            {
                "id": "1",
                "name": "Solo Plan",
                "stripe_product_id": "prod_123",
                "is_active": True,
                "description": "Solo plan description",
            }
        ]

        stripe_products = [
            MagicMock(
                id="prod_123",
                name="Different Name",  # Mismatch
                description="Solo plan description",
                active=True,
            )
        ]

        with patch.object(
            self.validator, "_get_local_products", return_value=local_products
        ):
            with patch.object(
                self.validator, "_get_stripe_products", return_value=stripe_products
            ):
                result = await self.validator.validate_products()

                assert result.discrepancies_count == 1
                assert result.discrepancies[0].type == DiscrepancyType.DATA_MISMATCH
                assert result.discrepancies[0].severity == SeverityLevel.MEDIUM
                assert "name mismatch" in result.discrepancies[0].description

    @pytest.mark.asyncio
    async def test_product_validation_status_mismatch(self):
        """Test product validation with status mismatch."""
        local_products = [
            {
                "id": "1",
                "name": "Solo Plan",
                "stripe_product_id": "prod_123",
                "is_active": True,
                "description": "Solo plan description",
            }
        ]

        stripe_products = [
            MagicMock(
                id="prod_123",
                name="Solo Plan",
                description="Solo plan description",
                active=False,  # Status mismatch
            )
        ]

        with patch.object(
            self.validator, "_get_local_products", return_value=local_products
        ):
            with patch.object(
                self.validator, "_get_stripe_products", return_value=stripe_products
            ):
                result = await self.validator.validate_products()

                assert result.discrepancies_count == 1
                assert result.discrepancies[0].type == DiscrepancyType.STATUS_MISMATCH
                assert result.discrepancies[0].severity == SeverityLevel.HIGH
                assert "status mismatch" in result.discrepancies[0].description

    @pytest.mark.asyncio
    async def test_comprehensive_validation(self):
        """Test comprehensive validation of all entity types."""
        # Mock all validation methods
        product_result = ValidationResult(
            entity_type="products",
            total_local=1,
            total_stripe=1,
            matched_entities=1,
            discrepancies_count=0,
            discrepancies=[],
            validation_time=datetime.utcnow(),
            duration_seconds=1.0,
        )

        with patch.object(
            self.validator, "validate_products", return_value=product_result
        ):
            with patch.object(
                self.validator, "validate_prices", return_value=product_result
            ):
                with patch.object(
                    self.validator, "validate_customers", return_value=product_result
                ):
                    with patch.object(
                        self.validator,
                        "validate_subscriptions",
                        return_value=product_result,
                    ):
                        with patch.object(
                            self.validator,
                            "_store_validation_results",
                            return_value=None,
                        ):
                            result = await self.validator.validate_all_data()

                            assert "products" in result
                            assert "prices" in result
                            assert "customers" in result
                            assert "subscriptions" in result
                            assert "summary" in result
                            assert "validation_metadata" in result

                            assert (
                                result["validation_metadata"]["total_discrepancies"]
                                == 0
                            )
                            assert result["validation_metadata"]["critical_issues"] == 0

    def test_utility_methods(self):
        """Test utility methods for finding and converting data."""
        # Test product finding
        local_products = [{"stripe_product_id": "prod_123", "name": "Test Product"}]

        found_product = self.validator._find_local_product_by_stripe_id(
            local_products, "prod_123"
        )
        assert found_product is not None
        assert found_product["name"] == "Test Product"

        not_found = self.validator._find_local_product_by_stripe_id(
            local_products, "prod_456"
        )
        assert not_found is None

        # Test Stripe product conversion
        stripe_product = MagicMock(
            id="prod_123",
            name="Test Product",
            description="Test Description",
            active=True,
            metadata={"key": "value"},
            created=1234567890,
        )

        product_dict = self.validator._stripe_product_to_dict(stripe_product)
        assert product_dict["id"] == "prod_123"
        assert product_dict["name"] == "Test Product"
        assert product_dict["description"] == "Test Description"
        assert product_dict["active"] == True
        assert product_dict["metadata"] == {"key": "value"}

    def test_validation_summary_generation(self):
        """Test validation summary generation."""
        # Add some test discrepancies
        self.validator.discrepancies = [
            SyncDiscrepancy(
                type=DiscrepancyType.MISSING_LOCAL,
                entity_type="product",
                entity_id="prod_123",
                local_data=None,
                stripe_data={},
                severity=SeverityLevel.CRITICAL,
                description="Test critical discrepancy",
                detected_at=datetime.utcnow(),
            ),
            SyncDiscrepancy(
                type=DiscrepancyType.DATA_MISMATCH,
                entity_type="product",
                entity_id="prod_456",
                local_data={},
                stripe_data={},
                severity=SeverityLevel.HIGH,
                description="Test high discrepancy",
                detected_at=datetime.utcnow(),
            ),
        ]

        validation_results = {
            "products": ValidationResult(
                entity_type="products",
                total_local=1,
                total_stripe=2,
                matched_entities=1,
                discrepancies_count=2,
                discrepancies=self.validator.discrepancies,
                validation_time=datetime.utcnow(),
                duration_seconds=1.0,
            )
        }

        summary = self.validator._generate_validation_summary(validation_results)

        assert summary["overall_status"] == "issues_detected"
        assert summary["total_discrepancies"] == 2
        assert summary["severity_breakdown"]["critical"] == 1
        assert summary["severity_breakdown"]["high"] == 1
        assert len(summary["recommendations"]) > 0


class TestStripeSyncReconciliation:
    """Test Stripe sync reconciliation functionality."""

    def setup_method(self):
        """Set up test fixtures."""
        self.reconciliation = StripeSyncReconciliation()

    def test_reconciliation_initialization(self):
        """Test reconciliation service initialization."""
        assert self.reconciliation is not None
        assert (
            self.reconciliation.strategy == ReconciliationStrategy.STRIPE_AUTHORITATIVE
        )
        assert self.reconciliation.results == []

    def test_determine_reconciliation_action_missing_local(self):
        """Test reconciliation action determination for missing local data."""
        discrepancy = SyncDiscrepancy(
            type=DiscrepancyType.MISSING_LOCAL,
            entity_type="product",
            entity_id="prod_123",
            local_data=None,
            stripe_data={},
            severity=SeverityLevel.HIGH,
            description="Missing local product",
            detected_at=datetime.utcnow(),
        )

        # Test simplified strategies
        self.reconciliation.strategy = ReconciliationStrategy.STRIPE_AUTHORITATIVE
        action = self.reconciliation._determine_reconciliation_action(discrepancy)
        assert action == ReconciliationAction.CREATE_LOCAL

        self.reconciliation.strategy = ReconciliationStrategy.MANUAL_REVIEW
        action = self.reconciliation._determine_reconciliation_action(discrepancy)
        assert action == ReconciliationAction.MANUAL_REVIEW

    def test_determine_reconciliation_action_missing_stripe(self):
        """Test reconciliation action determination for missing Stripe data."""
        discrepancy = SyncDiscrepancy(
            type=DiscrepancyType.MISSING_STRIPE,
            entity_type="product",
            entity_id="prod_123",
            local_data={},
            stripe_data=None,
            severity=SeverityLevel.CRITICAL,
            description="Missing Stripe product",
            detected_at=datetime.utcnow(),
        )

        # Test simplified strategies
        self.reconciliation.strategy = ReconciliationStrategy.STRIPE_AUTHORITATIVE
        action = self.reconciliation._determine_reconciliation_action(discrepancy)
        assert action == ReconciliationAction.CREATE_STRIPE

        self.reconciliation.strategy = ReconciliationStrategy.MANUAL_REVIEW
        action = self.reconciliation._determine_reconciliation_action(discrepancy)
        assert action == ReconciliationAction.MANUAL_REVIEW

    def test_determine_reconciliation_action_data_mismatch(self):
        """Test reconciliation action determination for data mismatches."""
        discrepancy = SyncDiscrepancy(
            type=DiscrepancyType.DATA_MISMATCH,
            entity_type="product",
            entity_id="prod_123",
            local_data={},
            stripe_data={},
            severity=SeverityLevel.MEDIUM,
            description="Data mismatch",
            detected_at=datetime.utcnow(),
        )

        # Test different strategies
        self.reconciliation.strategy = ReconciliationStrategy.STRIPE_WINS
        action = self.reconciliation._determine_reconciliation_action(discrepancy)
        assert action == ReconciliationAction.UPDATE_LOCAL

        self.reconciliation.strategy = ReconciliationStrategy.LOCAL_WINS
        action = self.reconciliation._determine_reconciliation_action(discrepancy)
        assert action == ReconciliationAction.UPDATE_STRIPE

    def test_stripe_authoritative_strategy(self):
        """Test simplified Stripe authoritative strategy."""
        discrepancy = SyncDiscrepancy(
            type=DiscrepancyType.DATA_MISMATCH,
            entity_type="product",
            entity_id="prod_123",
            local_data={},
            stripe_data={},
            severity=SeverityLevel.MEDIUM,
            description="Data mismatch",
            detected_at=datetime.utcnow(),
        )

        self.reconciliation.strategy = ReconciliationStrategy.STRIPE_AUTHORITATIVE
        action = self.reconciliation._determine_reconciliation_action(discrepancy)
        assert (
            action == ReconciliationAction.UPDATE_LOCAL
        )  # Stripe data takes precedence

        discrepancy.type = DiscrepancyType.STATUS_MISMATCH
        action = self.reconciliation._determine_reconciliation_action(discrepancy)
        assert (
            action == ReconciliationAction.UPDATE_LOCAL
        )  # Stripe status takes precedence

    @pytest.mark.asyncio
    async def test_reconcile_single_discrepancy(self):
        """Test reconciling a single discrepancy."""
        discrepancy = SyncDiscrepancy(
            type=DiscrepancyType.DATA_MISMATCH,
            entity_type="product",
            entity_id="prod_123",
            local_data={},
            stripe_data={},
            severity=SeverityLevel.MEDIUM,
            description="Data mismatch",
            detected_at=datetime.utcnow(),
        )

        # Mock the execution method
        with patch.object(
            self.reconciliation,
            "_execute_reconciliation_action",
            return_value=(True, None, {"action": "test"}),
        ):
            result = await self.reconciliation._reconcile_single_discrepancy(
                discrepancy
            )

            assert result.success == True
            assert result.discrepancy_id == "prod_123"
            assert result.action_taken == ReconciliationAction.UPDATE_LOCAL
            assert result.details == {"action": "test"}

    @pytest.mark.asyncio
    async def test_reconcile_discrepancies_list(self):
        """Test reconciling a list of discrepancies."""
        discrepancies = [
            SyncDiscrepancy(
                type=DiscrepancyType.DATA_MISMATCH,
                entity_type="product",
                entity_id="prod_123",
                local_data={},
                stripe_data={},
                severity=SeverityLevel.MEDIUM,
                description="Data mismatch 1",
                detected_at=datetime.utcnow(),
            ),
            SyncDiscrepancy(
                type=DiscrepancyType.MISSING_LOCAL,
                entity_type="product",
                entity_id="prod_456",
                local_data=None,
                stripe_data={},
                severity=SeverityLevel.HIGH,
                description="Missing local product",
                detected_at=datetime.utcnow(),
            ),
        ]

        # Mock the execution method
        with patch.object(
            self.reconciliation,
            "_execute_reconciliation_action",
            return_value=(True, None, {"action": "test"}),
        ):
            summary = await self.reconciliation.reconcile_discrepancies(discrepancies)

            assert summary.total_discrepancies == 2
            assert summary.successful_reconciliations == 2
            assert summary.failed_reconciliations == 0
            assert len(summary.results) == 2
