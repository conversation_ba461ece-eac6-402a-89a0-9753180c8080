"""
Comprehensive Webhook Security Tests

Tests for webhook security including signature verification, replay attack prevention,
malformed payload handling, and other security measures for Stripe webhooks.
"""

import pytest
import asyncio
import json
import time
import hmac
import hashlib
from unittest.mock import patch, MagicMock, AsyncMock
from datetime import datetime, timedelta
from typing import Dict, Any

import stripe
from fastapi.testclient import TestClient
from fastapi import FastAPI

# Import the webhook handler
from backend.api.routes.stripe_webhook import router as webhook_router
from backend.config import settings


class TestWebhookSecurity:
    """Test webhook security measures."""

    def setup_method(self):
        """Set up test fixtures."""
        self.app = FastAPI()
        self.app.include_router(webhook_router)
        self.client = TestClient(self.app)

        # Test webhook secret
        self.webhook_secret = "whsec_test_secret_key_for_testing"

        # Sample webhook payload
        self.sample_payload = {
            "id": "evt_test_webhook",
            "object": "event",
            "api_version": "2024-06-20",
            "created": int(time.time()),
            "data": {
                "object": {
                    "id": "cs_test_checkout_session",
                    "object": "checkout.session",
                    "customer": "cus_test_customer",
                    "subscription": "sub_test_subscription",
                    "metadata": {
                        "tenant_id": "test-tenant-123",
                        "user_id": "test-user-456",
                    },
                }
            },
            "type": "checkout.session.completed",
            "pending_webhooks": 1,
            "request": {"id": "req_test_request", "idempotency_key": None},
        }

    def _generate_stripe_signature(
        self, payload: str, secret: str, timestamp: int = None
    ) -> str:
        """Generate a valid Stripe webhook signature."""
        if timestamp is None:
            timestamp = int(time.time())

        # Create the signed payload
        signed_payload = f"{timestamp}.{payload}"

        # Generate signature
        signature = hmac.new(
            secret.encode("utf-8"), signed_payload.encode("utf-8"), hashlib.sha256
        ).hexdigest()

        return f"t={timestamp},v1={signature}"

    def test_valid_webhook_signature(self):
        """Test that valid webhook signatures are accepted."""
        payload = json.dumps(self.sample_payload)
        signature = self._generate_stripe_signature(payload, self.webhook_secret)

        with patch(
            "backend.config.settings.stripe_webhook_secret", self.webhook_secret
        ):
            with patch("stripe.Webhook.construct_event") as mock_construct:
                mock_construct.return_value = self.sample_payload

                response = self.client.post(
                    "/api/webhooks/stripe",
                    data=payload,
                    headers={
                        "stripe-signature": signature,
                        "content-type": "application/json",
                    },
                )

                assert response.status_code == 200
                mock_construct.assert_called_once()

    def test_invalid_webhook_signature(self):
        """Test that invalid webhook signatures are rejected."""
        payload = json.dumps(self.sample_payload)
        invalid_signature = "t=1234567890,v1=invalid_signature_hash"

        with patch(
            "backend.config.settings.stripe_webhook_secret", self.webhook_secret
        ):
            response = self.client.post(
                "/api/webhooks/stripe",
                data=payload,
                headers={
                    "stripe-signature": invalid_signature,
                    "content-type": "application/json",
                },
            )

            assert response.status_code == 400
            assert "signature" in response.json()["detail"].lower()

    def test_missing_webhook_signature(self):
        """Test that requests without signatures are rejected."""
        payload = json.dumps(self.sample_payload)

        response = self.client.post(
            "/api/webhooks/stripe",
            data=payload,
            headers={"content-type": "application/json"},
        )

        assert response.status_code == 400
        assert "missing stripe-signature header" in response.json()["detail"].lower()

    def test_replay_attack_prevention(self):
        """Test that old webhook events are rejected to prevent replay attacks."""
        # Create a payload with an old timestamp (more than 5 minutes ago)
        old_timestamp = int(time.time()) - 600  # 10 minutes ago
        payload = json.dumps(self.sample_payload)
        signature = self._generate_stripe_signature(
            payload, self.webhook_secret, old_timestamp
        )

        with patch(
            "backend.config.settings.stripe_webhook_secret", self.webhook_secret
        ):
            with patch("stripe.Webhook.construct_event") as mock_construct:
                mock_construct.side_effect = stripe.error.SignatureVerificationError(
                    "Timestamp outside the tolerance zone", signature
                )

                response = self.client.post(
                    "/api/webhooks/stripe",
                    data=payload,
                    headers={
                        "stripe-signature": signature,
                        "content-type": "application/json",
                    },
                )

                assert response.status_code == 400
                assert "signature" in response.json()["detail"].lower()

    def test_malformed_json_payload(self):
        """Test handling of malformed JSON payloads."""
        malformed_payload = '{"invalid": json, "missing": quote}'
        signature = self._generate_stripe_signature(
            malformed_payload, self.webhook_secret
        )

        with patch(
            "backend.config.settings.stripe_webhook_secret", self.webhook_secret
        ):
            response = self.client.post(
                "/api/webhooks/stripe",
                data=malformed_payload,
                headers={
                    "stripe-signature": signature,
                    "content-type": "application/json",
                },
            )

            assert response.status_code == 400
            assert "parse error" in response.json()["detail"].lower()

    def test_oversized_payload(self):
        """Test handling of oversized payloads."""
        # Create a very large payload
        large_data = "x" * (1024 * 1024)  # 1MB of data
        large_payload = {**self.sample_payload, "large_field": large_data}
        payload_str = json.dumps(large_payload)
        signature = self._generate_stripe_signature(payload_str, self.webhook_secret)

        with patch(
            "backend.config.settings.stripe_webhook_secret", self.webhook_secret
        ):
            response = self.client.post(
                "/api/webhooks/stripe",
                data=payload_str,
                headers={
                    "stripe-signature": signature,
                    "content-type": "application/json",
                },
            )

            # Should handle large payloads gracefully
            assert response.status_code in [200, 413, 400]

    def test_webhook_idempotency(self):
        """Test that duplicate webhook events are handled idempotently."""
        payload = json.dumps(self.sample_payload)
        signature = self._generate_stripe_signature(payload, self.webhook_secret)

        with patch(
            "backend.config.settings.stripe_webhook_secret", self.webhook_secret
        ):
            with patch("stripe.Webhook.construct_event") as mock_construct:
                mock_construct.return_value = self.sample_payload

                with patch(
                    "backend.services.webhook_idempotency.is_webhook_processed"
                ) as mock_is_processed:
                    # First request - not processed
                    mock_is_processed.return_value = False

                    response1 = self.client.post(
                        "/api/webhooks/stripe",
                        data=payload,
                        headers={
                            "stripe-signature": signature,
                            "content-type": "application/json",
                        },
                    )

                    # Second request - already processed
                    mock_is_processed.return_value = True

                    response2 = self.client.post(
                        "/api/webhooks/stripe",
                        data=payload,
                        headers={
                            "stripe-signature": signature,
                            "content-type": "application/json",
                        },
                    )

                    assert response1.status_code == 200
                    assert response2.status_code == 200

    def test_webhook_rate_limiting(self):
        """Test webhook rate limiting to prevent abuse."""
        payload = json.dumps(self.sample_payload)
        signature = self._generate_stripe_signature(payload, self.webhook_secret)

        with patch(
            "backend.config.settings.stripe_webhook_secret", self.webhook_secret
        ):
            with patch("stripe.Webhook.construct_event") as mock_construct:
                mock_construct.return_value = self.sample_payload

                # Send multiple requests rapidly
                responses = []
                for i in range(10):
                    response = self.client.post(
                        "/api/webhooks/stripe",
                        data=payload,
                        headers={
                            "stripe-signature": signature,
                            "content-type": "application/json",
                        },
                    )
                    responses.append(response)

                # All should succeed (rate limiting should be generous for webhooks)
                # but we should have monitoring in place
                for response in responses:
                    assert response.status_code in [200, 429]

    def test_webhook_content_type_validation(self):
        """Test that only JSON content types are accepted."""
        payload = json.dumps(self.sample_payload)
        signature = self._generate_stripe_signature(payload, self.webhook_secret)

        # Test with wrong content type
        response = self.client.post(
            "/api/webhooks/stripe",
            data=payload,
            headers={"stripe-signature": signature, "content-type": "text/plain"},
        )

        # Should still work as Stripe sends application/json
        # but our endpoint should handle various content types gracefully
        assert response.status_code in [200, 400, 415]

    def test_webhook_metadata_validation(self):
        """Test validation of webhook metadata for security."""
        # Test with missing tenant_id in metadata
        invalid_payload = {
            **self.sample_payload,
            "data": {
                "object": {
                    **self.sample_payload["data"]["object"],
                    "metadata": {
                        "user_id": "test-user-456"
                        # Missing tenant_id
                    },
                }
            },
        }

        payload = json.dumps(invalid_payload)
        signature = self._generate_stripe_signature(payload, self.webhook_secret)

        with patch(
            "backend.config.settings.stripe_webhook_secret", self.webhook_secret
        ):
            with patch("stripe.Webhook.construct_event") as mock_construct:
                mock_construct.return_value = invalid_payload

                response = self.client.post(
                    "/api/webhooks/stripe",
                    data=payload,
                    headers={
                        "stripe-signature": signature,
                        "content-type": "application/json",
                    },
                )

                # Should handle gracefully even with missing metadata
                assert response.status_code == 200

    @pytest.mark.asyncio
    async def test_webhook_processing_timeout(self):
        """Test that webhook processing has appropriate timeouts."""
        payload = json.dumps(self.sample_payload)
        signature = self._generate_stripe_signature(payload, self.webhook_secret)

        with patch(
            "backend.config.settings.stripe_webhook_secret", self.webhook_secret
        ):
            with patch("stripe.Webhook.construct_event") as mock_construct:
                mock_construct.return_value = self.sample_payload

                # Mock a slow processing function
                with patch(
                    "backend.api.routes.stripe_webhook._process_webhook_event"
                ) as mock_process:
                    mock_process.side_effect = asyncio.sleep(30)  # 30 second delay

                    start_time = time.time()
                    response = self.client.post(
                        "/api/webhooks/stripe",
                        data=payload,
                        headers={
                            "stripe-signature": signature,
                            "content-type": "application/json",
                        },
                    )
                    end_time = time.time()

                    # Should not take more than reasonable time
                    processing_time = end_time - start_time
                    assert processing_time < 25  # Should timeout before 30 seconds
                    assert response.status_code in [200, 500, 504]


class TestWebhookSignatureVerification:
    """Detailed tests for webhook signature verification."""

    def setup_method(self):
        """Set up test fixtures."""
        self.webhook_secret = "whsec_test_secret_key_for_testing"
        self.payload = '{"test": "data"}'

    def test_signature_generation_algorithm(self):
        """Test the signature generation algorithm matches Stripe's."""
        timestamp = 1234567890
        expected_signed_payload = f"{timestamp}.{self.payload}"

        # Generate signature using our algorithm
        signature = hmac.new(
            self.webhook_secret.encode("utf-8"),
            expected_signed_payload.encode("utf-8"),
            hashlib.sha256,
        ).hexdigest()

        stripe_signature = f"t={timestamp},v1={signature}"

        # Verify signature format
        assert stripe_signature.startswith("t=")
        assert ",v1=" in stripe_signature
        assert len(signature) == 64  # SHA256 hex digest length

    def test_signature_with_multiple_versions(self):
        """Test handling signatures with multiple versions."""
        timestamp = int(time.time())
        signed_payload = f"{timestamp}.{self.payload}"

        # Generate v1 signature
        v1_signature = hmac.new(
            self.webhook_secret.encode("utf-8"),
            signed_payload.encode("utf-8"),
            hashlib.sha256,
        ).hexdigest()

        # Create signature with multiple versions (v0 and v1)
        multi_version_signature = f"t={timestamp},v0=fake_signature,v1={v1_signature}"

        # Should accept the valid v1 signature even with invalid v0
        with patch("stripe.Webhook.construct_event") as mock_construct:
            mock_construct.return_value = {"test": "data"}
            # This would be tested in the actual webhook endpoint
            assert len(v1_signature) == 64

    def test_signature_timestamp_tolerance(self):
        """Test timestamp tolerance for signature verification."""
        current_time = int(time.time())

        # Test timestamps within tolerance (5 minutes)
        valid_timestamps = [
            current_time,
            current_time - 60,  # 1 minute ago
            current_time - 300,  # 5 minutes ago (edge case)
        ]

        for timestamp in valid_timestamps:
            signed_payload = f"{timestamp}.{self.payload}"
            signature = hmac.new(
                self.webhook_secret.encode("utf-8"),
                signed_payload.encode("utf-8"),
                hashlib.sha256,
            ).hexdigest()

            # Signature should be valid format
            assert len(signature) == 64

        # Test timestamps outside tolerance
        invalid_timestamps = [
            current_time - 600,  # 10 minutes ago
            current_time + 600,  # 10 minutes in future
        ]

        for timestamp in invalid_timestamps:
            signed_payload = f"{timestamp}.{self.payload}"
            signature = hmac.new(
                self.webhook_secret.encode("utf-8"),
                signed_payload.encode("utf-8"),
                hashlib.sha256,
            ).hexdigest()

            # Signature format is valid but timestamp is not
            assert len(signature) == 64

    def test_signature_with_special_characters(self):
        """Test signature verification with special characters in payload."""
        special_payloads = [
            '{"unicode": "测试数据"}',
            '{"special": "chars!@#$%^&*()"}',
            '{"newlines": "line1\\nline2\\nline3"}',
            '{"quotes": "He said \\"Hello\\""}',
        ]

        for payload in special_payloads:
            timestamp = int(time.time())
            signed_payload = f"{timestamp}.{payload}"

            signature = hmac.new(
                self.webhook_secret.encode("utf-8"),
                signed_payload.encode("utf-8"),
                hashlib.sha256,
            ).hexdigest()

            # Should generate valid signature for any payload
            assert len(signature) == 64
            assert signature.isalnum()  # Should be hex

    def test_signature_case_sensitivity(self):
        """Test that signature verification is case sensitive."""
        timestamp = int(time.time())
        signed_payload = f"{timestamp}.{self.payload}"

        signature = hmac.new(
            self.webhook_secret.encode("utf-8"),
            signed_payload.encode("utf-8"),
            hashlib.sha256,
        ).hexdigest()

        # Original signature
        original_sig = f"t={timestamp},v1={signature}"

        # Modified signatures (should be invalid)
        modified_signatures = [
            f"t={timestamp},v1={signature.upper()}",  # Uppercase
            f"T={timestamp},V1={signature}",  # Uppercase headers
            f"t={timestamp},v1={signature[:-1]}X",  # Modified last char
        ]

        # All modified signatures should be different
        for modified_sig in modified_signatures:
            assert modified_sig != original_sig


class TestWebhookReplayAttackPrevention:
    """Tests for preventing webhook replay attacks."""

    def setup_method(self):
        """Set up test fixtures."""
        self.webhook_secret = "whsec_test_secret_key_for_testing"

    def test_duplicate_event_id_handling(self):
        """Test handling of duplicate event IDs."""
        event_id = "evt_test_duplicate"

        # Simulate processing the same event twice
        with patch(
            "backend.services.webhook_idempotency.is_webhook_processed"
        ) as mock_is_processed:
            with patch(
                "backend.services.webhook_idempotency.mark_webhook_processed"
            ) as mock_mark_processed:

                # First time - not processed
                mock_is_processed.return_value = False
                result1 = "processed"  # Simulate processing

                # Second time - already processed
                mock_is_processed.return_value = True
                result2 = "already_processed"  # Simulate skipping

                assert result1 == "processed"
                assert result2 == "already_processed"

    def test_timestamp_window_enforcement(self):
        """Test that old timestamps are rejected."""
        current_time = int(time.time())

        # Test various timestamp scenarios
        test_cases = [
            (current_time, True),  # Current time - valid
            (current_time - 60, True),  # 1 minute ago - valid
            (current_time - 300, True),  # 5 minutes ago - edge case
            (current_time - 600, False),  # 10 minutes ago - invalid
            (current_time + 60, False),  # Future time - invalid
        ]

        for timestamp, should_be_valid in test_cases:
            # This would be tested in the actual Stripe signature verification
            # Here we just verify our test logic
            time_diff = abs(current_time - timestamp)
            is_within_tolerance = time_diff <= 300  # 5 minutes

            assert is_within_tolerance == should_be_valid

    def test_nonce_tracking(self):
        """Test tracking of webhook nonces to prevent replay."""
        # Simulate nonce tracking system
        processed_nonces = set()

        test_nonces = [
            "nonce_123",
            "nonce_456",
            "nonce_123",  # Duplicate
            "nonce_789",
        ]

        results = []
        for nonce in test_nonces:
            if nonce in processed_nonces:
                results.append("duplicate")
            else:
                processed_nonces.add(nonce)
                results.append("processed")

        expected = ["processed", "processed", "duplicate", "processed"]
        assert results == expected
