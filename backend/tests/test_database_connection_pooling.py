"""
Database Connection Pooling Tests

Comprehensive tests for database connection pooling functionality including
pool configuration, health checks, monitoring, and performance validation.
"""

import asyncio
import pytest
import time
from unittest.mock import patch, MagicMock

from backend.db.session import (
    engine,
    pool_monitor,
    check_database_health,
    get_connection_pool_stats,
    get_db_session,
    cleanup_database_connections,
    ConnectionPoolMonitor,
    POOL_SIZE,
    MAX_OVERFLOW,
    POOL_TIMEOUT,
    POOL_RECYCLE,
)


class TestConnectionPoolConfiguration:
    """Test connection pool configuration and setup."""

    def test_pool_configuration_values(self):
        """Test that pool configuration values are set correctly."""
        assert POOL_SIZE > 0
        assert MAX_OVERFLOW >= 0
        assert POOL_TIMEOUT > 0
        assert POOL_RECYCLE > 0

    def test_engine_pool_configuration(self):
        """Test that engine is configured with proper pool settings."""
        pool = engine.pool
        assert pool is not None
        assert hasattr(pool, "size")
        assert hasattr(pool, "timeout")

    def test_pool_monitor_initialization(self):
        """Test that pool monitor is properly initialized."""
        assert pool_monitor is not None
        assert isinstance(pool_monitor, ConnectionPoolMonitor)
        assert pool_monitor.engine == engine


class TestConnectionPoolMonitor:
    """Test connection pool monitoring functionality."""

    def test_pool_monitor_initial_state(self):
        """Test initial state of pool monitor."""
        monitor = ConnectionPoolMonitor(engine)
        status = monitor.get_pool_status()

        assert isinstance(status, dict)
        assert "pool_size" in status
        assert "checked_in" in status
        assert "checked_out" in status
        assert "overflow" in status
        assert "invalid" in status
        assert "total_connections" in status
        assert "failed_connections" in status
        assert "success_rate" in status
        assert "avg_connection_time" in status

    def test_record_connection_attempt_success(self):
        """Test recording successful connection attempts."""
        monitor = ConnectionPoolMonitor(engine)
        initial_total = monitor._total_connections

        monitor.record_connection_attempt(True, 0.5)

        assert monitor._total_connections == initial_total + 1
        assert monitor._failed_connections == 0
        assert len(monitor._connection_times) == 1
        assert monitor._connection_times[0] == 0.5

    def test_record_connection_attempt_failure(self):
        """Test recording failed connection attempts."""
        monitor = ConnectionPoolMonitor(engine)
        initial_total = monitor._total_connections
        initial_failed = monitor._failed_connections

        monitor.record_connection_attempt(False, 1.0)

        assert monitor._total_connections == initial_total + 1
        assert monitor._failed_connections == initial_failed + 1

    def test_connection_times_limit(self):
        """Test that connection times list is limited to 100 entries."""
        monitor = ConnectionPoolMonitor(engine)

        # Add 150 connection times
        for i in range(150):
            monitor.record_connection_attempt(True, 0.1)

        assert len(monitor._connection_times) == 100

    def test_success_rate_calculation(self):
        """Test success rate calculation."""
        monitor = ConnectionPoolMonitor(engine)

        # Record 8 successful and 2 failed attempts
        for _ in range(8):
            monitor.record_connection_attempt(True, 0.1)
        for _ in range(2):
            monitor.record_connection_attempt(False, 0.1)

        status = monitor.get_pool_status()
        assert status["success_rate"] == 80.0

    def test_average_connection_time_calculation(self):
        """Test average connection time calculation."""
        monitor = ConnectionPoolMonitor(engine)

        # Record connection times: 0.1, 0.2, 0.3
        monitor.record_connection_attempt(True, 0.1)
        monitor.record_connection_attempt(True, 0.2)
        monitor.record_connection_attempt(True, 0.3)

        status = monitor.get_pool_status()
        assert status["avg_connection_time"] == 0.2


@pytest.mark.asyncio
class TestDatabaseHealthCheck:
    """Test database health check functionality."""

    async def test_database_health_check_success(self):
        """Test successful database health check."""
        health_data = await check_database_health()

        assert isinstance(health_data, dict)
        assert "status" in health_data
        assert "connection_time_ms" in health_data
        assert "database_url" in health_data
        assert "pool_status" in health_data
        assert "environment" in health_data
        assert "timestamp" in health_data

        # Check that credentials are hidden
        assert "@***" in health_data["database_url"]

    @patch("backend.db.session.async_session_factory")
    async def test_database_health_check_failure(self, mock_session_factory):
        """Test database health check failure handling."""
        # Mock session factory to raise an exception
        mock_session_factory.side_effect = Exception("Connection failed")

        health_data = await check_database_health()

        assert health_data["status"] == "unhealthy"
        assert "error" in health_data
        assert health_data["error"] == "Connection failed"

    async def test_connection_time_measurement(self):
        """Test that connection time is measured accurately."""
        health_data = await check_database_health()

        if health_data["status"] == "healthy":
            assert health_data["connection_time_ms"] > 0
            assert (
                health_data["connection_time_ms"] < 10000
            )  # Should be less than 10 seconds


@pytest.mark.asyncio
class TestConnectionPoolStats:
    """Test connection pool statistics functionality."""

    async def test_get_connection_pool_stats(self):
        """Test getting connection pool statistics."""
        stats = await get_connection_pool_stats()

        assert isinstance(stats, dict)
        assert "configuration" in stats
        assert "current_status" in stats
        assert "health_indicators" in stats
        assert "recommendations" in stats

        # Check configuration
        config = stats["configuration"]
        assert config["pool_size"] == POOL_SIZE
        assert config["max_overflow"] == MAX_OVERFLOW
        assert config["pool_timeout"] == POOL_TIMEOUT
        assert config["pool_recycle"] == POOL_RECYCLE

        # Check health indicators
        indicators = stats["health_indicators"]
        assert "pool_utilization" in indicators
        assert "connection_success_rate" in indicators
        assert "avg_connection_time_ms" in indicators

        # Check that utilization is a percentage
        assert 0 <= indicators["pool_utilization"] <= 100

    async def test_pool_recommendations(self):
        """Test pool recommendations generation."""
        stats = await get_connection_pool_stats()
        recommendations = stats["recommendations"]

        assert isinstance(recommendations, list)
        # Recommendations should be strings
        for rec in recommendations:
            assert isinstance(rec, str)


@pytest.mark.asyncio
class TestDatabaseSession:
    """Test database session management with pooling."""

    async def test_get_db_session_success(self):
        """Test successful database session creation."""
        async for session in get_db_session():
            assert session is not None
            # Test that we can execute a simple query
            result = await session.execute("SELECT 1")
            row = await result.fetchone()
            assert row[0] == 1

    async def test_get_db_session_monitoring(self):
        """Test that database sessions are monitored."""
        initial_total = pool_monitor._total_connections

        async for session in get_db_session():
            # Just create and close the session
            pass

        # Check that connection was recorded
        # Note: This might not always increment due to connection reuse
        assert pool_monitor._total_connections >= initial_total

    async def test_multiple_concurrent_sessions(self):
        """Test multiple concurrent database sessions."""

        async def create_session():
            async for session in get_db_session():
                result = await session.execute("SELECT 1")
                await result.fetchone()
                await asyncio.sleep(0.1)  # Hold session briefly

        # Create multiple concurrent sessions
        tasks = [create_session() for _ in range(5)]
        await asyncio.gather(*tasks)

        # All tasks should complete successfully
        assert True  # If we get here, all sessions worked

    @patch("backend.db.session.async_session_factory")
    async def test_session_error_handling(self, mock_session_factory):
        """Test session error handling and rollback."""
        # Mock session to raise an exception
        mock_session = MagicMock()
        mock_session.execute.side_effect = Exception("Query failed")
        mock_session_factory.return_value = mock_session

        with pytest.raises(Exception, match="Query failed"):
            async for session in get_db_session():
                await session.execute("SELECT 1")

        # Check that rollback was called
        mock_session.rollback.assert_called_once()


@pytest.mark.asyncio
class TestConnectionPoolPerformance:
    """Test connection pool performance characteristics."""

    async def test_connection_reuse(self):
        """Test that connections are reused from the pool."""
        # Create multiple sessions and check pool status
        initial_status = pool_monitor.get_pool_status()

        for _ in range(10):
            async for session in get_db_session():
                result = await session.execute("SELECT 1")
                await result.fetchone()

        final_status = pool_monitor.get_pool_status()

        # Pool size should remain stable (connections reused)
        assert final_status["pool_size"] >= initial_status["pool_size"]

    async def test_pool_utilization_under_load(self):
        """Test pool behavior under concurrent load."""

        async def worker():
            async for session in get_db_session():
                result = await session.execute("SELECT pg_sleep(0.1)")
                await result.fetchone()

        # Start multiple workers concurrently
        tasks = [worker() for _ in range(POOL_SIZE + 5)]

        # Start tasks and check pool status
        start_time = time.time()
        await asyncio.gather(*tasks)
        end_time = time.time()

        # All tasks should complete
        assert end_time - start_time < 30  # Should complete within 30 seconds

    async def test_connection_timeout_handling(self):
        """Test connection timeout handling."""
        # This test would require mocking network delays
        # For now, just ensure the timeout configuration is set
        assert POOL_TIMEOUT > 0


@pytest.mark.asyncio
class TestDatabaseCleanup:
    """Test database connection cleanup functionality."""

    async def test_cleanup_database_connections(self):
        """Test database connection cleanup."""
        # This test should be run carefully as it disposes the engine
        # For now, just test that the function exists and can be called
        try:
            # Don't actually cleanup in tests as it would break other tests
            # await cleanup_database_connections()
            assert callable(cleanup_database_connections)
        except Exception:
            # Cleanup might fail in test environment, that's okay
            pass


class TestPoolRecommendations:
    """Test pool recommendation system."""

    def test_high_utilization_recommendation(self):
        """Test recommendation for high pool utilization."""
        from backend.db.session import _get_pool_recommendations

        # Mock high utilization scenario
        pool_status = {
            "checked_out": 45,  # High utilization
            "success_rate": 98.0,
            "avg_connection_time": 0.1,
            "invalid": 0,
        }

        recommendations = _get_pool_recommendations(pool_status)

        # Should recommend increasing pool size
        assert any("pool_size" in rec for rec in recommendations)

    def test_low_success_rate_recommendation(self):
        """Test recommendation for low connection success rate."""
        from backend.db.session import _get_pool_recommendations

        # Mock low success rate scenario
        pool_status = {
            "checked_out": 5,
            "success_rate": 85.0,  # Low success rate
            "avg_connection_time": 0.1,
            "invalid": 0,
        }

        recommendations = _get_pool_recommendations(pool_status)

        # Should recommend checking connectivity
        assert any("connectivity" in rec for rec in recommendations)

    def test_high_connection_time_recommendation(self):
        """Test recommendation for high connection times."""
        from backend.db.session import _get_pool_recommendations

        # Mock high connection time scenario
        pool_status = {
            "checked_out": 5,
            "success_rate": 98.0,
            "avg_connection_time": 2.0,  # High connection time
            "invalid": 0,
        }

        recommendations = _get_pool_recommendations(pool_status)

        # Should recommend checking performance
        assert any("performance" in rec for rec in recommendations)

    def test_invalid_connections_recommendation(self):
        """Test recommendation for invalid connections."""
        from backend.db.session import _get_pool_recommendations

        # Mock invalid connections scenario
        pool_status = {
            "checked_out": 5,
            "success_rate": 98.0,
            "avg_connection_time": 0.1,
            "invalid": 3,  # Invalid connections
        }

        recommendations = _get_pool_recommendations(pool_status)

        # Should recommend checking pool settings
        assert any("pool_recycle" in rec for rec in recommendations)
