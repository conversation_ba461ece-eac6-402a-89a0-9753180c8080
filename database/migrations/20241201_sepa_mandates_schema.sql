-- SEPA Direct Debit Mandates Schema
-- Creates tables for managing SEPA Direct Debit mandates and compliance

-- Create SEPA mandates table
CREATE TABLE IF NOT EXISTS payment_methods.sepa_mandates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Customer and payment method references
    customer_id VARCHAR(255) NOT NULL, -- Stripe customer ID
    stripe_payment_method_id VARCHAR(255) NOT NULL,
    tenant_id UUID NOT NULL,
    
    -- SEPA mandate details
    iban VARCHAR(34) NOT NULL,
    bic VARCHAR(11), -- Optional for SEPA countries
    account_holder_name VARCHAR(255) NOT NULL,
    mandate_reference VARCHAR(35) NOT NULL UNIQUE,
    
    -- Mandate acceptance details (required for SEPA compliance)
    mandate_acceptance_date TIMESTAMP WITH TIME ZONE NOT NULL,
    mandate_acceptance_type VARCHAR(20) NOT NULL CHECK (mandate_acceptance_type IN ('online', 'offline')),
    mandate_acceptance_ip INET,
    mandate_acceptance_user_agent TEXT,
    mandate_acceptance_location TEXT, -- For offline mandates
    
    -- Mandate URLs and documents
    mandate_url TEXT, -- URL to mandate document
    mandate_document_id VARCHAR(255), -- Reference to stored mandate document
    
    -- Status and lifecycle
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'active', 'inactive', 'cancelled', 'expired')),
    activated_at TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    cancellation_reason TEXT,
    
    -- SEPA compliance fields
    creditor_identifier VARCHAR(35), -- SEPA Creditor Identifier (SCI)
    scheme VARCHAR(20) DEFAULT 'CORE' CHECK (scheme IN ('CORE', 'B2B', 'COR1')),
    sequence_type VARCHAR(20) DEFAULT 'RCUR' CHECK (sequence_type IN ('FRST', 'RCUR', 'OOFF', 'FNAL')),
    
    -- Metadata and tracking
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Foreign key constraints
    CONSTRAINT fk_sepa_mandates_tenant FOREIGN KEY (tenant_id) REFERENCES tenants.firms(id) ON DELETE CASCADE
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_sepa_mandates_customer_id ON payment_methods.sepa_mandates(customer_id);
CREATE INDEX IF NOT EXISTS idx_sepa_mandates_tenant_id ON payment_methods.sepa_mandates(tenant_id);
CREATE INDEX IF NOT EXISTS idx_sepa_mandates_status ON payment_methods.sepa_mandates(status);
CREATE INDEX IF NOT EXISTS idx_sepa_mandates_iban ON payment_methods.sepa_mandates(iban);
CREATE INDEX IF NOT EXISTS idx_sepa_mandates_reference ON payment_methods.sepa_mandates(mandate_reference);
CREATE INDEX IF NOT EXISTS idx_sepa_mandates_created_at ON payment_methods.sepa_mandates(created_at);

-- Create SEPA payments table for tracking SEPA Direct Debit transactions
CREATE TABLE IF NOT EXISTS payment_methods.sepa_payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Payment references
    mandate_id UUID NOT NULL REFERENCES payment_methods.sepa_mandates(id) ON DELETE CASCADE,
    stripe_payment_intent_id VARCHAR(255) NOT NULL UNIQUE,
    stripe_charge_id VARCHAR(255),
    tenant_id UUID NOT NULL,
    
    -- Payment details
    amount_cents INTEGER NOT NULL CHECK (amount_cents > 0),
    currency VARCHAR(3) NOT NULL DEFAULT 'EUR',
    description TEXT,
    
    -- SEPA specific fields
    end_to_end_id VARCHAR(35), -- SEPA End-to-End Identification
    instruction_id VARCHAR(35), -- SEPA Instruction Identification
    remittance_information TEXT, -- Payment description for bank statement
    
    -- Payment status and lifecycle
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN (
        'pending', 'processing', 'succeeded', 'failed', 'cancelled', 'disputed', 'refunded'
    )),
    failure_reason TEXT,
    failure_code VARCHAR(50),
    
    -- Processing timestamps
    initiated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE,
    settled_at TIMESTAMP WITH TIME ZONE,
    
    -- Dispute and refund tracking
    disputed_at TIMESTAMP WITH TIME ZONE,
    dispute_reason TEXT,
    refunded_at TIMESTAMP WITH TIME ZONE,
    refund_amount_cents INTEGER,
    
    -- Metadata and tracking
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Foreign key constraints
    CONSTRAINT fk_sepa_payments_tenant FOREIGN KEY (tenant_id) REFERENCES tenants.firms(id) ON DELETE CASCADE
);

-- Create indexes for SEPA payments
CREATE INDEX IF NOT EXISTS idx_sepa_payments_mandate_id ON payment_methods.sepa_payments(mandate_id);
CREATE INDEX IF NOT EXISTS idx_sepa_payments_tenant_id ON payment_methods.sepa_payments(tenant_id);
CREATE INDEX IF NOT EXISTS idx_sepa_payments_status ON payment_methods.sepa_payments(status);
CREATE INDEX IF NOT EXISTS idx_sepa_payments_stripe_payment_intent ON payment_methods.sepa_payments(stripe_payment_intent_id);
CREATE INDEX IF NOT EXISTS idx_sepa_payments_created_at ON payment_methods.sepa_payments(created_at);
CREATE INDEX IF NOT EXISTS idx_sepa_payments_processed_at ON payment_methods.sepa_payments(processed_at);

-- Create SEPA mandate events table for audit trail
CREATE TABLE IF NOT EXISTS payment_methods.sepa_mandate_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Event references
    mandate_id UUID NOT NULL REFERENCES payment_methods.sepa_mandates(id) ON DELETE CASCADE,
    tenant_id UUID NOT NULL,
    
    -- Event details
    event_type VARCHAR(50) NOT NULL CHECK (event_type IN (
        'mandate_created', 'mandate_activated', 'mandate_cancelled', 'mandate_expired',
        'payment_initiated', 'payment_succeeded', 'payment_failed', 'payment_disputed',
        'mandate_updated', 'compliance_check', 'document_generated'
    )),
    event_description TEXT,
    
    -- Event context
    triggered_by VARCHAR(50), -- 'system', 'user', 'stripe_webhook', 'admin'
    user_id UUID, -- If triggered by user action
    ip_address INET,
    user_agent TEXT,
    
    -- Event data
    event_data JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Foreign key constraints
    CONSTRAINT fk_sepa_events_tenant FOREIGN KEY (tenant_id) REFERENCES tenants.firms(id) ON DELETE CASCADE
);

-- Create indexes for SEPA mandate events
CREATE INDEX IF NOT EXISTS idx_sepa_events_mandate_id ON payment_methods.sepa_mandate_events(mandate_id);
CREATE INDEX IF NOT EXISTS idx_sepa_events_tenant_id ON payment_methods.sepa_mandate_events(tenant_id);
CREATE INDEX IF NOT EXISTS idx_sepa_events_type ON payment_methods.sepa_mandate_events(event_type);
CREATE INDEX IF NOT EXISTS idx_sepa_events_created_at ON payment_methods.sepa_mandate_events(created_at);

-- Create SEPA compliance settings table
CREATE TABLE IF NOT EXISTS payment_methods.sepa_compliance_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Organization details
    tenant_id UUID NOT NULL UNIQUE,
    
    -- SEPA Creditor details
    creditor_name VARCHAR(255) NOT NULL,
    creditor_identifier VARCHAR(35) NOT NULL, -- SEPA Creditor Identifier (SCI)
    creditor_address JSONB NOT NULL, -- Structured address
    creditor_country VARCHAR(2) NOT NULL,
    
    -- Business details
    business_registration_number VARCHAR(50),
    vat_number VARCHAR(50),
    
    -- Mandate settings
    default_scheme VARCHAR(20) DEFAULT 'CORE' CHECK (default_scheme IN ('CORE', 'B2B', 'COR1')),
    default_sequence_type VARCHAR(20) DEFAULT 'RCUR' CHECK (default_sequence_type IN ('FRST', 'RCUR', 'OOFF', 'FNAL')),
    mandate_validity_days INTEGER DEFAULT 365,
    
    -- Notification settings
    pre_notification_days INTEGER DEFAULT 14, -- SEPA requires 14 days pre-notification
    notification_language VARCHAR(5) DEFAULT 'en',
    
    -- Compliance flags
    psd2_compliant BOOLEAN DEFAULT false,
    gdpr_compliant BOOLEAN DEFAULT false,
    
    -- Metadata and tracking
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Foreign key constraints
    CONSTRAINT fk_sepa_compliance_tenant FOREIGN KEY (tenant_id) REFERENCES tenants.firms(id) ON DELETE CASCADE
);

-- Create indexes for SEPA compliance settings
CREATE INDEX IF NOT EXISTS idx_sepa_compliance_tenant_id ON payment_methods.sepa_compliance_settings(tenant_id);
CREATE INDEX IF NOT EXISTS idx_sepa_compliance_creditor_id ON payment_methods.sepa_compliance_settings(creditor_identifier);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION payment_methods.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_sepa_mandates_updated_at 
    BEFORE UPDATE ON payment_methods.sepa_mandates 
    FOR EACH ROW EXECUTE FUNCTION payment_methods.update_updated_at_column();

CREATE TRIGGER update_sepa_payments_updated_at 
    BEFORE UPDATE ON payment_methods.sepa_payments 
    FOR EACH ROW EXECUTE FUNCTION payment_methods.update_updated_at_column();

CREATE TRIGGER update_sepa_compliance_updated_at 
    BEFORE UPDATE ON payment_methods.sepa_compliance_settings 
    FOR EACH ROW EXECUTE FUNCTION payment_methods.update_updated_at_column();

-- Insert default SEPA compliance settings for existing tenants
INSERT INTO payment_methods.sepa_compliance_settings (
    tenant_id,
    creditor_name,
    creditor_identifier,
    creditor_address,
    creditor_country
)
SELECT 
    id as tenant_id,
    COALESCE(firm_name, 'PI Lawyer AI') as creditor_name,
    'PLACEHOLDER-SCI-' || SUBSTRING(id::text, 1, 8) as creditor_identifier,
    COALESCE(
        address,
        '{"street": "TBD", "city": "TBD", "postal_code": "TBD", "country": "BE"}'::jsonb
    ) as creditor_address,
    COALESCE(country, 'BE') as creditor_country
FROM tenants.firms
WHERE id NOT IN (
    SELECT tenant_id FROM payment_methods.sepa_compliance_settings
)
ON CONFLICT (tenant_id) DO NOTHING;

-- Add comments for documentation
COMMENT ON TABLE payment_methods.sepa_mandates IS 'SEPA Direct Debit mandates with compliance tracking';
COMMENT ON TABLE payment_methods.sepa_payments IS 'SEPA Direct Debit payment transactions';
COMMENT ON TABLE payment_methods.sepa_mandate_events IS 'Audit trail for SEPA mandate lifecycle events';
COMMENT ON TABLE payment_methods.sepa_compliance_settings IS 'SEPA compliance configuration per tenant';

COMMENT ON COLUMN payment_methods.sepa_mandates.mandate_reference IS 'Unique mandate reference for SEPA compliance';
COMMENT ON COLUMN payment_methods.sepa_mandates.creditor_identifier IS 'SEPA Creditor Identifier (SCI) for the organization';
COMMENT ON COLUMN payment_methods.sepa_payments.end_to_end_id IS 'SEPA End-to-End Identification for payment tracking';
COMMENT ON COLUMN payment_methods.sepa_compliance_settings.pre_notification_days IS 'Days before payment to send pre-notification (SEPA requirement: 14 days)';

-- Grant permissions (adjust based on your role structure)
GRANT SELECT, INSERT, UPDATE, DELETE ON payment_methods.sepa_mandates TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON payment_methods.sepa_payments TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON payment_methods.sepa_mandate_events TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON payment_methods.sepa_compliance_settings TO authenticated;
