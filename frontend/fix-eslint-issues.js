#!/usr/bin/env node

/**
 * ESLint Issue Fixer Script
 * 
 * This script helps automate common ESLint fixes across the codebase.
 * It handles:
 * 1. Unused variable warnings by prefixing with underscore
 * 2. Missing return types for simple functions
 * 3. Converting <img> to <Image /> with Next.js import
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const srcDir = './src';
const exclude = ['node_modules', '.next', 'dist', 'build'];

// Utility functions
function walkDir(dir, callback) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && !exclude.includes(file)) {
      walkDir(filePath, callback);
    } else if (stat.isFile() && (file.endsWith('.tsx') || file.endsWith('.ts'))) {
      callback(filePath);
    }
  });
}

// Fix unused variables by prefixing with underscore
function fixUnusedVariables(content) {
  // Match common unused variable patterns
  const patterns = [
    // Function parameters: (error: Error) => to (_error: Error) =>
    /\(([a-zA-Z][a-zA-Z0-9]*): ([^)]+)\) =>/g,
    // Variable declarations: const error = to const _error =
    /const ([a-zA-Z][a-zA-Z0-9]*) = /g,
    // Destructured parameters: { error } to { _error }
    /\{ ([a-zA-Z][a-zA-Z0-9]*) \}/g,
  ];
  
  let fixedContent = content;
  
  // Only fix if the variable is actually unused (this is a simple heuristic)
  const commonUnusedNames = ['error', 'router', 'supabase', 'data', 'response', 'result'];
  
  commonUnusedNames.forEach(name => {
    // If variable is declared but only used once (in declaration), prefix it
    const declareRegex = new RegExp(`\\b${name}\\b`, 'g');
    const matches = content.match(declareRegex);
    
    if (matches && matches.length <= 2) { // Declared and maybe used once
      fixedContent = fixedContent.replace(
        new RegExp(`\\b${name}\\b(?=\\s*[=:])`),
        `_${name}`
      );
    }
  });
  
  return fixedContent;
}

// Add missing return types to simple functions
function addReturnTypes(content) {
  let fixedContent = content;
  
  // Add React.ReactElement to component functions
  fixedContent = fixedContent.replace(
    /export function ([A-Z][a-zA-Z0-9]*)\(([^)]*)\)\s*\{/g,
    'export function $1($2): React.ReactElement {'
  );
  
  // Add void to handler functions
  fixedContent = fixedContent.replace(
    /const (handle[A-Z][a-zA-Z0-9]*) = \(([^)]*)\) => \{/g,
    'const $1 = ($2): void => {'
  );
  
  // Add React import if needed and not present
  if (fixedContent.includes('React.ReactElement') && !fixedContent.includes('import React')) {
    fixedContent = fixedContent.replace(
      /import\s+\{([^}]+)\}\s+from\s+['"]react['"];?/,
      "import React, { $1 } from 'react';"
    );
  }
  
  return fixedContent;
}

// Convert <img> to Next.js <Image />
function convertImgToImage(content) {
  let fixedContent = content;
  
  // Add Image import if <img> tags are found
  if (fixedContent.includes('<img') && !fixedContent.includes('import Image')) {
    fixedContent = fixedContent.replace(
      /import React/,
      "import React from 'react';\nimport Image from 'next/image'"
    );
  }
  
  // Convert <img> tags to <Image /> (simple cases)
  fixedContent = fixedContent.replace(
    /<img\s+src=\{([^}]+)\}\s+alt=["']([^"']+)["']\s*([^>]*)\s*\/?>/g,
    '<Image src={$1} alt="$2" width={200} height={200} $3 />'
  );
  
  return fixedContent;
}

// Main processing function
function processFile(filePath) {
  console.log(`Processing: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  const originalContent = content;
  
  // Apply fixes
  content = fixUnusedVariables(content);
  content = addReturnTypes(content);
  content = convertImgToImage(content);
  
  // Write back if changed
  if (content !== originalContent) {
    fs.writeFileSync(filePath, content);
    console.log(`  ✓ Fixed: ${filePath}`);
    return true;
  }
  
  return false;
}

// Main execution
function main() {
  console.log('Starting ESLint issue fixes...\n');
  
  let filesProcessed = 0;
  let filesFixed = 0;
  
  walkDir(srcDir, (filePath) => {
    filesProcessed++;
    if (processFile(filePath)) {
      filesFixed++;
    }
  });
  
  console.log(`\nComplete! Processed ${filesProcessed} files, fixed ${filesFixed} files.`);
  
  // Run ESLint to check results
  console.log('\nRunning ESLint to check results...');
  try {
    execSync('npm run lint -- --max-warnings 100', { stdio: 'inherit' });
  } catch (error) {
    console.log('ESLint found remaining issues. Check the output above.');
  }
}

// Execute if run directly
if (require.main === module) {
  main();
}

module.exports = {
  processFile,
  fixUnusedVariables,
  addReturnTypes,
  convertImgToImage
};