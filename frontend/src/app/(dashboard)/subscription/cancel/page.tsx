'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { XCircle, ArrowLeft, Mail, RefreshCw } from 'lucide-react';

export default function SubscriptionCancelPage() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <Card className="w-full max-w-lg">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <XCircle className="h-16 w-16 text-orange-500" />
          </div>
          <CardTitle className="text-2xl text-gray-900">
            Subscription Cancelled
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-center space-y-4">
            <p className="text-gray-600">
              Your subscription process was cancelled. No payment has been processed.
            </p>
            
            <div className="bg-orange-50 p-4 rounded-lg">
              <h3 className="font-semibold text-orange-900 mb-2">What happened?</h3>
              <p className="text-sm text-orange-800">
                You chose to cancel the subscription process before completing payment. 
                Your account remains unchanged and you can try again at any time.
              </p>
            </div>
          </div>

          <div className="space-y-3">
            <h3 className="font-semibold text-gray-900">What would you like to do?</h3>
            
            <div className="space-y-2">
              <Button 
                onClick={() => router.push('/subscribe')}
                className="w-full"
                variant="default"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
              
              <Button 
                onClick={() => router.push('/dashboard')}
                className="w-full"
                variant="outline"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
              
              <Button 
                onClick={() => window.location.href = 'mailto:<EMAIL>?subject=Subscription Questions'}
                className="w-full"
                variant="ghost"
              >
                <Mail className="h-4 w-4 mr-2" />
                Contact Sales
              </Button>
            </div>
          </div>

          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold text-blue-900 mb-2">Need Help?</h3>
            <p className="text-sm text-blue-800 mb-3">
              If you're having trouble with the subscription process or have questions about our plans, 
              we're here to help.
            </p>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Compare our subscription plans</li>
              <li>• Get answers about billing and features</li>
              <li>• Schedule a demo with our team</li>
            </ul>
          </div>

          <div className="text-center">
            <p className="text-xs text-gray-500">
              Questions? <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">Contact Support</a>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
