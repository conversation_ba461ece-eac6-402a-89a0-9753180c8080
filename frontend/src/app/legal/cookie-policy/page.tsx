'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Eye, Shield, Calendar, ToggleLeft, ToggleRight } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Switch } from '@/components/ui/switch'
import { RegionalDisclaimerBanner } from '@/components/legal/regional-disclaimer-banner'

interface CookieCategory {
  id: string
  name: string
  description: string
  required: boolean
  enabled: boolean
  cookies: CookieDetail[]
}

interface CookieDetail {
  name: string
  purpose: string
  duration: string
  provider: string
}

export default function CookiePolicyPage() {
  const [userRegion, setUserRegion] = useState<string>('US')
  const [lastUpdated] = useState(new Date('2024-12-01'))
  const [cookiePreferences, setCookiePreferences] = useState<Record<string, boolean>>({
    essential: true,
    analytics: false,
    marketing: false,
    preferences: false
  })

  const cookieCategories: CookieCategory[] = [
    {
      id: 'essential',
      name: 'Essential Cookies',
      description: 'Required for the website to function properly. Cannot be disabled.',
      required: true,
      enabled: true,
      cookies: [
        {
          name: 'ailex_session',
          purpose: 'Maintains user session and authentication state',
          duration: 'Session',
          provider: 'AiLex'
        },
        {
          name: 'ailex_csrf',
          purpose: 'Prevents cross-site request forgery attacks',
          duration: 'Session',
          provider: 'AiLex'
        },
        {
          name: 'ailex_region',
          purpose: 'Stores detected user region for data residency compliance',
          duration: '1 year',
          provider: 'AiLex'
        }
      ]
    },
    {
      id: 'analytics',
      name: 'Analytics Cookies',
      description: 'Help us understand how visitors interact with our website.',
      required: false,
      enabled: cookiePreferences.analytics,
      cookies: [
        {
          name: '_ga',
          purpose: 'Distinguishes unique users for analytics',
          duration: '2 years',
          provider: 'Google Analytics'
        },
        {
          name: '_ga_*',
          purpose: 'Stores session state for analytics',
          duration: '2 years',
          provider: 'Google Analytics'
        },
        {
          name: 'ailex_analytics',
          purpose: 'Tracks feature usage and performance metrics',
          duration: '1 year',
          provider: 'AiLex'
        }
      ]
    },
    {
      id: 'marketing',
      name: 'Marketing Cookies',
      description: 'Used to deliver relevant advertisements and track campaign effectiveness.',
      required: false,
      enabled: cookiePreferences.marketing,
      cookies: [
        {
          name: '_fbp',
          purpose: 'Facebook pixel for conversion tracking',
          duration: '3 months',
          provider: 'Facebook'
        },
        {
          name: 'linkedin_oauth',
          purpose: 'LinkedIn advertising and conversion tracking',
          duration: '1 year',
          provider: 'LinkedIn'
        }
      ]
    },
    {
      id: 'preferences',
      name: 'Preference Cookies',
      description: 'Remember your settings and preferences for a better experience.',
      required: false,
      enabled: cookiePreferences.preferences,
      cookies: [
        {
          name: 'ailex_theme',
          purpose: 'Stores user interface theme preference',
          duration: '1 year',
          provider: 'AiLex'
        },
        {
          name: 'ailex_language',
          purpose: 'Stores user language preference',
          duration: '1 year',
          provider: 'AiLex'
        },
        {
          name: 'ailex_dashboard_layout',
          purpose: 'Stores dashboard layout preferences',
          duration: '1 year',
          provider: 'AiLex'
        }
      ]
    }
  ]

  useEffect(() => {
    // Load saved preferences from localStorage
    const saved = localStorage.getItem('ailex_cookie_preferences')
    if (saved) {
      setCookiePreferences(JSON.parse(saved))
    }
  }, [])

  const updatePreference = (categoryId: string, enabled: boolean) => {
    const newPreferences = { ...cookiePreferences, [categoryId]: enabled }
    setCookiePreferences(newPreferences)
    localStorage.setItem('ailex_cookie_preferences', JSON.stringify(newPreferences))
  }

  const acceptAll = () => {
    const allEnabled = {
      essential: true,
      analytics: true,
      marketing: true,
      preferences: true
    }
    setCookiePreferences(allEnabled)
    localStorage.setItem('ailex_cookie_preferences', JSON.stringify(allEnabled))
  }

  const rejectAll = () => {
    const essentialOnly = {
      essential: true,
      analytics: false,
      marketing: false,
      preferences: false
    }
    setCookiePreferences(essentialOnly)
    localStorage.setItem('ailex_cookie_preferences', JSON.stringify(essentialOnly))
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <Cookie className="h-8 w-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">Cookie Policy</h1>
          </div>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            How we use cookies and similar technologies to improve your experience and comply with privacy regulations.
          </p>
          <div className="flex items-center justify-center space-x-4 mt-4">
            <div className="flex items-center space-x-2">
              <Settings className="h-4 w-4 text-gray-500" />
              <Badge variant="outline" className="text-sm">
                Region: {userRegion}
              </Badge>
            </div>
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-600">
                Last Updated: {lastUpdated.toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>

        {/* Regional Disclaimer Banner */}
        <div className="mb-8">
          <RegionalDisclaimerBanner
            placement="footer"
            showRegionBadge={true}
            className="bg-white border border-gray-200 rounded-lg p-6"
          />
        </div>

        {/* Cookie Preference Controls */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5 text-blue-600" />
              <span>Cookie Preferences</span>
            </CardTitle>
            <CardDescription>
              Manage your cookie preferences. Changes take effect immediately.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {cookieCategories.map((category) => (
                <div key={category.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h4 className="font-semibold text-gray-900">{category.name}</h4>
                      {category.required && (
                        <Badge variant="destructive" className="text-xs">Required</Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600">{category.description}</p>
                    <p className="text-xs text-gray-500 mt-1">
                      {category.cookies.length} cookie{category.cookies.length !== 1 ? 's' : ''}
                    </p>
                  </div>
                  <div className="ml-4">
                    <Switch
                      checked={category.required || cookiePreferences[category.id]}
                      onCheckedChange={(checked) => updatePreference(category.id, checked)}
                      disabled={category.required}
                    />
                  </div>
                </div>
              ))}
              
              <div className="flex space-x-4 pt-4">
                <Button onClick={acceptAll} className="flex-1">
                  Accept All Cookies
                </Button>
                <Button onClick={rejectAll} variant="outline" className="flex-1">
                  Reject Non-Essential
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Cookie Policy Content */}
        <div className="space-y-8">
          
          {/* 1. What Are Cookies */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Cookie className="h-5 w-5 text-blue-600" />
                <span>1. What Are Cookies?</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <p>
                Cookies are small text files that are stored on your device when you visit our website. 
                They help us provide you with a better experience by remembering your preferences and 
                understanding how you use our services.
              </p>
              <p><strong>Types of cookies we use:</strong></p>
              <ul>
                <li><strong>Session Cookies:</strong> Temporary cookies that expire when you close your browser</li>
                <li><strong>Persistent Cookies:</strong> Cookies that remain on your device for a specified period</li>
                <li><strong>First-Party Cookies:</strong> Set directly by AiLex</li>
                <li><strong>Third-Party Cookies:</strong> Set by our trusted partners and service providers</li>
              </ul>
            </CardContent>
          </Card>

          {/* 2. Cookie Categories */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Eye className="h-5 w-5 text-green-600" />
                <span>2. Cookie Categories</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {cookieCategories.map((category) => (
                  <div key={category.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-semibold text-gray-900">{category.name}</h4>
                      <div className="flex items-center space-x-2">
                        {category.required && (
                          <Badge variant="destructive" className="text-xs">Required</Badge>
                        )}
                        <Badge variant={cookiePreferences[category.id] ? "default" : "secondary"} className="text-xs">
                          {cookiePreferences[category.id] ? "Enabled" : "Disabled"}
                        </Badge>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mb-4">{category.description}</p>
                    
                    <div className="space-y-3">
                      <h5 className="text-sm font-medium text-gray-900">Cookies in this category:</h5>
                      {category.cookies.map((cookie, index) => (
                        <div key={index} className="bg-gray-50 rounded p-3">
                          <div className="grid grid-cols-1 md:grid-cols-4 gap-2 text-sm">
                            <div>
                              <span className="font-medium text-gray-900">{cookie.name}</span>
                            </div>
                            <div className="text-gray-600">{cookie.purpose}</div>
                            <div className="text-gray-600">{cookie.duration}</div>
                            <div className="text-gray-600">{cookie.provider}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 3. Managing Cookies */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5 text-purple-600" />
                <span>3. Managing Your Cookie Preferences</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <p><strong>Through Our Website:</strong></p>
              <ul>
                <li>Use the cookie preference controls above</li>
                <li>Access cookie settings from the footer of any page</li>
                <li>Changes take effect immediately</li>
              </ul>
              
              <p><strong>Through Your Browser:</strong></p>
              <ul>
                <li><strong>Chrome:</strong> Settings → Privacy and Security → Cookies and other site data</li>
                <li><strong>Firefox:</strong> Preferences → Privacy & Security → Cookies and Site Data</li>
                <li><strong>Safari:</strong> Preferences → Privacy → Manage Website Data</li>
                <li><strong>Edge:</strong> Settings → Cookies and site permissions → Cookies and site data</li>
              </ul>
              
              <p><strong>Note:</strong> Disabling essential cookies may affect website functionality.</p>
            </CardContent>
          </Card>

          {/* 4. Third-Party Cookies */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-orange-600" />
                <span>4. Third-Party Cookies and Services</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <p>We work with trusted third-party services that may set cookies:</p>
              
              <p><strong>Analytics Services:</strong></p>
              <ul>
                <li><strong>Google Analytics:</strong> Website usage analytics and performance monitoring</li>
                <li><strong>Hotjar:</strong> User behavior analysis and heatmaps</li>
              </ul>
              
              <p><strong>Marketing Services:</strong></p>
              <ul>
                <li><strong>Facebook Pixel:</strong> Conversion tracking and advertising optimization</li>
                <li><strong>LinkedIn Insight Tag:</strong> Professional audience analytics</li>
              </ul>
              
              <p><strong>Support Services:</strong></p>
              <ul>
                <li><strong>Intercom:</strong> Customer support chat functionality</li>
                <li><strong>Stripe:</strong> Payment processing and fraud prevention</li>
              </ul>
              
              <p>Each service has its own privacy policy governing their use of cookies.</p>
            </CardContent>
          </Card>

          {/* 5. Legal Basis */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-red-600" />
                <span>5. Legal Basis for Cookie Use</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <p><strong>Essential Cookies:</strong></p>
              <p>
                Used based on legitimate interest to provide core website functionality. 
                These are necessary for the website to function properly.
              </p>
              
              <p><strong>Non-Essential Cookies:</strong></p>
              <p>
                Used based on your explicit consent. You can withdraw consent at any time 
                through our cookie preference controls.
              </p>
              
              <p><strong>Regional Compliance:</strong></p>
              <ul>
                <li><strong>EU/UK:</strong> GDPR and ePrivacy Directive compliance</li>
                <li><strong>US:</strong> CCPA and state privacy law compliance</li>
                <li><strong>Canada:</strong> PIPEDA compliance</li>
              </ul>
            </CardContent>
          </Card>

        </div>

        {/* Contact Information */}
        <div className="mt-12 text-center">
          <Separator className="mb-8" />
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Questions About Cookies?</h3>
            <p className="text-gray-600">
              Contact our Privacy Team at <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline"><EMAIL></a>
            </p>
            <div className="flex items-center justify-center space-x-4 text-sm text-gray-500">
              <span>AiLex Legal Services</span>
              <span>•</span>
              <span>Last Updated: {lastUpdated.toLocaleDateString()}</span>
              <span>•</span>
              <span>Version 1.0</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
