'use client'

import { useState, useEffect } from 'react'
import { Shield, Globe, Eye, Lock, Database, Users, Calendar, ExternalLink } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { RegionalDisclaimerBanner } from '@/components/legal/regional-disclaimer-banner'

export default function PrivacyPolicyPage() {
  const [userRegion, setUserRegion] = useState<string>('US')
  const [lastUpdated] = useState(new Date('2024-12-01'))

  useEffect(() => {
    // In a real implementation, this would detect user region
    setUserRegion('US')
  }, [])

  const getRegionalContent = () => {
    if (userRegion === 'EU') {
      return {
        framework: 'General Data Protection Regulation (GDPR)',
        rights: [
          'Right to access your personal data',
          'Right to rectification of inaccurate data',
          'Right to erasure ("right to be forgotten")',
          'Right to restrict processing',
          'Right to data portability',
          'Right to object to processing',
          'Rights related to automated decision-making'
        ],
        lawfulBasis: 'Article 6(1)(b) - performance of contract, Article 6(1)(f) - legitimate interests',
        dataController: 'AiLex Europe BVBA, Belgium',
        supervisoryAuthority: 'Belgian Data Protection Authority'
      }
    }
    return {
      framework: 'California Consumer Privacy Act (CCPA) and applicable state laws',
      rights: [
        'Right to know what personal information is collected',
        'Right to delete personal information',
        'Right to opt-out of sale of personal information',
        'Right to non-discrimination for exercising privacy rights',
        'Right to correct inaccurate personal information'
      ],
      lawfulBasis: 'Contractual necessity and legitimate business interests',
      dataController: 'AiLex Inc., Texas, USA',
      supervisoryAuthority: 'California Attorney General\'s Office'
    }
  }

  const regional = getRegionalContent()

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <Shield className="h-8 w-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">Privacy Policy</h1>
          </div>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            How we collect, use, and protect your personal information in compliance with {regional.framework}.
          </p>
          <div className="flex items-center justify-center space-x-4 mt-4">
            <div className="flex items-center space-x-2">
              <Globe className="h-4 w-4 text-gray-500" />
              <Badge variant="outline" className="text-sm">
                Region: {userRegion}
              </Badge>
            </div>
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-600">
                Last Updated: {lastUpdated.toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>

        {/* Regional Disclaimer Banner */}
        <div className="mb-8">
          <RegionalDisclaimerBanner
            placement="footer"
            showRegionBadge={true}
            className="bg-white border border-gray-200 rounded-lg p-6"
          />
        </div>

        {/* Privacy Content */}
        <div className="space-y-8">
          
          {/* 1. Information We Collect */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Database className="h-5 w-5 text-blue-600" />
                <span>1. Information We Collect</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <p><strong>Account Information:</strong></p>
              <ul>
                <li>Name, email address, professional credentials</li>
                <li>Law firm or organization details</li>
                <li>Billing and payment information</li>
                <li>Professional licensing information</li>
              </ul>
              
              <p><strong>Usage Data:</strong></p>
              <ul>
                <li>Documents uploaded and processed</li>
                <li>AI interactions and queries</li>
                <li>Feature usage and preferences</li>
                <li>System logs and performance data</li>
              </ul>
              
              <p><strong>Technical Data:</strong></p>
              <ul>
                <li>IP address and location data</li>
                <li>Device and browser information</li>
                <li>Session data and cookies</li>
                <li>Security and authentication logs</li>
              </ul>
            </CardContent>
          </Card>

          {/* 2. How We Use Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Eye className="h-5 w-5 text-green-600" />
                <span>2. How We Use Your Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <p><strong>Service Provision:</strong></p>
              <ul>
                <li>Providing AI-powered legal assistance</li>
                <li>Document processing and analysis</li>
                <li>Account management and billing</li>
                <li>Customer support and communication</li>
              </ul>
              
              <p><strong>Service Improvement:</strong></p>
              <ul>
                <li>Analyzing usage patterns to improve features</li>
                <li>Training AI models (with anonymized data)</li>
                <li>Security monitoring and fraud prevention</li>
                <li>Performance optimization</li>
              </ul>
              
              <p><strong>Legal Basis:</strong> {regional.lawfulBasis}</p>
            </CardContent>
          </Card>

          {/* 3. Data Sharing and Disclosure */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="h-5 w-5 text-purple-600" />
                <span>3. Data Sharing and Disclosure</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <p><strong>We do not sell your personal information.</strong></p>
              
              <p><strong>Limited Sharing:</strong></p>
              <ul>
                <li><strong>Service Providers:</strong> Trusted third parties who assist in service delivery (cloud hosting, payment processing)</li>
                <li><strong>Legal Requirements:</strong> When required by law, court order, or regulatory authority</li>
                <li><strong>Business Transfers:</strong> In connection with mergers, acquisitions, or asset sales</li>
                <li><strong>Consent:</strong> When you explicitly authorize sharing</li>
              </ul>
              
              <p><strong>Data Residency:</strong> Your data is processed and stored in compliance with regional requirements. 
              {userRegion === 'EU' ? ' EU users\' data remains within EU regions.' : ' US users\' data remains within US regions.'}</p>
            </CardContent>
          </Card>

          {/* 4. Data Security */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Lock className="h-5 w-5 text-red-600" />
                <span>4. Data Security</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <p><strong>Security Measures:</strong></p>
              <ul>
                <li>End-to-end encryption for data in transit and at rest</li>
                <li>Multi-factor authentication for account access</li>
                <li>Regular security audits and penetration testing</li>
                <li>SOC 2 Type II compliance</li>
                <li>Employee security training and background checks</li>
              </ul>
              
              <p><strong>Incident Response:</strong> We maintain an incident response plan and will notify you 
              of any data breaches as required by applicable law.</p>
              
              <p><strong>Data Retention:</strong> We retain your data only as long as necessary for service 
              provision and legal compliance. You can request deletion at any time.</p>
            </CardContent>
          </Card>

          {/* 5. Your Privacy Rights */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-orange-600" />
                <span>5. Your Privacy Rights</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <p><strong>Under {regional.framework}, you have the following rights:</strong></p>
              <ul>
                {regional.rights.map((right, index) => (
                  <li key={index}>{right}</li>
                ))}
              </ul>
              
              <p><strong>Exercising Your Rights:</strong></p>
              <ul>
                <li>Contact <NAME_EMAIL></li>
                <li>Use the privacy controls in your account settings</li>
                <li>Submit requests through our privacy portal</li>
              </ul>
              
              <p><strong>Response Time:</strong> We will respond to your requests within 
              {userRegion === 'EU' ? ' 30 days as required by GDPR' : ' 45 days as required by CCPA'}.</p>
            </CardContent>
          </Card>

          {/* 6. Cookies and Tracking */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Eye className="h-5 w-5 text-yellow-600" />
                <span>6. Cookies and Tracking</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <p><strong>Cookie Types:</strong></p>
              <ul>
                <li><strong>Essential:</strong> Required for service functionality</li>
                <li><strong>Analytics:</strong> Help us understand usage patterns</li>
                <li><strong>Preferences:</strong> Remember your settings and preferences</li>
                <li><strong>Marketing:</strong> Deliver relevant content (with consent)</li>
              </ul>
              
              <p><strong>Cookie Management:</strong> You can control cookies through your browser settings 
              or our cookie preference center. Note that disabling essential cookies may affect service functionality.</p>
              
              <p>For detailed information, see our <a href="/legal/cookie-policy" className="text-blue-600 hover:underline">Cookie Policy</a>.</p>
            </CardContent>
          </Card>

          {/* 7. International Transfers */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Globe className="h-5 w-5 text-indigo-600" />
                <span>7. International Data Transfers</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <p>
                We implement appropriate safeguards for international data transfers, including:
              </p>
              <ul>
                <li>Standard Contractual Clauses (SCCs) for EU data</li>
                <li>Adequacy decisions where applicable</li>
                <li>Data Processing Agreements with all vendors</li>
                <li>Regular compliance assessments</li>
              </ul>
              
              <p><strong>Data Controller:</strong> {regional.dataController}</p>
              <p><strong>Supervisory Authority:</strong> {regional.supervisoryAuthority}</p>
            </CardContent>
          </Card>

        </div>

        {/* Contact Information */}
        <div className="mt-12 text-center">
          <Separator className="mb-8" />
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Privacy Questions or Concerns?</h3>
            <div className="space-y-2">
              <p className="text-gray-600">
                <strong>Privacy Officer:</strong> <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline"><EMAIL></a>
              </p>
              <p className="text-gray-600">
                <strong>Data Protection Officer (EU):</strong> <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline"><EMAIL></a>
              </p>
            </div>
            <div className="flex items-center justify-center space-x-4 text-sm text-gray-500">
              <span>AiLex Legal Services</span>
              <span>•</span>
              <span>Last Updated: {lastUpdated.toLocaleDateString()}</span>
              <span>•</span>
              <span>Version 1.0</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
