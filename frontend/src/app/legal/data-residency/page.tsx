'use client'

import { useState, useEffect } from 'react'
import { Globe, Shield, Server, MapPin, Lock, Database, Calendar, CheckCircle } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { RegionalDisclaimerBanner } from '@/components/legal/regional-disclaimer-banner'

interface DataRegion {
  code: string
  name: string
  description: string
  dataCenter: string
  compliance: string[]
  flag: string
}

export default function DataResidencyPage() {
  const [userRegion, setUserRegion] = useState<string>('US')
  const [detectedRegion, setDetectedRegion] = useState<DataRegion | null>(null)
  const [lastUpdated] = useState(new Date('2024-12-01'))

  const dataRegions: DataRegion[] = [
    {
      code: 'US',
      name: 'United States',
      description: 'US-based users with data stored in US data centers',
      dataCenter: 'AWS US-East-1 (Virginia) and US-West-2 (Oregon)',
      compliance: ['SOC 2 Type II', 'CCPA', 'HIPAA (where applicable)', 'State Privacy Laws'],
      flag: '🇺🇸'
    },
    {
      code: 'EU',
      name: 'European Union',
      description: 'EU-based users with data stored in EU data centers',
      dataCenter: 'AWS EU-Central-1 (Frankfurt) and EU-West-1 (Ireland)',
      compliance: ['GDPR', 'ISO 27001', 'SOC 2 Type II', 'EU Data Protection Laws'],
      flag: '🇪🇺'
    },
    {
      code: 'UK',
      name: 'United Kingdom',
      description: 'UK-based users with data stored in EU data centers',
      dataCenter: 'AWS EU-West-2 (London) and EU-Central-1 (Frankfurt)',
      compliance: ['UK GDPR', 'Data Protection Act 2018', 'ISO 27001', 'SOC 2 Type II'],
      flag: '🇬🇧'
    },
    {
      code: 'CA',
      name: 'Canada',
      description: 'Canadian users with data stored in US data centers',
      dataCenter: 'AWS US-West-2 (Oregon) and CA-Central-1 (Canada)',
      compliance: ['PIPEDA', 'Provincial Privacy Laws', 'SOC 2 Type II'],
      flag: '🇨🇦'
    }
  ]

  useEffect(() => {
    // Simulate region detection
    const region = dataRegions.find(r => r.code === userRegion) || dataRegions[0]
    setDetectedRegion(region)
  }, [userRegion])

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <Globe className="h-8 w-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">Data Residency</h1>
          </div>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            How we ensure your data is processed and stored in compliance with regional data protection laws.
          </p>
          <div className="flex items-center justify-center space-x-4 mt-4">
            <div className="flex items-center space-x-2">
              <MapPin className="h-4 w-4 text-gray-500" />
              <Badge variant="outline" className="text-sm">
                Detected Region: {detectedRegion?.flag} {detectedRegion?.name}
              </Badge>
            </div>
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-600">
                Last Updated: {lastUpdated.toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>

        {/* Regional Disclaimer Banner */}
        <div className="mb-8">
          <RegionalDisclaimerBanner
            placement="footer"
            showRegionBadge={true}
            className="bg-white border border-gray-200 rounded-lg p-6"
          />
        </div>

        {/* Current Region Status */}
        {detectedRegion && (
          <Alert className="mb-8 border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">
              <strong>Your Data Region:</strong> {detectedRegion.flag} {detectedRegion.name} - 
              Your data is processed and stored in {detectedRegion.dataCenter} in compliance with applicable laws.
            </AlertDescription>
          </Alert>
        )}

        {/* Data Residency Content */}
        <div className="space-y-8">
          
          {/* 1. Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-blue-600" />
                <span>1. Data Residency Overview</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <p>
                AiLex implements comprehensive data residency controls to ensure your personal and 
                professional data is processed and stored in compliance with regional data protection laws.
              </p>
              <p>
                <strong>Key Principles:</strong>
              </p>
              <ul>
                <li><strong>Regional Processing:</strong> Data is processed in your geographic region</li>
                <li><strong>Compliance First:</strong> All processing complies with local data protection laws</li>
                <li><strong>Transparent Controls:</strong> Clear visibility into where your data is stored</li>
                <li><strong>User Control:</strong> You can request data location changes where legally permissible</li>
              </ul>
            </CardContent>
          </Card>

          {/* 2. Regional Data Centers */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Server className="h-5 w-5 text-green-600" />
                <span>2. Regional Data Centers</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-2">
                {dataRegions.map((region) => (
                  <div key={region.code} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center space-x-3 mb-3">
                      <span className="text-2xl">{region.flag}</span>
                      <div>
                        <h4 className="font-semibold text-gray-900">{region.name}</h4>
                        <p className="text-sm text-gray-600">{region.description}</p>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Server className="h-4 w-4 text-gray-500" />
                        <span className="text-sm text-gray-700">{region.dataCenter}</span>
                      </div>
                      
                      <div className="flex flex-wrap gap-1">
                        {region.compliance.map((standard) => (
                          <Badge key={standard} variant="secondary" className="text-xs">
                            {standard}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 3. Data Types and Processing */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Database className="h-5 w-5 text-purple-600" />
                <span>3. Data Types and Processing</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <p><strong>Personal Data:</strong></p>
              <ul>
                <li><strong>Account Information:</strong> Stored in your regional data center</li>
                <li><strong>Professional Credentials:</strong> Processed according to regional licensing requirements</li>
                <li><strong>Billing Data:</strong> Processed by regional payment processors</li>
                <li><strong>Communication Data:</strong> Stored in regional data centers with encryption</li>
              </ul>
              
              <p><strong>Professional Data:</strong></p>
              <ul>
                <li><strong>Legal Documents:</strong> Processed and stored in your region with attorney-client privilege protections</li>
                <li><strong>Case Information:</strong> Subject to regional confidentiality and professional responsibility rules</li>
                <li><strong>AI Interactions:</strong> Processed in-region with anonymization for model improvement</li>
                <li><strong>Usage Analytics:</strong> Aggregated and anonymized data may be processed globally</li>
              </ul>
            </CardContent>
          </Card>

          {/* 4. Cross-Border Transfers */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Globe className="h-5 w-5 text-orange-600" />
                <span>4. Cross-Border Data Transfers</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <p><strong>Limited Transfers:</strong></p>
              <p>
                We minimize cross-border data transfers. When transfers are necessary, we implement 
                appropriate safeguards:
              </p>
              <ul>
                <li><strong>EU to US:</strong> Standard Contractual Clauses (SCCs) and adequacy decisions</li>
                <li><strong>US to EU:</strong> GDPR compliance mechanisms and data processing agreements</li>
                <li><strong>Service Providers:</strong> All vendors must comply with regional data protection requirements</li>
                <li><strong>Emergency Access:</strong> Limited to legal compliance and security incidents</li>
              </ul>
              
              <p><strong>Transfer Safeguards:</strong></p>
              <ul>
                <li>Encryption in transit and at rest</li>
                <li>Access controls and audit logging</li>
                <li>Data minimization principles</li>
                <li>Regular compliance assessments</li>
              </ul>
            </CardContent>
          </Card>

          {/* 5. Compliance Frameworks */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Lock className="h-5 w-5 text-red-600" />
                <span>5. Compliance Frameworks</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <p><strong>Regional Compliance:</strong></p>
              
              <div className="grid gap-4 md:grid-cols-2">
                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900 mb-2">🇺🇸 United States</h4>
                  <ul className="text-sm space-y-1">
                    <li>• California Consumer Privacy Act (CCPA)</li>
                    <li>• Virginia Consumer Data Protection Act</li>
                    <li>• Colorado Privacy Act</li>
                    <li>• HIPAA (healthcare data)</li>
                    <li>• SOC 2 Type II</li>
                  </ul>
                </div>
                
                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900 mb-2">🇪🇺 European Union</h4>
                  <ul className="text-sm space-y-1">
                    <li>• General Data Protection Regulation (GDPR)</li>
                    <li>• ePrivacy Directive</li>
                    <li>• Digital Services Act</li>
                    <li>• ISO 27001</li>
                    <li>• SOC 2 Type II</li>
                  </ul>
                </div>
              </div>
              
              <p><strong>Professional Standards:</strong></p>
              <ul>
                <li>ABA Model Rules of Professional Conduct (US)</li>
                <li>EU Legal Services Directive</li>
                <li>Attorney-client privilege protections</li>
                <li>Professional confidentiality requirements</li>
              </ul>
            </CardContent>
          </Card>

          {/* 6. User Controls */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span>6. Your Data Residency Controls</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <p><strong>Available Controls:</strong></p>
              <ul>
                <li><strong>Region Verification:</strong> View your current data processing region</li>
                <li><strong>Data Location Requests:</strong> Request data location changes where legally permissible</li>
                <li><strong>Transfer Notifications:</strong> Receive notifications of any cross-border transfers</li>
                <li><strong>Compliance Reports:</strong> Access compliance documentation and certifications</li>
              </ul>
              
              <p><strong>How to Exercise Controls:</strong></p>
              <ul>
                <li>Contact our Data Protection <NAME_EMAIL></li>
                <li>Use the data residency controls in your account settings</li>
                <li>Submit requests through our privacy portal</li>
                <li>Contact customer support for assistance</li>
              </ul>
              
              <p><strong>Response Time:</strong> We will respond to data residency requests within 30 days.</p>
            </CardContent>
          </Card>

        </div>

        {/* Contact Information */}
        <div className="mt-12 text-center">
          <Separator className="mb-8" />
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Data Residency Questions?</h3>
            <div className="space-y-2">
              <p className="text-gray-600">
                <strong>Data Protection Officer:</strong> <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline"><EMAIL></a>
              </p>
              <p className="text-gray-600">
                <strong>Compliance Team:</strong> <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline"><EMAIL></a>
              </p>
            </div>
            <div className="flex items-center justify-center space-x-4 text-sm text-gray-500">
              <span>AiLex Legal Services</span>
              <span>•</span>
              <span>Last Updated: {lastUpdated.toLocaleDateString()}</span>
              <span>•</span>
              <span>Version 1.0</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
