'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts'
import { 
  Shield, 
  Scale, 
  Database, 
  Users, 
  Globe, 
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  FileText,
  Eye,
  Settings,
  Download,
  RefreshCw,
  ExternalLink
} from 'lucide-react'

// Types
interface ComplianceOverview {
  total_frameworks: number
  active_policies: number
  compliance_rate: number
  pending_reviews: number
  last_updated: string
}

interface FrameworkStatus {
  framework: string
  status: 'compliant' | 'warning' | 'violation'
  compliance_rate: number
  last_audit: string
  next_review: string
  active_policies: number
  pending_actions: number
}

interface ComplianceMetric {
  date: string
  framework: string
  events: number
  violations: number
  compliance_rate: number
}

interface RegionalCompliance {
  region: string
  frameworks: string[]
  compliance_rate: number
  data_residency_status: 'compliant' | 'warning'
  last_updated: string
}

export default function ComplianceDashboard() {
  const [loading, setLoading] = useState(true)
  const [overview, setOverview] = useState<ComplianceOverview | null>(null)
  const [frameworks, setFrameworks] = useState<FrameworkStatus[]>([])
  const [metrics, setMetrics] = useState<ComplianceMetric[]>([])
  const [regionalCompliance, setRegionalCompliance] = useState<RegionalCompliance[]>([])
  const [selectedTimeframe, setSelectedTimeframe] = useState('30d')

  useEffect(() => {
    loadDashboardData()
  }, [selectedTimeframe])

  const loadDashboardData = async () => {
    try {
      setLoading(true)

      // Load data from APIs
      const [overviewRes, frameworksRes, metricsRes, regionalRes] = await Promise.all([
        fetch('/api/compliance/overview'),
        fetch('/api/compliance/frameworks/status'),
        fetch(`/api/compliance/metrics?timeframe=${selectedTimeframe}`),
        fetch('/api/compliance/regional')
      ])

      if (overviewRes.ok) {
        const overviewData = await overviewRes.json()
        setOverview(overviewData)
      }

      if (frameworksRes.ok) {
        const frameworksData = await frameworksRes.json()
        setFrameworks(frameworksData)
      }

      if (metricsRes.ok) {
        const metricsData = await metricsRes.json()
        setMetrics(metricsData)
      }

      if (regionalRes.ok) {
        const regionalData = await regionalRes.json()
        setRegionalCompliance(regionalData)
      }

    } catch (error) {
      console.error('Error loading compliance dashboard:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'compliant': return 'text-green-600 bg-green-50 border-green-200'
      case 'warning': return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'violation': return 'text-red-600 bg-red-50 border-red-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'compliant': return <CheckCircle className="h-4 w-4" />
      case 'warning': return <AlertTriangle className="h-4 w-4" />
      case 'violation': return <AlertTriangle className="h-4 w-4" />
      default: return <Clock className="h-4 w-4" />
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Compliance Dashboard</h1>
        </div>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardHeader className="space-y-0 pb-2">
                <div className="h-4 bg-gray-200 rounded animate-pulse" />
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded animate-pulse" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Compliance Dashboard</h1>
          <p className="text-gray-600">Monitor compliance status across all frameworks and regions</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={loadDashboardData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      {overview && (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Frameworks</CardTitle>
              <Shield className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{overview.total_frameworks}</div>
              <p className="text-xs text-gray-600">Active compliance frameworks</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Policies</CardTitle>
              <FileText className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{overview.active_policies}</div>
              <p className="text-xs text-gray-600">Across all frameworks</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Compliance Rate</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{overview.compliance_rate}%</div>
              <Progress value={overview.compliance_rate} className="mt-2" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Reviews</CardTitle>
              <Clock className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{overview.pending_reviews}</div>
              <p className="text-xs text-gray-600">Require attention</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content Tabs */}
      <Tabs defaultValue="frameworks" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="frameworks">Frameworks</TabsTrigger>
          <TabsTrigger value="regional">Regional</TabsTrigger>
          <TabsTrigger value="metrics">Metrics</TabsTrigger>
          <TabsTrigger value="management">Management</TabsTrigger>
        </TabsList>

        {/* Frameworks Tab */}
        <TabsContent value="frameworks" className="space-y-6">
          <div className="grid gap-6">
            {frameworks.map((framework) => (
              <Card key={framework.framework}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Scale className="h-5 w-5 text-blue-600" />
                      <div>
                        <CardTitle className="text-lg">{framework.framework}</CardTitle>
                        <CardDescription>
                          {framework.active_policies} active policies • Last audit: {framework.last_audit}
                        </CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge className={getStatusColor(framework.status)}>
                        {getStatusIcon(framework.status)}
                        <span className="ml-1 capitalize">{framework.status}</span>
                      </Badge>
                      <span className="text-2xl font-bold text-green-600">
                        {framework.compliance_rate}%
                      </span>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <div className="text-sm text-gray-600">Compliance Rate</div>
                      <Progress value={framework.compliance_rate} className="mt-1" />
                    </div>
                    <div>
                      <div className="text-sm text-gray-600">Next Review</div>
                      <div className="font-medium">{framework.next_review}</div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-600">Pending Actions</div>
                      <div className="font-medium">
                        {framework.pending_actions > 0 ? (
                          <span className="text-yellow-600">{framework.pending_actions} pending</span>
                        ) : (
                          <span className="text-green-600">None</span>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Regional Tab */}
        <TabsContent value="regional" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {regionalCompliance.map((region) => (
              <Card key={region.region}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Globe className="h-5 w-5 text-blue-600" />
                      <div>
                        <CardTitle className="text-lg">{region.region}</CardTitle>
                        <CardDescription>
                          {region.frameworks.length} frameworks • Updated {region.last_updated}
                        </CardDescription>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-green-600">
                        {region.compliance_rate}%
                      </div>
                      <Badge className={getStatusColor(region.data_residency_status)}>
                        Data Residency
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div>
                      <div className="text-sm text-gray-600 mb-2">Active Frameworks</div>
                      <div className="flex flex-wrap gap-1">
                        {region.frameworks.map((framework) => (
                          <Badge key={framework} variant="secondary" className="text-xs">
                            {framework}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <Progress value={region.compliance_rate} className="mt-2" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Metrics Tab */}
        <TabsContent value="metrics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Compliance Trends</CardTitle>
              <CardDescription>
                Compliance rate and events over the last 30 days
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={metrics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Area 
                      type="monotone" 
                      dataKey="compliance_rate" 
                      stroke="#10b981" 
                      fill="#10b981" 
                      fillOpacity={0.1}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Management Tab */}
        <TabsContent value="management" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Database className="h-5 w-5 text-blue-600" />
                  <span>Data Retention</span>
                </CardTitle>
                <CardDescription>
                  Manage data retention policies and cleanup jobs
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Link href="/superadmin/compliance/data-retention">
                  <Button className="w-full">
                    <Settings className="h-4 w-4 mr-2" />
                    Manage Retention
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Scale className="h-5 w-5 text-green-600" />
                  <span>Legal Disclaimers</span>
                </CardTitle>
                <CardDescription>
                  Monitor regional disclaimer compliance
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Link href="/superadmin/compliance/disclaimers">
                  <Button className="w-full">
                    <Eye className="h-4 w-4 mr-2" />
                    View Disclaimers
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Users className="h-5 w-5 text-purple-600" />
                  <span>Consent Management</span>
                </CardTitle>
                <CardDescription>
                  Track user consent and preferences
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button className="w-full" disabled>
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Coming Soon
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
