import type { NextRequest} from 'next/server';
import { NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import type { Database } from '@/lib/supabase/database.types';

// Rate limit configurations (should match backend)
const RATE_LIMIT_CONFIG = {
  'checkout': {
    requests: 10,
    window: 300, // 5 minutes
    burst_requests: 3,
    burst_window: 60, // 1 minute
  },
  'payment-method': {
    requests: 20,
    window: 300, // 5 minutes
    burst_requests: 5,
    burst_window: 60, // 1 minute
  },
  'subscription': {
    requests: 5,
    window: 300, // 5 minutes
    burst_requests: 2,
    burst_window: 60, // 1 minute
  },
  'refund': {
    requests: 3,
    window: 600, // 10 minutes
    burst_requests: 1,
    burst_window: 300, // 5 minutes
  },
};

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const operation = searchParams.get('operation');

    if (!operation || !RATE_LIMIT_CONFIG[operation as keyof typeof RATE_LIMIT_CONFIG]) {
      return NextResponse.json(
        { error: 'Invalid operation parameter' },
        { status: 400 }
      );
    }

    const cookieStore = await cookies();

    // Create Supabase client
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );

    // Get the current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user tenant ID
    const tenantId = session.user.user_metadata?.tenant_id;
    const userId = session.user.id;

    // In a real implementation, this would call the backend rate limiter
    // For now, return mock data based on configuration
    const config = RATE_LIMIT_CONFIG[operation as keyof typeof RATE_LIMIT_CONFIG];
    const now = Math.floor(Date.now() / 1000);

    // Mock rate limit status (in production, this would query Redis)
    const mockStatus = {
      allowed: true,
      remaining: Math.floor(config.requests * 0.8), // Assume 80% remaining
      reset_time: now + config.window,
      limit_type: operation,
      retry_after: 0,
      normal: {
        current: Math.floor(config.requests * 0.2), // Assume 20% used
        limit: config.requests,
        remaining: Math.floor(config.requests * 0.8),
        window_seconds: config.window,
        reset_time: now + config.window
      },
      burst: {
        current: 0,
        limit: config.burst_requests,
        remaining: config.burst_requests,
        window_seconds: config.burst_window,
        reset_time: now + config.burst_window
      }
    };

    return NextResponse.json(mockStatus);

  } catch (error) {
    console.error('Error checking rate limit status:', error);
    return NextResponse.json(
      { error: 'Failed to check rate limit status' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { operation, action } = body;

    if (!operation || !RATE_LIMIT_CONFIG[operation as keyof typeof RATE_LIMIT_CONFIG]) {
      return NextResponse.json(
        { error: 'Invalid operation parameter' },
        { status: 400 }
      );
    }

    const cookieStore = await cookies();

    // Create Supabase client
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );

    // Get the current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Handle different actions
    switch (action) {
      case 'check':
        // Check if a request would be allowed without incrementing counters
        const config = RATE_LIMIT_CONFIG[operation as keyof typeof RATE_LIMIT_CONFIG];
        const now = Math.floor(Date.now() / 1000);

        // Mock check result (in production, this would query Redis without incrementing)
        const checkResult = {
          allowed: true,
          remaining: Math.floor(config.requests * 0.8),
          reset_time: now + config.window,
          limit_type: operation,
          retry_after: 0
        };

        return NextResponse.json(checkResult);

      case 'reset':
        // Reset rate limit (admin function)
        // This would typically require admin privileges
        const userRole = session.user.user_metadata?.role;
        if (userRole !== 'admin' && userRole !== 'superadmin') {
          return NextResponse.json(
            { error: 'Insufficient privileges' },
            { status: 403 }
          );
        }

        // In production, this would call the backend to reset rate limits
        return NextResponse.json({ 
          success: true, 
          message: `Rate limit reset for ${operation}` 
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action parameter' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error processing rate limit request:', error);
    return NextResponse.json(
      { error: 'Failed to process rate limit request' },
      { status: 500 }
    );
  }
}
