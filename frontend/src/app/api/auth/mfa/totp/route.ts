/**
 * Enhanced TOTP API Routes
 * 
 * API endpoints for enhanced TOTP authentication with advanced features
 * including device trust, backup codes, and security monitoring.
 */

import type { NextRequest} from 'next/server';
import { NextResponse } from 'next/server';
import { EnhancedTOTPService, TOTPError } from '@/lib/services/enhanced-totp-service';
import { createClient } from '@supabase/supabase-js';

const totpService = new EnhancedTOTPService();
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

/**
 * POST /api/auth/mfa/totp
 * Setup TOTP authentication for a user
 */
export async function POST(request: NextRequest) {
  try {
    const { userId, userEmail } = await request.json();

    // Validate required fields
    if (!userId || !userEmail) {
      return NextResponse.json(
        { 
          error: 'Missing required fields: userId and userEmail',
          error_code: 'MISSING_REQUIRED_FIELDS'
        },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(userEmail)) {
      return NextResponse.json(
        { 
          error: 'Invalid email format',
          error_code: 'INVALID_EMAIL_FORMAT'
        },
        { status: 400 }
      );
    }

    const setupResponse = await totpService.setupTOTP(userId, userEmail);

    return NextResponse.json({
      success: true,
      _data: setupResponse,
      message: 'TOTP setup initiated successfully'
    });

  } catch (error) {
    console.error('TOTP setup error:', error);

    if (error instanceof TOTPError) {
      return NextResponse.json(
        { 
          error: error.message,
          error_code: error.code
        },
        { status: error.statusCode }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to setup TOTP authentication',
        error_code: 'TOTP_SETUP_ERROR'
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/auth/mfa/totp?userId=xxx
 * Get TOTP status for a user
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { 
          error: 'Missing userId parameter',
          error_code: 'MISSING_USER_ID'
        },
        { status: 400 }
      );
    }

    const status = await totpService.getTOTPStatus(userId);

    return NextResponse.json({
      success: true,
      data: status
    });

  } catch (error) {
    console.error('TOTP status error:', error);

    if (error instanceof TOTPError) {
      return NextResponse.json(
        { 
          error: error.message,
          error_code: error.code
        },
        { status: error.statusCode }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to get TOTP status',
        error_code: 'TOTP_STATUS_ERROR'
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/auth/mfa/totp?userId=xxx
 * Disable TOTP for a user
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { 
          error: 'Missing userId parameter',
          error_code: 'MISSING_USER_ID'
        },
        { status: 400 }
      );
    }

    await totpService.disableTOTP(userId);

    return NextResponse.json({
      success: true,
      message: 'TOTP authentication disabled successfully'
    });

  } catch (error) {
    console.error('TOTP disable error:', error);

    if (error instanceof TOTPError) {
      return NextResponse.json(
        { 
          error: error.message,
          error_code: error.code
        },
        { status: error.statusCode }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to disable TOTP authentication',
        error_code: 'TOTP_DISABLE_ERROR'
      },
      { status: 500 }
    );
  }
}
