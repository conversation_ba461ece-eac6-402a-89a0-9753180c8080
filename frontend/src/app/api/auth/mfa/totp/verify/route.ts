/**
 * TOTP Verification API Routes
 * 
 * API endpoints for TOTP token verification including setup verification
 * and authentication verification with enhanced security features.
 */

import type { NextRequest} from 'next/server';
import { NextResponse } from 'next/server';
import { EnhancedTOTPService, TOTPError } from '@/lib/services/enhanced-totp-service';

const totpService = new EnhancedTOTPService();

/**
 * POST /api/auth/mfa/totp/verify
 * Verify TOTP token for setup or authentication
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      factorId, 
      token, 
      userId, 
      deviceName, 
      trustDevice, 
      verificationType = 'authentication' 
    } = body;

    // Validate required fields
    if (!token) {
      return NextResponse.json(
        { 
          error: 'Missing required field: token',
          error_code: 'MISSING_TOKEN'
        },
        { status: 400 }
      );
    }

    // Validate token format (6 digits)
    if (!/^\d{6}$/.test(token)) {
      return NextResponse.json(
        { 
          error: 'Invalid token format. Token must be 6 digits',
          error_code: 'INVALID_TOKEN_FORMAT'
        },
        { status: 400 }
      );
    }

    let verificationResult: boolean;

    if (verificationType === 'setup') {
      // Setup verification
      if (!factorId) {
        return NextResponse.json(
          { 
            error: 'Missing required field: factorId for setup verification',
            error_code: 'MISSING_FACTOR_ID'
          },
          { status: 400 }
        );
      }

      verificationResult = await totpService.verifyTOTPSetup({
        factorId,
        token,
        deviceName,
        trustDevice
      });

      if (verificationResult) {
        return NextResponse.json({
          success: true,
          message: 'TOTP setup verified successfully',
          data: {
            verified: true,
            setupComplete: true,
            deviceTrusted: trustDevice || false
          }
        });
      } else {
        return NextResponse.json(
          { 
            error: 'Invalid TOTP token for setup verification',
            error_code: 'INVALID_SETUP_TOKEN'
          },
          { status: 400 }
        );
      }

    } else {
      // Authentication verification
      if (!userId) {
        return NextResponse.json(
          { 
            error: 'Missing required field: userId for authentication verification',
            error_code: 'MISSING_USER_ID'
          },
          { status: 400 }
        );
      }

      verificationResult = await totpService.verifyTOTPToken(userId, token);

      if (verificationResult) {
        // Get client IP and user agent for security logging
        const clientIP = request.headers.get('x-forwarded-for') || 
                        request.headers.get('x-real-ip') || 
                        'unknown';
        const userAgent = request.headers.get('user-agent') || 'unknown';

        return NextResponse.json({
          success: true,
          message: 'TOTP token verified successfully',
          data: {
            verified: true,
            timestamp: new Date().toISOString(),
            clientInfo: {
              ip: clientIP,
              userAgent: userAgent
            }
          }
        });
      } else {
        return NextResponse.json(
          { 
            error: 'Invalid TOTP token',
            error_code: 'INVALID_TOKEN'
          },
          { status: 401 }
        );
      }
    }

  } catch (error) {
    console.error('TOTP verification error:', error);

    if (error instanceof TOTPError) {
      return NextResponse.json(
        { 
          error: error.message,
          error_code: error.code
        },
        { status: error.statusCode }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to verify TOTP token',
        error_code: 'TOTP_VERIFICATION_ERROR'
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/auth/mfa/totp/verify?userId=xxx&token=xxx
 * Quick TOTP token verification (for simple authentication flows)
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const token = searchParams.get('token');

    if (!userId || !token) {
      return NextResponse.json(
        { 
          error: 'Missing required parameters: userId and token',
          error_code: 'MISSING_PARAMETERS'
        },
        { status: 400 }
      );
    }

    // Validate token format
    if (!/^\d{6}$/.test(token)) {
      return NextResponse.json(
        { 
          error: 'Invalid token format. Token must be 6 digits',
          error_code: 'INVALID_TOKEN_FORMAT'
        },
        { status: 400 }
      );
    }

    const verificationResult = await totpService.verifyTOTPToken(userId, token);

    return NextResponse.json({
      success: true,
      data: {
        verified: verificationResult,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('TOTP quick verification error:', error);

    if (error instanceof TOTPError) {
      return NextResponse.json(
        { 
          error: error.message,
          error_code: error.code
        },
        { status: error.statusCode }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to verify TOTP token',
        error_code: 'TOTP_VERIFICATION_ERROR'
      },
      { status: 500 }
    );
  }
}
