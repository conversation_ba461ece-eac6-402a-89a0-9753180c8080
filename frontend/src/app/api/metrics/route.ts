/**
 * Prometheus Metrics Endpoint
 * 
 * Exposes payment and system metrics in Prometheus format for scraping
 * by monitoring systems like Prometheus, Grafana, or DataDog.
 */

import type { NextRequest} from 'next/server';
import { NextResponse } from 'next/server';
import { paymentMetrics } from '@/lib/monitoring/payment-prometheus-metrics';

/**
 * GET /api/metrics
 * 
 * Returns metrics in Prometheus format for scraping
 */
export async function GET(request: NextRequest) {
  try {
    // Check for basic authentication or API key if needed
    const authHeader = request.headers.get('authorization');
    const apiKey = request.headers.get('x-api-key');
    
    // In production, you might want to secure this endpoint
    // For now, we'll allow access but could add authentication
    if (process.env.NODE_ENV === 'production') {
      const expectedApiKey = process.env.METRICS_API_KEY;
      if (expectedApiKey && apiKey !== expectedApiKey) {
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }
    }
    
    // Get metrics in Prometheus format
    const metricsOutput = paymentMetrics.getPrometheusMetrics();
    
    // Add system metrics
    const systemMetrics = generateSystemMetrics();
    const fullMetrics = metricsOutput + systemMetrics;
    
    // Return metrics with proper content type
    return new NextResponse(fullMetrics, {
      status: 200,
      headers: {
        'Content-Type': 'text/plain; version=0.0.4; charset=utf-8',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
    
  } catch (error) {
    console.error('Error generating metrics:', error);
    return NextResponse.json(
      { error: 'Failed to generate metrics' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/metrics
 * 
 * Accepts custom metrics from client-side or other services
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate the metric data
    if (!body.metric_name || !body.metric_type || body.value === undefined) {
      return NextResponse.json(
        { error: 'Invalid metric data. Required: metric_name, metric_type, value' },
        { status: 400 }
      );
    }
    
    // Process custom metrics based on type
    switch (body.metric_type) {
      case 'payment_method_creation':
        paymentMetrics.recordPaymentMethodCreation(
          body.payment_method,
          body.region,
          body.currency,
          body.success,
          body.duration_ms,
          body.tenant_id
        );
        break;
        
      case 'payment_processing':
        paymentMetrics.recordPaymentProcessing(
          body.payment_method,
          body.region,
          body.currency,
          body.amount_cents,
          body.success,
          body.duration_ms,
          body.tenant_id
        );
        break;
        
      case 'payment_error':
        paymentMetrics.recordPaymentError(
          body.payment_method,
          body.region,
          body.currency,
          body.error_code,
          body.tenant_id
        );
        break;
        
      case 'compliance_violation':
        paymentMetrics.recordComplianceViolation(
          body.framework,
          body.violation_type,
          body.severity,
          body.region,
          body.tenant_id
        );
        break;
        
      case 'webhook_processing':
        paymentMetrics.recordWebhookProcessing(
          body.event_type,
          body.success,
          body.duration_ms,
          body.tenant_id
        );
        break;
        
      default:
        return NextResponse.json(
          { error: `Unsupported metric type: ${body.metric_type}` },
          { status: 400 }
        );
    }
    
    return NextResponse.json({
      success: true,
      message: 'Metric recorded successfully'
    });
    
  } catch (error) {
    console.error('Error recording custom metric:', error);
    return NextResponse.json(
      { error: 'Failed to record metric' },
      { status: 500 }
    );
  }
}

/**
 * Generate system-level metrics
 */
function generateSystemMetrics(): string {
  const now = Date.now();
  const uptime = process.uptime();
  const memoryUsage = process.memoryUsage();
  
  return `
# HELP nodejs_process_uptime_seconds Process uptime in seconds
# TYPE nodejs_process_uptime_seconds gauge
nodejs_process_uptime_seconds ${uptime}

# HELP nodejs_memory_usage_bytes Memory usage in bytes
# TYPE nodejs_memory_usage_bytes gauge
nodejs_memory_usage_bytes{type="rss"} ${memoryUsage.rss}
nodejs_memory_usage_bytes{type="heapTotal"} ${memoryUsage.heapTotal}
nodejs_memory_usage_bytes{type="heapUsed"} ${memoryUsage.heapUsed}
nodejs_memory_usage_bytes{type="external"} ${memoryUsage.external}

# HELP pi_lawyer_ai_build_info Build information
# TYPE pi_lawyer_ai_build_info gauge
pi_lawyer_ai_build_info{version="${process.env.NEXT_PUBLIC_APP_VERSION || 'unknown'}",environment="${process.env.NODE_ENV || 'unknown'}"} 1

# HELP pi_lawyer_ai_metrics_scrape_timestamp_seconds Timestamp of metrics scrape
# TYPE pi_lawyer_ai_metrics_scrape_timestamp_seconds gauge
pi_lawyer_ai_metrics_scrape_timestamp_seconds ${now / 1000}

`;
}

/**
 * OPTIONS /api/metrics
 * 
 * Handle CORS preflight requests
 */
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    },
  });
}
