/**
 * SEPA Direct Debit API Routes
 *
 * API endpoints for SEPA Direct Debit payment method management
 * including mandate creation, payment processing, and compliance.
 *
 * SECURITY: Requires JWT authentication and tenant validation
 */

import type { NextRequest} from 'next/server';
import { NextResponse } from 'next/server';
import { SEPADirectDebitService } from '@/lib/services/sepa-direct-debit-service';
import type {
  SEPAPaymentMethodRequest} from '@/lib/services/sepa-direct-debit-service';
import {
  SEPAPaymentRequest
} from '@/lib/services/sepa-direct-debit-service';
import type {
  SupportedRegion} from '@/lib/types/payment-methods';
import {
  PaymentMethodError,
  StripeIntegrationError,
  SupportedCurrency
} from '@/lib/types/payment-methods';
import { withPaymentAuth, addPaymentSecurityHeaders } from '@/lib/auth/payment-auth';

const sepaService = new SEPADirectDebitService();

// SEPA countries that support SEPA Direct Debit
const SEPA_COUNTRIES: SupportedRegion[] = [
  'BE', 'DE', 'FR', 'NL', 'IT', 'ES', 'AT', 'EU'
];

/**
 * POST /api/payment-methods/sepa
 * Create SEPA Direct Debit payment method with mandate
 * SECURITY: Requires JWT authentication, API key, and tenant validation
 */
export const POST = withPaymentAuth(
  async (request: NextRequest, { user, supabase }) => {
    try {
      const body = await request.json();

      // Validate required fields
      const requiredFields = [
        'iban', 'account_holder_name', 'customer_email',
        'customer_name', 'country_code', 'currency_code', 'tenant_id'
      ];

      for (const field of requiredFields) {
        if (!body[field]) {
          return NextResponse.json(
            {
              error: `Missing required field: ${field}`,
              code: 'MISSING_REQUIRED_FIELD'
            },
            { status: 400 }
          );
        }
      }

      // Verify tenant access
      if (body.tenant_id !== user.tenantId && user.role !== 'super_admin') {
        return NextResponse.json(
          {
            error: 'Access denied to requested tenant',
            code: 'TENANT_ACCESS_DENIED'
          },
          { status: 403 }
        );
      }

      // Validate SEPA country
      if (!SEPA_COUNTRIES.includes(body.country_code)) {
        return NextResponse.json(
          {
            error: `SEPA Direct Debit not available in ${body.country_code}`,
            code: 'SEPA_NOT_AVAILABLE',
            available_countries: SEPA_COUNTRIES
          },
          { status: 400 }
        );
      }

    // Validate currency (SEPA only supports EUR)
    if (body.currency_code !== 'EUR') {
      return NextResponse.json(
        { 
          error: 'SEPA Direct Debit only supports EUR currency',
          error_code: 'INVALID_CURRENCY',
          supported_currency: 'EUR'
        },
        { status: 400 }
      );
    }

    // Extract client IP and user agent for mandate compliance
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    const sepaRequest: SEPAPaymentMethodRequest = {
      iban: body.iban,
      bic: body.bic,
      account_holder_name: body.account_holder_name,
      customer_email: body.customer_email,
      customer_name: body.customer_name,
      country_code: body.country_code,
      currency_code: body.currency_code,
      tenant_id: body.tenant_id,
      mandate_acceptance_type: 'online',
      mandate_acceptance_ip: clientIP,
      mandate_acceptance_user_agent: userAgent
    };

    const result = await sepaService.createSEPAPaymentMethod(sepaRequest);

    return NextResponse.json({
      success: true,
      _data: result,
      message: 'SEPA Direct Debit payment method created successfully'
    });

  } catch (error) {
    console.error('SEPA payment method creation error:', error);

    if (error instanceof PaymentMethodError) {
      return NextResponse.json(
        { 
          error: error.message,
          error_code: error.code,
          country_code: error.country_code
        },
        { status: 400 }
      );
    }

    if (error instanceof StripeIntegrationError) {
      return NextResponse.json(
        { 
          error: error.message,
          error_code: error.code,
          stripe_error_type: error.stripe_error_type
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to create SEPA payment method',
        error_code: 'INTERNAL_SERVER_ERROR'
      },
      { status: 500 }
    );
  }
});

/**
 * GET /api/payment-methods/sepa?mandate_id=xxx
 * Get SEPA mandate status
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const mandateId = searchParams.get('mandate_id');

    if (!mandateId) {
      return NextResponse.json(
        { 
          error: 'Missing mandate_id parameter',
          error_code: 'MISSING_MANDATE_ID'
        },
        { status: 400 }
      );
    }

    const mandate = await sepaService.getMandateStatus(mandateId);

    return NextResponse.json({
      success: true,
      data: mandate
    });

  } catch (error) {
    console.error('SEPA mandate retrieval error:', error);

    if (error instanceof PaymentMethodError) {
      return NextResponse.json(
        { 
          error: error.message,
          error_code: error.code
        },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to retrieve SEPA mandate',
        error_code: 'INTERNAL_SERVER_ERROR'
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/payment-methods/sepa?mandate_id=xxx
 * Cancel SEPA mandate
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const mandateId = searchParams.get('mandate_id');

    if (!mandateId) {
      return NextResponse.json(
        { 
          error: 'Missing mandate_id parameter',
          error_code: 'MISSING_MANDATE_ID'
        },
        { status: 400 }
      );
    }

    await sepaService.cancelMandate(mandateId);

    return NextResponse.json({
      success: true,
      message: 'SEPA mandate cancelled successfully'
    });

  } catch (error) {
    console.error('SEPA mandate cancellation error:', error);

    if (error instanceof PaymentMethodError) {
      return NextResponse.json(
        { 
          error: error.message,
          error_code: error.code
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to cancel SEPA mandate',
        error_code: 'INTERNAL_SERVER_ERROR'
      },
      { status: 500 }
    );
  }
}
