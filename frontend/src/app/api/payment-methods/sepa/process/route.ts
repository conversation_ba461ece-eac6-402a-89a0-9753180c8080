/**
 * SEPA Direct Debit Payment Processing API
 * 
 * API endpoint for processing SEPA Direct Debit payments
 * with proper mandate validation and compliance.
 */

import type { NextRequest} from 'next/server';
import { NextResponse } from 'next/server';
import type { SEPAPaymentRequest } from '@/lib/services/sepa-direct-debit-service';
import { SEPADirectDebitService } from '@/lib/services/sepa-direct-debit-service';
import { PaymentMethodError, StripeIntegrationError } from '@/lib/types/payment-methods';

const sepaService = new SEPADirectDebitService();

/**
 * POST /api/payment-methods/sepa/process
 * Process SEPA Direct Debit payment
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    const requiredFields = [
      'payment_method_id', 'amount_cents', 'currency', 'tenant_id'
    ];
    
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { 
            error: `Missing required field: ${field}`,
            error_code: 'MISSING_REQUIRED_FIELD'
          },
          { status: 400 }
        );
      }
    }

    // Validate amount
    if (typeof body.amount_cents !== 'number' || body.amount_cents <= 0) {
      return NextResponse.json(
        { 
          error: 'Amount must be a positive number',
          error_code: 'INVALID_AMOUNT'
        },
        { status: 400 }
      );
    }

    // Validate currency (SEPA only supports EUR)
    if (body.currency !== 'EUR') {
      return NextResponse.json(
        { 
          error: 'SEPA Direct Debit only supports EUR currency',
          error_code: 'INVALID_CURRENCY',
          supported_currency: 'EUR'
        },
        { status: 400 }
      );
    }

    // Validate minimum amount (€0.50 for SEPA)
    if (body.amount_cents < 50) {
      return NextResponse.json(
        { 
          error: 'Minimum amount for SEPA Direct Debit is €0.50',
          error_code: 'AMOUNT_TOO_LOW',
          minimum_amount_cents: 50
        },
        { status: 400 }
      );
    }

    // Validate maximum amount (€999,999.99 for SEPA)
    if (body.amount_cents > 99999999) {
      return NextResponse.json(
        { 
          error: 'Maximum amount for SEPA Direct Debit is €999,999.99',
          error_code: 'AMOUNT_TOO_HIGH',
          maximum_amount_cents: 99999999
        },
        { status: 400 }
      );
    }

    const paymentRequest: SEPAPaymentRequest = {
      payment_method_id: body.payment_method_id,
      amount_cents: body.amount_cents,
      currency: body.currency,
      tenant_id: body.tenant_id,
      description: body.description,
      metadata: body.metadata
    };

    const result = await sepaService.processSEPAPayment(paymentRequest);

    // Return appropriate response based on payment status
    if (result.status === 'succeeded') {
      return NextResponse.json({
        success: true,
        data: result,
        message: 'SEPA Direct Debit payment processed successfully'
      });
    } else if (result.status === 'requires_action') {
      return NextResponse.json({
        success: true,
        data: result,
        message: 'SEPA Direct Debit payment requires additional action',
        requires_action: true
      });
    } else if (result.status === 'processing') {
      return NextResponse.json({
        success: true,
        data: result,
        message: 'SEPA Direct Debit payment is being processed'
      });
    } else {
      return NextResponse.json({
        success: false,
        data: result,
        message: 'SEPA Direct Debit payment failed',
        error: result.error_message
      }, { status: 400 });
    }

  } catch (error) {
    console.error('SEPA payment processing error:', error);

    if (error instanceof PaymentMethodError) {
      return NextResponse.json(
        { 
          error: error.message,
          error_code: error.code,
          country_code: error.country_code
        },
        { status: 400 }
      );
    }

    if (error instanceof StripeIntegrationError) {
      return NextResponse.json(
        { 
          error: error.message,
          error_code: error.code,
          stripe_error_type: error.stripe_error_type
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to process SEPA payment',
        error_code: 'INTERNAL_SERVER_ERROR'
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/payment-methods/sepa/process?payment_intent_id=xxx
 * Get SEPA payment status
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const paymentIntentId = searchParams.get('payment_intent_id');

    if (!paymentIntentId) {
      return NextResponse.json(
        { 
          error: 'Missing payment_intent_id parameter',
          error_code: 'MISSING_PAYMENT_INTENT_ID'
        },
        { status: 400 }
      );
    }

    // Initialize Stripe to get payment intent status
    const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

    return NextResponse.json({
      success: true,
      data: {
        payment_intent_id: paymentIntent.id,
        status: paymentIntent.status,
        amount_cents: paymentIntent.amount,
        currency: paymentIntent.currency.toUpperCase(),
        created: paymentIntent.created,
        last_payment_error: paymentIntent.last_payment_error,
        next_action: paymentIntent.next_action,
        metadata: paymentIntent.metadata
      }
    });

  } catch (error) {
    console.error('SEPA payment status retrieval error:', error);

    if (error && typeof error === 'object' && 'type' in error && error.type === 'StripeInvalidRequestError') {
      return NextResponse.json(
        { 
          error: 'Payment intent not found',
          error_code: 'PAYMENT_INTENT_NOT_FOUND'
        },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to retrieve SEPA payment status',
        error_code: 'INTERNAL_SERVER_ERROR'
      },
      { status: 500 }
    );
  }
}
