/**
 * Detailed Health Check API
 * 
 * Comprehensive health monitoring for production deployment validation
 * Includes payment system, database, external services, and security checks
 */

import type { NextRequest} from 'next/server';
import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import <PERSON><PERSON> from 'stripe';

interface DetailedHealthCheck {
  service: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  response_time_ms: number;
  details: Record<string, unknown>;
  error?: string;
  recommendations?: string[];
}

interface SystemHealthReport {
  overall_status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  version: string;
  environment: string;
  uptime_seconds: number;
  deployment_id: string;
  checks: DetailedHealthCheck[];
  summary: {
    total_checks: number;
    healthy_checks: number;
    degraded_checks: number;
    unhealthy_checks: number;
    critical_issues: string[];
    warnings: string[];
  };
  performance_metrics: {
    avg_response_time: number;
    slowest_service: string;
    fastest_service: string;
  };
}

class DetailedHealthChecker {
  private startTime: number;
  private deploymentId: string;

  constructor() {
    this.startTime = Date.now();
    this.deploymentId = process.env.DEPLOYMENT_ID || 'unknown';
  }

  /**
   * Check database health with detailed metrics
   */
  async checkDatabaseHealth(): Promise<DetailedHealthCheck> {
    const start = Date.now();
    const service = 'database';
    
    try {
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
      const supabaseKey = process.env.SUPABASE_SERVICE_KEY;
      
      if (!supabaseUrl || !supabaseKey) {
        return {
          service,
          status: 'unhealthy',
          timestamp: new Date().toISOString(),
          response_time_ms: Date.now() - start,
          details: { error: 'Missing Supabase configuration' },
          error: 'Supabase URL or service key not configured',
          recommendations: ['Configure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_KEY']
        };
      }

      const supabase = createClient(supabaseUrl, supabaseKey);
      
      // Test multiple database operations
      const checks = await Promise.all([
        // Basic connectivity
        supabase.from('subscription_plans').select('count').limit(1),
        // Authentication check
        supabase.auth.getSession(),
        // RLS policy check
        supabase.from('tenants').select('id').limit(1)
      ]);

      const [plansResult, authResult, tenantsResult] = checks;
      const responseTime = Date.now() - start;

      const details = {
        connectivity: plansResult.error ? 'failed' : 'success',
        auth_service: authResult.error ? 'failed' : 'success',
        rls_policies: tenantsResult.error ? 'failed' : 'success',
        response_time_ms: responseTime,
        supabase_url: supabaseUrl,
        connection_pool: 'active'
      };

      const errors = [plansResult.error, authResult.error, tenantsResult.error].filter(Boolean);
      
      let status: 'healthy' | 'degraded' | 'unhealthy';
      if (errors.length === 0) {
        status = responseTime < 1000 ? 'healthy' : 'degraded';
      } else if (errors.length < 3) {
        status = 'degraded';
      } else {
        status = 'unhealthy';
      }

      const recommendations = [];
      if (responseTime > 1000) {
        recommendations.push('Database response time is slow, consider optimizing queries or scaling');
      }
      if (errors.length > 0) {
        recommendations.push('Some database operations failed, check RLS policies and permissions');
      }

      return {
        service,
        status,
        timestamp: new Date().toISOString(),
        response_time_ms: responseTime,
        details,
        error: errors.length > 0 ? errors.map(e => e?.message).join('; ') : undefined,
        recommendations: recommendations.length > 0 ? recommendations : undefined
      };

    } catch (error) {
      return {
        service,
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        response_time_ms: Date.now() - start,
        details: { error: 'Database connection failed' },
        error: error instanceof Error ? error.message : 'Unknown database error',
        recommendations: ['Check database connectivity and credentials']
      };
    }
  }

  /**
   * Check Stripe payment system health
   */
  async checkStripeHealth(): Promise<DetailedHealthCheck> {
    const start = Date.now();
    const service = 'stripe_payment_system';
    
    try {
      const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
      const stripePublishableKey = process.env.STRIPE_PUBLISHABLE_KEY;
      const stripeWebhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
      
      if (!stripeSecretKey || !stripePublishableKey) {
        return {
          service,
          status: 'unhealthy',
          timestamp: new Date().toISOString(),
          response_time_ms: Date.now() - start,
          details: { error: 'Missing Stripe configuration' },
          error: 'Stripe keys not configured',
          recommendations: ['Configure STRIPE_SECRET_KEY and STRIPE_PUBLISHABLE_KEY']
        };
      }

      const stripe = new Stripe(stripeSecretKey, { apiVersion: '2025-06-30.basil' });
      
      // Test multiple Stripe operations
      const [account, balance, paymentMethods] = await Promise.all([
        stripe.accounts.retrieve(),
        stripe.balance.retrieve(),
        stripe.paymentMethods.list({ type: 'card', limit: 1 })
      ]);

      const responseTime = Date.now() - start;
      
      const details = {
        account_id: account.id,
        country: account.country,
        charges_enabled: account.charges_enabled,
        payouts_enabled: account.payouts_enabled,
        available_balance: balance.available[0]?.amount || 0,
        currency: balance.available[0]?.currency || 'usd',
        webhook_configured: !!stripeWebhookSecret,
        key_type: stripeSecretKey.startsWith('sk_live_') ? 'live' : 'test',
        api_version: '2023-10-16'
      };

      const recommendations = [];
      if (!account.charges_enabled) {
        recommendations.push('Stripe account charges not enabled');
      }
      if (!account.payouts_enabled) {
        recommendations.push('Stripe account payouts not enabled');
      }
      if (!stripeWebhookSecret) {
        recommendations.push('Stripe webhook secret not configured');
      }
      if (stripeSecretKey.startsWith('sk_test_')) {
        recommendations.push('Using Stripe test keys in production environment');
      }

      const status = recommendations.length === 0 ? 'healthy' : 
                   recommendations.length <= 2 ? 'degraded' : 'unhealthy';

      return {
        service,
        status,
        timestamp: new Date().toISOString(),
        response_time_ms: responseTime,
        details,
        recommendations: recommendations.length > 0 ? recommendations : undefined
      };

    } catch (error) {
      return {
        service,
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        response_time_ms: Date.now() - start,
        details: { error: 'Stripe API connection failed' },
        error: error instanceof Error ? error.message : 'Unknown Stripe error',
        recommendations: ['Check Stripe API keys and network connectivity']
      };
    }
  }

  /**
   * Check payment encryption system
   */
  async checkPaymentEncryption(): Promise<DetailedHealthCheck> {
    const start = Date.now();
    const service = 'payment_encryption';
    
    try {
      const encryptionKey = process.env.PAYMENT_ENCRYPTION_KEY;
      const masterKey = process.env.PAYMENT_MASTER_KEY;
      const apiKey = process.env.PAYMENT_API_KEY;
      
      const details: Record<string, unknown> = {
        encryption_key_configured: !!encryptionKey,
        master_key_configured: !!masterKey,
        api_key_configured: !!apiKey,
        encryption_key_length: encryptionKey?.length || 0,
        master_key_length: masterKey?.length || 0
      };

      const recommendations = [];
      
      if (!encryptionKey) {
        recommendations.push('Payment encryption key not configured');
      } else if (encryptionKey.length !== 64) {
        recommendations.push('Payment encryption key should be 64 hex characters');
      }
      
      if (!masterKey) {
        recommendations.push('Payment master key not configured');
      } else if (masterKey.length < 32) {
        recommendations.push('Payment master key should be at least 32 characters');
      }
      
      if (!apiKey) {
        recommendations.push('Payment API key not configured');
      }

      // Test encryption functionality if keys are available
      if (encryptionKey && masterKey) {
        try {
          const crypto = require('crypto');
          const testData = 'health-check-encryption-test';
          
          // Test encryption/decryption
          const cipher = crypto.createCipher('aes-256-cbc', encryptionKey);
          let encrypted = cipher.update(testData, 'utf8', 'hex');
          encrypted += cipher.final('hex');
          
          const decipher = crypto.createDecipher('aes-256-cbc', encryptionKey);
          let decrypted = decipher.update(encrypted, 'hex', 'utf8');
          decrypted += decipher.final('utf8');
          
          details.encryption_test = decrypted === testData ? 'passed' : 'failed';
          
          if (decrypted !== testData) {
            recommendations.push('Encryption/decryption test failed');
          }
        } catch (encError) {
          details.encryption_test = 'error';
          recommendations.push('Encryption test threw an error');
        }
      }

      const status = recommendations.length === 0 ? 'healthy' : 
                   recommendations.length <= 2 ? 'degraded' : 'unhealthy';

      return {
        service,
        status,
        timestamp: new Date().toISOString(),
        response_time_ms: Date.now() - start,
        details,
        recommendations: recommendations.length > 0 ? recommendations : undefined
      };

    } catch (error) {
      return {
        service,
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        response_time_ms: Date.now() - start,
        details: { error: 'Payment encryption check failed' },
        error: error instanceof Error ? error.message : 'Unknown encryption error',
        recommendations: ['Check payment encryption configuration']
      };
    }
  }

  /**
   * Check external API dependencies
   */
  async checkExternalAPIs(): Promise<DetailedHealthCheck> {
    const start = Date.now();
    const service = 'external_apis';
    
    try {
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_API_URL;
      const lawsApiUrl = process.env.NEXT_PUBLIC_LAWS_API_BASE;
      
      const apiChecks = [];
      
      // Check backend API
      if (backendUrl) {
        try {
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 5000);
          
          const response = await fetch(`${backendUrl}/health`, {
            signal: controller.signal,
            method: 'GET'
          });
          
          clearTimeout(timeoutId);
          
          apiChecks.push({
            name: 'backend_api',
            url: backendUrl,
            status: response.ok ? 'healthy' : 'degraded',
            response_code: response.status,
            response_time: Date.now() - start
          });
        } catch (error) {
          apiChecks.push({
            name: 'backend_api',
            url: backendUrl,
            status: 'unhealthy',
            error: error instanceof Error ? error.message : 'Connection failed'
          });
        }
      }

      // Check laws API
      if (lawsApiUrl) {
        try {
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 5000);
          
          const response = await fetch(`${lawsApiUrl}/health`, {
            signal: controller.signal,
            method: 'GET'
          });
          
          clearTimeout(timeoutId);
          
          apiChecks.push({
            name: 'laws_api',
            url: lawsApiUrl,
            status: response.ok ? 'healthy' : 'degraded',
            response_code: response.status,
            response_time: Date.now() - start
          });
        } catch (error) {
          apiChecks.push({
            name: 'laws_api',
            url: lawsApiUrl,
            status: 'unhealthy',
            error: error instanceof Error ? error.message : 'Connection failed'
          });
        }
      }

      const unhealthyCount = apiChecks.filter(api => api.status === 'unhealthy').length;
      const degradedCount = apiChecks.filter(api => api.status === 'degraded').length;
      
      let status: 'healthy' | 'degraded' | 'unhealthy';
      if (unhealthyCount > 0) {
        status = 'unhealthy';
      } else if (degradedCount > 0) {
        status = 'degraded';
      } else {
        status = 'healthy';
      }

      const recommendations: string[] = [];
      apiChecks.forEach(api => {
        if (api.status === 'unhealthy') {
          recommendations.push(`${api.name} is not accessible: ${api.error}`);
        } else if (api.status === 'degraded') {
          recommendations.push(`${api.name} returned HTTP ${api.response_code}`);
        }
      });

      return {
        service,
        status,
        timestamp: new Date().toISOString(),
        response_time_ms: Date.now() - start,
        details: {
          total_apis: apiChecks.length,
          healthy_apis: apiChecks.filter(api => api.status === 'healthy').length,
          api_checks: apiChecks
        },
        recommendations: recommendations.length > 0 ? recommendations : undefined
      };

    } catch (error) {
      return {
        service,
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        response_time_ms: Date.now() - start,
        details: { error: 'External API check failed' },
        error: error instanceof Error ? error.message : 'Unknown external API error',
        recommendations: ['Check external API connectivity and configuration']
      };
    }
  }

  /**
   * Run all detailed health checks
   */
  async runDetailedHealthChecks(): Promise<SystemHealthReport> {
    const checks = await Promise.all([
      this.checkDatabaseHealth(),
      this.checkStripeHealth(),
      this.checkPaymentEncryption(),
      this.checkExternalAPIs()
    ]);

    const healthyCount = checks.filter(c => c.status === 'healthy').length;
    const degradedCount = checks.filter(c => c.status === 'degraded').length;
    const unhealthyCount = checks.filter(c => c.status === 'unhealthy').length;

    let overallStatus: 'healthy' | 'degraded' | 'unhealthy';
    if (unhealthyCount > 0) {
      overallStatus = 'unhealthy';
    } else if (degradedCount > 0) {
      overallStatus = 'degraded';
    } else {
      overallStatus = 'healthy';
    }

    // Collect critical issues and warnings
    const criticalIssues: string[] = [];
    const warnings: string[] = [];

    checks.forEach(check => {
      if (check.status === 'unhealthy' && check.error) {
        criticalIssues.push(`${check.service}: ${check.error}`);
      }
      if (check.recommendations) {
        warnings.push(...check.recommendations.map(rec => `${check.service}: ${rec}`));
      }
    });

    // Calculate performance metrics
    const responseTimes = checks.map(c => c.response_time_ms);
    const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
    const slowestCheck = checks.reduce((prev, current) => 
      prev.response_time_ms > current.response_time_ms ? prev : current
    );
    const fastestCheck = checks.reduce((prev, current) => 
      prev.response_time_ms < current.response_time_ms ? prev : current
    );

    return {
      overall_status: overallStatus,
      timestamp: new Date().toISOString(),
      version: process.env.NEXT_PUBLIC_APP_VERSION || 'unknown',
      environment: process.env.NODE_ENV || 'unknown',
      uptime_seconds: Math.floor((Date.now() - this.startTime) / 1000),
      deployment_id: this.deploymentId,
      checks,
      summary: {
        total_checks: checks.length,
        healthy_checks: healthyCount,
        degraded_checks: degradedCount,
        unhealthy_checks: unhealthyCount,
        critical_issues: criticalIssues,
        warnings: warnings
      },
      performance_metrics: {
        avg_response_time: Math.round(avgResponseTime),
        slowest_service: slowestCheck.service,
        fastest_service: fastestCheck.service
      }
    };
  }
}

/**
 * GET /api/health/detailed
 * 
 * Returns comprehensive health status with detailed diagnostics
 */
export async function GET(request: NextRequest) {
  try {
    const healthChecker = new DetailedHealthChecker();
    const healthReport = await healthChecker.runDetailedHealthChecks();

    // Set appropriate HTTP status code
    let statusCode = 200;
    if (healthReport.overall_status === 'unhealthy') {
      statusCode = 503; // Service unavailable
    }

    return NextResponse.json(healthReport, { status: statusCode });

  } catch (error) {
    return NextResponse.json({
      overall_status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown health check error',
      summary: {
        total_checks: 0,
        healthy_checks: 0,
        degraded_checks: 0,
        unhealthy_checks: 0,
        critical_issues: ['Health check system failure'],
        warnings: []
      }
    }, { status: 500 });
  }
}
