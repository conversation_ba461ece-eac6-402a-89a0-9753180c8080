import type { NextRequest} from 'next/server';
import { NextResponse } from 'next/server'

// Mock implementation for framework statuses
export async function GET(request: NextRequest) {
  try {
    // In production, this would call the backend API
    // For now, return mock data that matches the expected structure
    
    const frameworks = [
      {
        framework: 'GDPR',
        status: 'compliant',
        compliance_rate: 98.5,
        last_audit: '2024-11-15',
        next_review: '2024-12-15',
        active_policies: 12,
        pending_actions: 0,
        critical_issues: 0,
        description: 'General Data Protection Regulation compliance',
        regulatory_body: 'European Data Protection Board'
      },
      {
        framework: 'CCPA',
        status: 'compliant',
        compliance_rate: 97.2,
        last_audit: '2024-11-10',
        next_review: '2024-12-10',
        active_policies: 8,
        pending_actions: 1,
        critical_issues: 0,
        description: 'California Consumer Privacy Act compliance',
        regulatory_body: 'California Attorney General'
      },
      {
        framework: 'Professional Responsibility',
        status: 'warning',
        compliance_rate: 94.1,
        last_audit: '2024-11-01',
        next_review: '2024-12-01',
        active_policies: 15,
        pending_actions: 2,
        critical_issues: 1,
        description: 'Legal professional conduct compliance',
        regulatory_body: 'State Bar Associations'
      },
      {
        framework: 'Data Retention',
        status: 'compliant',
        compliance_rate: 99.1,
        last_audit: '2024-11-20',
        next_review: '2024-12-20',
        active_policies: 6,
        pending_actions: 0,
        critical_issues: 0,
        description: 'Automated data lifecycle management',
        regulatory_body: 'Various Data Protection Authorities'
      },
      {
        framework: 'Consent Management',
        status: 'compliant',
        compliance_rate: 96.8,
        last_audit: '2024-11-12',
        next_review: '2024-12-12',
        active_policies: 4,
        pending_actions: 0,
        critical_issues: 0,
        description: 'User consent tracking and management',
        regulatory_body: 'Data Protection Authorities'
      },
      {
        framework: 'Data Residency',
        status: 'compliant',
        compliance_rate: 100.0,
        last_audit: '2024-11-25',
        next_review: '2024-12-25',
        active_policies: 2,
        pending_actions: 0,
        critical_issues: 0,
        description: 'Regional data placement compliance',
        regulatory_body: 'Regional Data Protection Authorities'
      },
      {
        framework: 'Regional Disclaimers',
        status: 'compliant',
        compliance_rate: 98.9,
        last_audit: '2024-11-28',
        next_review: '2024-12-28',
        active_policies: 5,
        pending_actions: 0,
        critical_issues: 0,
        description: 'Jurisdiction-specific legal notices',
        regulatory_body: 'State Bar Associations'
      },
      {
        framework: 'Security',
        status: 'compliant',
        compliance_rate: 97.5,
        last_audit: '2024-11-22',
        next_review: '2024-12-22',
        active_policies: 8,
        pending_actions: 1,
        critical_issues: 0,
        description: 'Authentication and authorization compliance',
        regulatory_body: 'Security Standards Organizations'
      }
    ]

    return NextResponse.json(frameworks)

  } catch (error) {
    console.error('Error in framework status API:', error)
    return NextResponse.json(
      { error: 'Failed to fetch framework statuses' },
      { status: 500 }
    )
  }
}
