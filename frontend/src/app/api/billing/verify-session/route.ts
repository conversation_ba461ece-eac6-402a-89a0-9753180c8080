import type { NextRequest} from 'next/server';
import { NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import type { Database } from '@/lib/supabase/database.types';
import Stripe from 'stripe';

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-06-30.basil',
  typescript: true,
});

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('session_id');

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing session_id parameter' },
        { status: 400 }
      );
    }

    const cookieStore = await cookies();

    // Create Supabase client
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );

    // Get the current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user tenant ID
    const tenantId = session.user.user_metadata?.tenant_id;
    if (!tenantId) {
      return NextResponse.json({ error: 'No tenant ID found' }, { status: 400 });
    }

    try {
      // Retrieve the checkout session from Stripe
      const checkoutSession = await stripe.checkout.sessions.retrieve(sessionId, {
        expand: ['subscription', 'customer'],
      });

      // Verify the session belongs to the current user
      if (checkoutSession.metadata?.tenant_id !== tenantId) {
        return NextResponse.json(
          { error: 'Session does not belong to current user' },
          { status: 403 }
        );
      }

      // Check if payment was successful
      if (checkoutSession.payment_status !== 'paid') {
        return NextResponse.json(
          { error: 'Payment not completed' },
          { status: 400 }
        );
      }

      // Get subscription details
      const subscription = checkoutSession.subscription as Stripe.Subscription;
      if (!subscription) {
        return NextResponse.json(
          { error: 'No subscription found' },
          { status: 400 }
        );
      }

      // Get pricing information from our database
      const priceId = subscription.items.data[0]?.price.id;
      const { data: pricingData, error: pricingError } = await supabase
        .schema('tenants')
        .from('subscription_pricing')
        .select('*')
        .eq('stripe_price_id', priceId)
        .single();

      if (pricingError || !pricingData) {
        console.error('Failed to get pricing data:', pricingError);
        return NextResponse.json(
          { error: 'Failed to get subscription details' },
          { status: 500 }
        );
      }

      // Create or update subscription in our database
      const subscriptionData = {
        tenant_id: tenantId,
        plan_id: pricingData.plan_tier, // Use plan_tier as plan_id for now
        status: subscription.status,
        billing_cycle: pricingData.billing_cycle,
        current_period_start: new Date((subscription as any).current_period_start * 1000).toISOString(),
        current_period_end: new Date((subscription as any).current_period_end * 1000).toISOString(),
        payment_provider: 'stripe',
        payment_provider_subscription_id: subscription.id,
        metadata: {
          stripe_customer_id: checkoutSession.customer,
          stripe_session_id: sessionId,
          plan_tier: pricingData.plan_tier,
          amount: pricingData.amount,
          billing_currency: pricingData.currency,
          billing_country: pricingData.region,
        },
      };

      // Check if subscription already exists
      const { data: existingSubscription } = await supabase
        .schema('tenants')
        .from('tenant_subscriptions')
        .select('id')
        .eq('payment_provider_subscription_id', subscription.id)
        .single();

      if (!existingSubscription) {
        // Create new subscription record
        const { error: insertError } = await supabase
          .schema('tenants')
          .from('tenant_subscriptions')
          .insert(subscriptionData as any);

        if (insertError) {
          console.error('Failed to create subscription record:', insertError);
          // Don't fail the request as payment was successful
        }
      }

      // Return subscription details for the frontend
      const subscriptionDetails = {
        plan_name: `${pricingData.plan_tier.charAt(0).toUpperCase() + pricingData.plan_tier.slice(1)} Plan`,
        amount: parseFloat(pricingData.amount),
        currency: pricingData.currency,
        billing_cycle: pricingData.billing_cycle,
        status: subscription.status,
        current_period_end: new Date((subscription as any).current_period_end * 1000).toLocaleDateString(),
        trial_end: subscription.trial_end ? new Date(subscription.trial_end * 1000).toLocaleDateString() : null,
      };

      return NextResponse.json({
        success: true,
        subscription: subscriptionDetails,
        session_id: sessionId,
      });

    } catch (stripeError) {
      console.error('Stripe error:', stripeError);
      return NextResponse.json(
        { error: 'Failed to verify session with Stripe' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error verifying session:', error);
    return NextResponse.json(
      { error: 'Failed to verify session' },
      { status: 500 }
    );
  }
}
