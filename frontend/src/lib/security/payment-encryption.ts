/**
 * Payment Data Encryption Service
 * 
 * Provides AES-256-GCM encryption for sensitive payment data at rest and in transit.
 * Implements field-level encryption for PII and payment information.
 */

import crypto from 'crypto';

// Encryption configuration
const ALGORITHM = 'aes-256-gcm';
const KEY_LENGTH = 32; // 256 bits
const IV_LENGTH = 16;  // 128 bits
const TAG_LENGTH = 16; // 128 bits
const SALT_LENGTH = 32; // 256 bits

// Environment configuration
const ENCRYPTION_KEY = process.env.PAYMENT_ENCRYPTION_KEY;
const MASTER_KEY = process.env.PAYMENT_MASTER_KEY;

// Types
export interface EncryptedData {
  encrypted: string;
  iv: string;
  tag: string;
  salt?: string;
}

export interface EncryptionResult {
  success: boolean;
  data?: EncryptedData;
  error?: string;
}

export interface DecryptionResult {
  success: boolean;
  data?: string;
  error?: string;
}

/**
 * Generate a cryptographically secure key from password and salt
 */
function deriveKey(password: string, salt: Buffer): Buffer {
  return crypto.pbkdf2Sync(password, salt, 100000, KEY_LENGTH, 'sha256');
}

/**
 * Generate a random salt
 */
function generateSalt(): Buffer {
  return crypto.randomBytes(SALT_LENGTH);
}

/**
 * Generate a random IV
 */
function generateIV(): Buffer {
  return crypto.randomBytes(IV_LENGTH);
}

/**
 * Encrypt sensitive payment data using AES-256-GCM
 */
export function encryptPaymentData(
  plaintext: string,
  useFieldEncryption: boolean = true
): EncryptionResult {
  try {
    if (!plaintext || typeof plaintext !== 'string') {
      return {
        success: false,
        error: 'Invalid input data'
      };
    }

    // Use master key if available, otherwise use environment key
    const keySource = MASTER_KEY || ENCRYPTION_KEY;
    if (!keySource) {
      return {
        success: false,
        error: 'Encryption key not configured'
      };
    }

    let key: Buffer;
    let salt: Buffer | undefined;

    if (useFieldEncryption && MASTER_KEY) {
      // Use key derivation for field-level encryption
      salt = generateSalt();
      key = deriveKey(MASTER_KEY, salt);
    } else {
      // Use direct key for transport encryption
      key = Buffer.from(keySource, 'hex');
      if (key.length !== KEY_LENGTH) {
        return {
          success: false,
          error: 'Invalid encryption key length'
        };
      }
    }

    // Generate random IV
    const iv = generateIV();

    // Create cipher
    const cipher = crypto.createCipher(ALGORITHM, key);
    cipher.setAAD(Buffer.from('payment-data')); // Additional authenticated data

    // Encrypt the data
    let encrypted = cipher.update(plaintext, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    // Get authentication tag
    const tag = cipher.getAuthTag();

    const result: EncryptedData = {
      encrypted,
      iv: iv.toString('hex'),
      tag: tag.toString('hex')
    };

    if (salt) {
      result.salt = salt.toString('hex');
    }

    return {
      success: true,
      data: result
    };

  } catch (error) {
    return {
      success: false,
      error: `Encryption failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Decrypt sensitive payment data
 */
export function decryptPaymentData(
  encryptedData: EncryptedData,
  useFieldEncryption: boolean = true
): DecryptionResult {
  try {
    if (!encryptedData || !encryptedData.encrypted || !encryptedData.iv || !encryptedData.tag) {
      return {
        success: false,
        error: 'Invalid encrypted data format'
      };
    }

    // Use master key if available, otherwise use environment key
    const keySource = MASTER_KEY || ENCRYPTION_KEY;
    if (!keySource) {
      return {
        success: false,
        error: 'Encryption key not configured'
      };
    }

    let key: Buffer;

    if (useFieldEncryption && MASTER_KEY && encryptedData.salt) {
      // Use key derivation for field-level encryption
      const salt = Buffer.from(encryptedData.salt, 'hex');
      key = deriveKey(MASTER_KEY, salt);
    } else {
      // Use direct key for transport encryption
      key = Buffer.from(keySource, 'hex');
      if (key.length !== KEY_LENGTH) {
        return {
          success: false,
          error: 'Invalid encryption key length'
        };
      }
    }

    // Parse encrypted components
    const iv = Buffer.from(encryptedData.iv, 'hex');
    const tag = Buffer.from(encryptedData.tag, 'hex');

    // Create decipher
    const decipher = crypto.createDecipher(ALGORITHM, key);
    decipher.setAuthTag(tag);
    decipher.setAAD(Buffer.from('payment-data')); // Must match encryption AAD

    // Decrypt the data
    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return {
      success: true,
      data: decrypted
    };

  } catch (error) {
    return {
      success: false,
      error: `Decryption failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Encrypt specific payment fields (PII data)
 */
export function encryptPaymentFields(
  paymentData: Record<string, unknown>
): { encrypted: Record<string, unknown>; errors: string[] } {
  const sensitiveFields = [
    'card_number', 'number', 'cvv', 'cvc', 'security_code',
    'iban', 'account_number', 'routing_number', 'sort_code',
    'account_holder_name', 'cardholder_name', 'customer_name',
    'billing_address', 'address', 'phone', 'email'
  ];

  const encrypted: Record<string, unknown> = { ...paymentData };
  const errors: string[] = [];

  for (const field of sensitiveFields) {
    if (paymentData[field] && typeof paymentData[field] === 'string') {
      const result = encryptPaymentData(paymentData[field], true);
      if (result.success && result.data) {
        encrypted[field] = result.data;
      } else {
        errors.push(`Failed to encrypt field ${field}: ${result.error}`);
      }
    }
  }

  return { encrypted, errors };
}

/**
 * Decrypt specific payment fields
 */
export function decryptPaymentFields(
  encryptedData: Record<string, unknown>
): { decrypted: Record<string, unknown>; errors: string[] } {
  const sensitiveFields = [
    'card_number', 'number', 'cvv', 'cvc', 'security_code',
    'iban', 'account_number', 'routing_number', 'sort_code',
    'account_holder_name', 'cardholder_name', 'customer_name',
    'billing_address', 'address', 'phone', 'email'
  ];

  const decrypted: Record<string, unknown> = { ...encryptedData };
  const errors: string[] = [];

  for (const field of sensitiveFields) {
    if (encryptedData[field] && typeof encryptedData[field] === 'object') {
      const fieldData = encryptedData[field] as EncryptedData;
      if (fieldData.encrypted && fieldData.iv && fieldData.tag) {
        const result = decryptPaymentData(fieldData, true);
        if (result.success && result.data) {
          decrypted[field] = result.data;
        } else {
          errors.push(`Failed to decrypt field ${field}: ${result.error}`);
        }
      }
    }
  }

  return { decrypted, errors };
}

/**
 * Secure hash for payment data integrity
 */
export function hashPaymentData(data: string): string {
  return crypto.createHash('sha256').update(data).digest('hex');
}

/**
 * Generate secure random token for payment operations
 */
export function generateSecureToken(length: number = 32): string {
  return crypto.randomBytes(length).toString('hex');
}

/**
 * Validate encryption configuration
 */
export function validateEncryptionConfig(): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!ENCRYPTION_KEY && !MASTER_KEY) {
    errors.push('No encryption key configured (PAYMENT_ENCRYPTION_KEY or PAYMENT_MASTER_KEY)');
  }

  if (ENCRYPTION_KEY) {
    try {
      const key = Buffer.from(ENCRYPTION_KEY, 'hex');
      if (key.length !== KEY_LENGTH) {
        errors.push(`PAYMENT_ENCRYPTION_KEY must be ${KEY_LENGTH * 2} hex characters (${KEY_LENGTH} bytes)`);
      }
    } catch (error) {
      errors.push('PAYMENT_ENCRYPTION_KEY must be valid hex string');
    }
  }

  if (MASTER_KEY && MASTER_KEY.length < 32) {
    errors.push('PAYMENT_MASTER_KEY must be at least 32 characters long');
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Generate new encryption key (for setup/rotation)
 */
export function generateEncryptionKey(): string {
  return crypto.randomBytes(KEY_LENGTH).toString('hex');
}
