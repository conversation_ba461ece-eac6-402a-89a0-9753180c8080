/**
 * usePaymentProcessing Hook
 * 
 * React hook for processing payments with Stripe integration,
 * payment method creation, and transaction management.
 */

import { useState, useCallback } from 'react';
import { StripePaymentMethodService } from '@/lib/services/stripe-payment-method-service';
import type {
  StripePaymentMethodCreationRequest,
  StripePaymentMethodCreationResponse,
  PaymentProcessingResponse,
  SupportedCurrency
} from '@/lib/types/payment-methods';
import {
  PaymentProcessingRequest,
  PaymentMethodError,
  SupportedRegion
} from '@/lib/types/payment-methods';

interface UsePaymentProcessingState {
  processing: boolean;
  error: string | null;
  paymentMethod: StripePaymentMethodCreationResponse | null;
  paymentResult: PaymentProcessingResponse | null;
  processingLog: string[];
}

interface UsePaymentProcessingActions {
  createPaymentMethod: (request: StripePaymentMethodCreationRequest) => Promise<StripePaymentMethodCreationResponse>;
  processPayment: (
    paymentMethodId: string,
    amountCents: number,
    currency: SupportedCurrency,
    tenantId: string,
    metadata?: Record<string, string>
  ) => Promise<PaymentProcessingResponse>;
  confirmPayment: (paymentIntentId: string, paymentMethodId: string) => Promise<PaymentProcessingResponse>;
  cancelPayment: (paymentIntentId: string) => Promise<void>;
  clearError: () => void;
  clearResults: () => void;
  addLogEntry: (message: string) => void;
}

type UsePaymentProcessingReturn = UsePaymentProcessingState & UsePaymentProcessingActions;

export function usePaymentProcessing(): UsePaymentProcessingReturn {
  const [state, setState] = useState<UsePaymentProcessingState>({
    processing: false,
    error: null,
    paymentMethod: null,
    paymentResult: null,
    processingLog: []
  });

  const stripeService = new StripePaymentMethodService();

  // Helper function to convert Stripe response to PaymentProcessingResponse
  const convertToPaymentProcessingResponse = (
    stripeResponse: StripePaymentMethodCreationResponse
  ): PaymentProcessingResponse => ({
    success: stripeResponse.status === 'succeeded',
    payment_intent_id: stripeResponse.payment_intent_id,
    client_secret: stripeResponse.client_secret,
    status: stripeResponse.status === 'succeeded' ? 'succeeded' :
            stripeResponse.status === 'requires_action' ? 'requires_action' :
            stripeResponse.status === 'failed' ? 'failed' : 'pending',
    error: stripeResponse.error_message,
    next_action: stripeResponse.next_action
  });

  const setProcessing = useCallback((processing: boolean) => {
    setState(prev => ({ ...prev, processing }));
  }, []);

  const setError = useCallback((error: string | null) => {
    setState(prev => ({ ...prev, error }));
  }, []);

  const addLogEntry = useCallback((message: string) => {
    setState(prev => ({
      ...prev,
      processingLog: [...prev.processingLog, `${new Date().toISOString()}: ${message}`]
    }));
  }, []);

  const createPaymentMethod = useCallback(async (
    request: StripePaymentMethodCreationRequest
  ): Promise<StripePaymentMethodCreationResponse> => {
    try {
      setProcessing(true);
      setError(null);
      addLogEntry(`Creating payment method: ${request.payment_method_code} for ${request.country_code}`);

      const result = await stripeService.createPaymentMethod(request);

      setState(prev => ({
        ...prev,
        paymentMethod: result,
        processing: false
      }));

      addLogEntry(`Payment method created successfully: ${result.stripe_payment_method_id}`);
      return result;
    } catch (error) {
      const errorMessage = error instanceof PaymentMethodError 
        ? error.message 
        : 'Failed to create payment method';
      
      addLogEntry(`Payment method creation failed: ${errorMessage}`);
      
      setState(prev => ({
        ...prev,
        processing: false,
        error: errorMessage
      }));

      throw error;
    }
  }, [stripeService, addLogEntry]);

  const processPayment = useCallback(async (
    paymentMethodId: string,
    amountCents: number,
    currency: SupportedCurrency,
    tenantId: string,
    metadata?: Record<string, string>
  ): Promise<PaymentProcessingResponse> => {
    try {
      setProcessing(true);
      setError(null);
      addLogEntry(`Processing payment: ${amountCents} ${currency} for tenant ${tenantId}`);

      const result = await stripeService.processPayment(
        paymentMethodId,
        amountCents,
        currency,
        tenantId,
        metadata
      );

      const paymentResponse = convertToPaymentProcessingResponse(result);

      setState(prev => ({
        ...prev,
        paymentResult: paymentResponse,
        processing: false
      }));

      addLogEntry(`Payment processed: ${result.status} - ${result.payment_intent_id}`);
      return paymentResponse;
    } catch (error) {
      const errorMessage = error instanceof PaymentMethodError 
        ? error.message 
        : 'Payment processing failed';
      
      addLogEntry(`Payment processing failed: ${errorMessage}`);
      
      setState(prev => ({
        ...prev,
        processing: false,
        error: errorMessage
      }));

      const failedResult: PaymentProcessingResponse = {
        success: false,
        payment_intent_id: '',
        status: 'failed',
        error: errorMessage
      };

      setState(prev => ({
        ...prev,
        paymentResult: failedResult
      }));

      return failedResult;
    }
  }, [stripeService, addLogEntry]);

  const confirmPayment = useCallback(async (
    paymentIntentId: string,
    paymentMethodId: string
  ): Promise<PaymentProcessingResponse> => {
    try {
      setProcessing(true);
      setError(null);
      addLogEntry(`Confirming payment: ${paymentIntentId}`);

      const result = await stripeService.confirmPayment(paymentIntentId, paymentMethodId);
      const paymentResponse = convertToPaymentProcessingResponse(result);

      setState(prev => ({
        ...prev,
        paymentResult: paymentResponse,
        processing: false
      }));

      addLogEntry(`Payment confirmed: ${result.status}`);
      return paymentResponse;
    } catch (error) {
      const errorMessage = error instanceof PaymentMethodError 
        ? error.message 
        : 'Payment confirmation failed';
      
      addLogEntry(`Payment confirmation failed: ${errorMessage}`);
      
      setState(prev => ({
        ...prev,
        processing: false,
        error: errorMessage
      }));

      const failedResult: PaymentProcessingResponse = {
        success: false,
        payment_intent_id: paymentIntentId,
        status: 'failed',
        error: errorMessage
      };

      setState(prev => ({
        ...prev,
        paymentResult: failedResult
      }));

      return failedResult;
    }
  }, [stripeService, addLogEntry]);

  const cancelPayment = useCallback(async (paymentIntentId: string) => {
    try {
      setProcessing(true);
      setError(null);
      addLogEntry(`Canceling payment: ${paymentIntentId}`);

      await stripeService.cancelPayment(paymentIntentId);

      setState(prev => ({
        ...prev,
        processing: false,
        paymentResult: null
      }));

      addLogEntry(`Payment canceled successfully`);
    } catch (error) {
      const errorMessage = error instanceof PaymentMethodError 
        ? error.message 
        : 'Payment cancellation failed';
      
      addLogEntry(`Payment cancellation failed: ${errorMessage}`);
      
      setState(prev => ({
        ...prev,
        processing: false,
        error: errorMessage
      }));
    }
  }, [stripeService, addLogEntry]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const clearResults = useCallback(() => {
    setState(prev => ({
      ...prev,
      paymentMethod: null,
      paymentResult: null,
      processingLog: []
    }));
  }, []);

  return {
    // State
    processing: state.processing,
    error: state.error,
    paymentMethod: state.paymentMethod,
    paymentResult: state.paymentResult,
    processingLog: state.processingLog,
    
    // Actions
    createPaymentMethod,
    processPayment,
    confirmPayment,
    cancelPayment,
    clearError,
    clearResults,
    addLogEntry
  };
}

export default usePaymentProcessing;
