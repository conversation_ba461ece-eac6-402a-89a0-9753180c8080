/**
 * usePaymentMethods Hook
 * 
 * React hook for managing payment methods with regional support,
 * validation, and customer preferences.
 */

import { useState, useEffect, useCallback } from 'react';
import { RegionalPaymentMethodService } from '@/lib/services/regional-payment-method-service';
import type {
  PaymentMethodAvailability,
  PaymentMethodValidationRequest,
  PaymentMethodValidationResponse,
  PaymentMethodSelectionRequest,
  PaymentMethodSelectionResponse,
  CustomerPaymentPreferences,
  CustomerPaymentPreferencesUpdate,
  SupportedRegion,
  SupportedCurrency} from '@/lib/types/payment-methods';
import {
  PaymentMethodError
} from '@/lib/types/payment-methods';

interface UsePaymentMethodsState {
  availableMethods: PaymentMethodAvailability[];
  loading: boolean;
  error: string | null;
  validationResult: PaymentMethodValidationResponse | null;
  recommendations: PaymentMethodSelectionResponse | null;
  customerPreferences: CustomerPaymentPreferences | null;
}

interface UsePaymentMethodsActions {
  fetchAvailableMethods: (
    countryCode: SupportedRegion,
    currencyCode: SupportedCurrency,
    amountCents?: number
  ) => Promise<void>;
  validatePaymentMethod: (request: PaymentMethodValidationRequest) => Promise<PaymentMethodValidationResponse>;
  recommendPaymentMethods: (request: PaymentMethodSelectionRequest) => Promise<PaymentMethodSelectionResponse>;
  getCustomerPreferences: (customerId: string, countryCode: SupportedRegion) => Promise<void>;
  saveCustomerPreferences: (preferences: CustomerPaymentPreferences) => Promise<void>;
  clearError: () => void;
  clearValidation: () => void;
}

type UsePaymentMethodsReturn = UsePaymentMethodsState & UsePaymentMethodsActions;

export function usePaymentMethods(): UsePaymentMethodsReturn {
  const [state, setState] = useState<UsePaymentMethodsState>({
    availableMethods: [],
    loading: false,
    error: null,
    validationResult: null,
    recommendations: null,
    customerPreferences: null
  });

  const regionalService = new RegionalPaymentMethodService();

  const setLoading = useCallback((loading: boolean) => {
    setState(prev => ({ ...prev, loading }));
  }, []);

  const setError = useCallback((error: string | null) => {
    setState(prev => ({ ...prev, error }));
  }, []);

  const fetchAvailableMethods = useCallback(async (
    countryCode: SupportedRegion,
    currencyCode: SupportedCurrency,
    amountCents?: number
  ) => {
    try {
      setLoading(true);
      setError(null);

      const methods = await regionalService.getAvailablePaymentMethods(
        countryCode,
        currencyCode,
        amountCents
      );

      setState(prev => ({
        ...prev,
        availableMethods: methods,
        loading: false
      }));
    } catch (error) {
      const errorMessage = error instanceof PaymentMethodError 
        ? error.message 
        : 'Failed to fetch payment methods';
      
      setState(prev => ({
        ...prev,
        availableMethods: [],
        loading: false,
        error: errorMessage
      }));
    }
  }, [regionalService]);

  const validatePaymentMethod = useCallback(async (
    request: PaymentMethodValidationRequest
  ): Promise<PaymentMethodValidationResponse> => {
    try {
      setLoading(true);
      setError(null);

      const result = await regionalService.validatePaymentMethod(request);

      setState(prev => ({
        ...prev,
        validationResult: result,
        loading: false
      }));

      return result;
    } catch (error) {
      const errorMessage = error instanceof PaymentMethodError 
        ? error.message 
        : 'Payment method validation failed';
      
      setError(errorMessage);
      setLoading(false);
      
      const failedResult: PaymentMethodValidationResponse = {
        is_valid: false,
        validation_errors: [{ field: 'general', message: errorMessage, validation_type: 'VALIDATION_ERROR' }]
      };

      setState(prev => ({
        ...prev,
        validationResult: failedResult,
        loading: false
      }));

      return failedResult;
    }
  }, [regionalService]);

  const recommendPaymentMethods = useCallback(async (
    request: PaymentMethodSelectionRequest
  ): Promise<PaymentMethodSelectionResponse> => {
    try {
      setLoading(true);
      setError(null);

      const result = await regionalService.getRecommendedPaymentMethod(request);

      setState(prev => ({
        ...prev,
        recommendations: result,
        loading: false
      }));

      return result;
    } catch (error) {
      const errorMessage = error instanceof PaymentMethodError 
        ? error.message 
        : 'Failed to get payment method recommendations';
      
      setError(errorMessage);
      setLoading(false);
      
      const failedResult: PaymentMethodSelectionResponse = {
        available_methods: [],
        recommended_method: null,
        total_processing_fee_cents: 0,
        estimated_completion_time: 'Unknown'
      };

      setState(prev => ({
        ...prev,
        recommendations: failedResult,
        loading: false
      }));

      return failedResult;
    }
  }, [regionalService]);

  const getCustomerPreferences = useCallback(async (
    customerId: string,
    countryCode: SupportedRegion
  ) => {
    try {
      setLoading(true);
      setError(null);

      const preferences = await regionalService.getCustomerPreferences(customerId);

      setState((prev: UsePaymentMethodsState): UsePaymentMethodsState => ({
        ...prev,
        customerPreferences: preferences || null,
        loading: false
      }));
    } catch (error) {
      const errorMessage = error instanceof PaymentMethodError 
        ? error.message 
        : 'Failed to fetch customer preferences';
      
      setState(prev => ({
        ...prev,
        customerPreferences: null,
        loading: false,
        error: errorMessage
      }));
    }
  }, [regionalService]);

  const saveCustomerPreferences = useCallback(async (
    preferences: CustomerPaymentPreferences
  ) => {
    try {
      setLoading(true);
      setError(null);

      const updates: CustomerPaymentPreferencesUpdate = {
        preferred_payment_method_type_id: preferences.preferred_payment_method_type_id,
        backup_payment_method_type_id: preferences.backup_payment_method_type_id,
        auto_select_optimal: preferences.auto_select_optimal
      };

      const updatedPreferences = await regionalService.updateCustomerPreferences(
        preferences.tenant_id,
        preferences.country_code,
        preferences.currency_code,
        updates
      );

      setState(prev => ({
        ...prev,
        customerPreferences: updatedPreferences,
        loading: false
      }));
    } catch (error) {
      const errorMessage = error instanceof PaymentMethodError 
        ? error.message 
        : 'Failed to save customer preferences';
      
      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage
      }));
    }
  }, [regionalService]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const clearValidation = useCallback(() => {
    setState(prev => ({
      ...prev,
      validationResult: null
    }));
  }, []);

  return {
    // State
    availableMethods: state.availableMethods,
    loading: state.loading,
    error: state.error,
    validationResult: state.validationResult,
    recommendations: state.recommendations,
    customerPreferences: state.customerPreferences,
    
    // Actions
    fetchAvailableMethods,
    validatePaymentMethod,
    recommendPaymentMethods,
    getCustomerPreferences,
    saveCustomerPreferences,
    clearError,
    clearValidation
  };
}

export default usePaymentMethods;
