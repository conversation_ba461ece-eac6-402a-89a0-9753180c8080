/**
 * Payment Prometheus Metrics
 * 
 * Comprehensive Prometheus metrics collection for payment operations
 * including counters, histograms, and gauges for monitoring and alerting.
 */

import type { PaymentMethodCode, SupportedRegion, SupportedCurrency } from '@/lib/types/payment-methods';

// Metric interfaces
export interface PaymentMetricLabels {
  payment_method: PaymentMethodCode;
  region: SupportedRegion;
  currency: SupportedCurrency;
  tenant_id?: string;
  status?: string;
  error_code?: string;
}

export interface PaymentOperationMetric {
  operation: string;
  success: boolean;
  duration_ms: number;
  labels: PaymentMetricLabels;
}

export interface PaymentVolumeMetric {
  amount_cents: number;
  labels: PaymentMetricLabels;
}

// Prometheus metric types (simplified for Next.js environment)
interface PrometheusMetric {
  name: string;
  help: string;
  type: 'counter' | 'histogram' | 'gauge';
  labels: string[];
}

export class PaymentPrometheusMetrics {
  private static instance: PaymentPrometheusMetrics;
  private metrics: Map<string, PrometheusMetric> = new Map();
  private values: Map<string, Map<string, number>> = new Map();
  
  public static getInstance(): PaymentPrometheusMetrics {
    if (!PaymentPrometheusMetrics.instance) {
      PaymentPrometheusMetrics.instance = new PaymentPrometheusMetrics();
    }
    return PaymentPrometheusMetrics.instance;
  }
  
  constructor() {
    this.initializeMetrics();
  }
  
  /**
   * Initialize all payment-related Prometheus metrics
   */
  private initializeMetrics(): void {
    // Payment method creation metrics
    this.registerMetric({
      name: 'payment_method_creation_total',
      help: 'Total number of payment method creation attempts',
      type: 'counter',
      labels: ['payment_method', 'region', 'currency', 'status', 'tenant_id']
    });
    
    this.registerMetric({
      name: 'payment_method_creation_duration_seconds',
      help: 'Duration of payment method creation operations',
      type: 'histogram',
      labels: ['payment_method', 'region', 'currency', 'status']
    });
    
    // Payment processing metrics
    this.registerMetric({
      name: 'payment_processing_total',
      help: 'Total number of payment processing attempts',
      type: 'counter',
      labels: ['payment_method', 'region', 'currency', 'status', 'tenant_id']
    });
    
    this.registerMetric({
      name: 'payment_processing_duration_seconds',
      help: 'Duration of payment processing operations',
      type: 'histogram',
      labels: ['payment_method', 'region', 'currency', 'status']
    });
    
    this.registerMetric({
      name: 'payment_volume_cents',
      help: 'Total payment volume processed in cents',
      type: 'counter',
      labels: ['payment_method', 'region', 'currency', 'tenant_id']
    });
    
    // Error metrics
    this.registerMetric({
      name: 'payment_errors_total',
      help: 'Total number of payment errors',
      type: 'counter',
      labels: ['payment_method', 'region', 'currency', 'error_code', 'tenant_id']
    });
    
    // Compliance metrics
    this.registerMetric({
      name: 'payment_compliance_violations_total',
      help: 'Total number of payment compliance violations',
      type: 'counter',
      labels: ['framework', 'violation_type', 'severity', 'region', 'tenant_id']
    });
    
    // Performance metrics
    this.registerMetric({
      name: 'payment_success_rate',
      help: 'Payment success rate by method and region',
      type: 'gauge',
      labels: ['payment_method', 'region', 'currency']
    });
    
    // Active payment methods
    this.registerMetric({
      name: 'active_payment_methods_total',
      help: 'Number of active payment methods by region',
      type: 'gauge',
      labels: ['payment_method', 'region', 'tenant_id']
    });
    
    // Webhook metrics
    this.registerMetric({
      name: 'payment_webhook_processing_total',
      help: 'Total number of payment webhook processing attempts',
      type: 'counter',
      labels: ['event_type', 'status', 'tenant_id']
    });
    
    this.registerMetric({
      name: 'payment_webhook_processing_duration_seconds',
      help: 'Duration of payment webhook processing',
      type: 'histogram',
      labels: ['event_type', 'status']
    });
  }
  
  /**
   * Record payment method creation metric
   */
  public recordPaymentMethodCreation(
    paymentMethod: PaymentMethodCode,
    region: SupportedRegion,
    currency: SupportedCurrency,
    success: boolean,
    durationMs: number,
    tenantId?: string
  ): void {
    const labels = {
      payment_method: paymentMethod,
      region: region,
      currency: currency,
      status: success ? 'success' : 'failure',
      tenant_id: tenantId || 'unknown'
    };
    
    this.incrementCounter('payment_method_creation_total', labels);
    this.recordHistogram('payment_method_creation_duration_seconds', durationMs / 1000, {
      payment_method: paymentMethod,
      region: region,
      currency: currency,
      status: labels.status
    });
  }
  
  /**
   * Record payment processing metric
   */
  public recordPaymentProcessing(
    paymentMethod: PaymentMethodCode,
    region: SupportedRegion,
    currency: SupportedCurrency,
    amountCents: number,
    success: boolean,
    durationMs: number,
    tenantId?: string
  ): void {
    const labels = {
      payment_method: paymentMethod,
      region: region,
      currency: currency,
      status: success ? 'success' : 'failure',
      tenant_id: tenantId || 'unknown'
    };
    
    this.incrementCounter('payment_processing_total', labels);
    this.recordHistogram('payment_processing_duration_seconds', durationMs / 1000, {
      payment_method: paymentMethod,
      region: region,
      currency: currency,
      status: labels.status
    });
    
    if (success) {
      this.incrementCounter('payment_volume_cents', {
        payment_method: paymentMethod,
        region: region,
        currency: currency,
        tenant_id: tenantId || 'unknown'
      }, amountCents);
    }
  }
  
  /**
   * Record payment error metric
   */
  public recordPaymentError(
    paymentMethod: PaymentMethodCode,
    region: SupportedRegion,
    currency: SupportedCurrency,
    errorCode: string,
    tenantId?: string
  ): void {
    this.incrementCounter('payment_errors_total', {
      payment_method: paymentMethod,
      region: region,
      currency: currency,
      error_code: errorCode,
      tenant_id: tenantId || 'unknown'
    });
  }
  
  /**
   * Record compliance violation metric
   */
  public recordComplianceViolation(
    framework: string,
    violationType: string,
    severity: string,
    region: SupportedRegion,
    tenantId?: string
  ): void {
    this.incrementCounter('payment_compliance_violations_total', {
      framework: framework,
      violation_type: violationType,
      severity: severity,
      region: region,
      tenant_id: tenantId || 'unknown'
    });
  }
  
  /**
   * Update payment success rate gauge
   */
  public updatePaymentSuccessRate(
    paymentMethod: PaymentMethodCode,
    region: SupportedRegion,
    currency: SupportedCurrency,
    successRate: number
  ): void {
    this.setGauge('payment_success_rate', successRate, {
      payment_method: paymentMethod,
      region: region,
      currency: currency
    });
  }
  
  /**
   * Record webhook processing metric
   */
  public recordWebhookProcessing(
    eventType: string,
    success: boolean,
    durationMs: number,
    tenantId?: string
  ): void {
    const labels = {
      event_type: eventType,
      status: success ? 'success' : 'failure',
      tenant_id: tenantId || 'unknown'
    };
    
    this.incrementCounter('payment_webhook_processing_total', labels);
    this.recordHistogram('payment_webhook_processing_duration_seconds', durationMs / 1000, {
      event_type: eventType,
      status: labels.status
    });
  }
  
  /**
   * Get metrics in Prometheus format for /metrics endpoint
   */
  public getPrometheusMetrics(): string {
    let output = '';
    
    for (const [name, metric] of this.metrics) {
      output += `# HELP ${name} ${metric.help}\n`;
      output += `# TYPE ${name} ${metric.type}\n`;
      
      const metricValues = this.values.get(name);
      if (metricValues) {
        for (const [labelString, value] of metricValues) {
          output += `${name}${labelString} ${value}\n`;
        }
      }
      output += '\n';
    }
    
    return output;
  }
  
  /**
   * Get metrics summary for monitoring dashboard
   */
  public getMetricsSummary(): Record<string, unknown> {
    const summary: Record<string, unknown> = {};
    
    for (const [name, metric] of this.metrics) {
      const metricValues = this.values.get(name);
      if (metricValues) {
        summary[name] = {
          type: metric.type,
          help: metric.help,
          values: Object.fromEntries(metricValues)
        };
      }
    }
    
    return summary;
  }
  
  // Private helper methods
  
  private registerMetric(metric: PrometheusMetric): void {
    this.metrics.set(metric.name, metric);
    this.values.set(metric.name, new Map());
  }
  
  private incrementCounter(name: string, labels: Record<string, string>, value: number = 1): void {
    const labelString = this.formatLabels(labels);
    const metricValues = this.values.get(name);
    if (metricValues) {
      const currentValue = metricValues.get(labelString) || 0;
      metricValues.set(labelString, currentValue + value);
    }
  }
  
  private recordHistogram(name: string, value: number, labels: Record<string, string>): void {
    // Simplified histogram implementation - in production, use proper histogram buckets
    const labelString = this.formatLabels(labels);
    const metricValues = this.values.get(name);
    if (metricValues) {
      metricValues.set(labelString, value);
    }
  }
  
  private setGauge(name: string, value: number, labels: Record<string, string>): void {
    const labelString = this.formatLabels(labels);
    const metricValues = this.values.get(name);
    if (metricValues) {
      metricValues.set(labelString, value);
    }
  }
  
  private formatLabels(labels: Record<string, string>): string {
    const labelPairs = Object.entries(labels)
      .map(([key, value]) => `${key}="${value}"`)
      .join(',');
    return labelPairs ? `{${labelPairs}}` : '';
  }
}

// Export singleton instance
export const paymentMetrics = PaymentPrometheusMetrics.getInstance();
export default paymentMetrics;
