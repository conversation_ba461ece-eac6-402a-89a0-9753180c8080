/**
 * Payment-Specific Sentry Integration
 * 
 * Enhanced Sentry error tracking and monitoring specifically for payment operations
 * with payment method context, transaction metadata, and custom alerts.
 */

import * as Sentry from '@sentry/nextjs';
import type { SupportedRegion, SupportedCurrency, PaymentMethodCode } from '@/lib/types/payment-methods';

// Payment-specific error types
export type PaymentErrorType = 
  | 'payment_method_creation_failed'
  | 'payment_processing_failed'
  | 'payment_validation_failed'
  | 'stripe_integration_error'
  | 'compliance_violation'
  | 'encryption_error'
  | 'authentication_failed'
  | 'rate_limit_exceeded'
  | 'webhook_processing_failed'
  | 'reconciliation_failed';

export type PaymentSeverity = 'low' | 'medium' | 'high' | 'critical';

export interface PaymentContext {
  tenantId?: string;
  userId?: string;
  customerId?: string;
  paymentMethodId?: string;
  paymentIntentId?: string;
  subscriptionId?: string;
  paymentMethodCode?: PaymentMethodCode;
  region?: SupportedRegion;
  currency?: SupportedCurrency;
  amount?: number;
  transactionId?: string;
  stripeEventType?: string;
  webhookEventId?: string;
  retryCount?: number;
  processingTimeMs?: number;
}

export interface PaymentMetrics {
  operation: string;
  success: boolean;
  durationMs: number;
  errorCode?: string;
  paymentMethodType?: PaymentMethodCode;
  region?: SupportedRegion;
  amount?: number;
}

export class PaymentSentryIntegration {
  private static instance: PaymentSentryIntegration;
  
  public static getInstance(): PaymentSentryIntegration {
    if (!PaymentSentryIntegration.instance) {
      PaymentSentryIntegration.instance = new PaymentSentryIntegration();
    }
    return PaymentSentryIntegration.instance;
  }
  
  /**
   * Capture payment-specific error with enhanced context
   */
  public capturePaymentError(
    error: Error,
    errorType: PaymentErrorType,
    context: PaymentContext,
    severity: PaymentSeverity = 'high'
  ): string {
    return Sentry.withScope((scope) => {
      // Set payment-specific context
      scope.setContext('payment', {
        error_type: errorType,
        tenant_id: context.tenantId,
        user_id: context.userId,
        customer_id: context.customerId,
        payment_method_id: context.paymentMethodId,
        payment_intent_id: context.paymentIntentId,
        subscription_id: context.subscriptionId,
        payment_method_code: context.paymentMethodCode,
        region: context.region,
        currency: context.currency,
        amount_cents: context.amount,
        transaction_id: context.transactionId,
        stripe_event_type: context.stripeEventType,
        webhook_event_id: context.webhookEventId,
        retry_count: context.retryCount || 0,
        processing_time_ms: context.processingTimeMs,
        timestamp: new Date().toISOString()
      });
      
      // Set payment-specific tags for filtering and alerting
      scope.setTags({
        payment_error_type: errorType,
        payment_method: context.paymentMethodCode || 'unknown',
        payment_region: context.region || 'unknown',
        payment_currency: context.currency || 'unknown',
        payment_severity: severity,
        has_retry: context.retryCount ? 'yes' : 'no',
        tenant_id: context.tenantId || 'unknown'
      });
      
      // Set error level based on severity
      scope.setLevel(this.getSentryLevel(severity));
      
      // Add payment breadcrumb
      scope.addBreadcrumb({
        category: 'payment.error',
        message: `Payment error: ${errorType}`,
        data: {
          error_type: errorType,
          payment_method: context.paymentMethodCode,
          region: context.region,
          amount: context.amount,
          retry_count: context.retryCount
        },
        level: 'error'
      });
      
      // Capture the error
      return Sentry.captureException(error);
    });
  }
  
  /**
   * Track payment operation performance
   */
  public trackPaymentPerformance(metrics: PaymentMetrics): void {
    // Create performance span
    Sentry.startSpan({
      op: 'payment.operation',
      name: `Payment Operation: ${metrics.operation}`,
      attributes: {
        operation: metrics.operation,
        success: metrics.success.toString(),
        duration_ms: metrics.durationMs,
        error_code: metrics.errorCode || 'none',
        payment_method_type: metrics.paymentMethodType || 'unknown',
        region: metrics.region || 'unknown',
        amount_cents: metrics.amount || 0
      }
    }, () => {
      // Performance span execution
    });
    
    // Add performance breadcrumb
    Sentry.addBreadcrumb({
      category: 'payment.performance',
      message: `Payment operation: ${metrics.operation}`,
      data: {
        operation: metrics.operation,
        success: metrics.success,
        duration_ms: metrics.durationMs,
        payment_method: metrics.paymentMethodType,
        region: metrics.region
      },
      level: metrics.success ? 'info' : 'warning'
    });
  }
  
  /**
   * Track payment method creation events
   */
  public trackPaymentMethodCreation(
    paymentMethodCode: PaymentMethodCode,
    region: SupportedRegion,
    success: boolean,
    context: PaymentContext,
    durationMs?: number
  ): void {
    Sentry.addBreadcrumb({
      category: 'payment.method_creation',
      message: `Payment method creation: ${paymentMethodCode}`,
      data: {
        payment_method_code: paymentMethodCode,
        region: region,
        success: success,
        tenant_id: context.tenantId,
        user_id: context.userId,
        duration_ms: durationMs,
        timestamp: new Date().toISOString()
      },
      level: success ? 'info' : 'error'
    });
    
    // Track as custom metric
    if (success) {
      this.trackPaymentPerformance({
        operation: 'payment_method_creation',
        success: true,
        durationMs: durationMs || 0,
        paymentMethodType: paymentMethodCode,
        region: region
      });
    }
  }
  
  /**
   * Track payment processing events
   */
  public trackPaymentProcessing(
    paymentMethodCode: PaymentMethodCode,
    amount: number,
    currency: SupportedCurrency,
    success: boolean,
    context: PaymentContext,
    durationMs?: number,
    errorCode?: string
  ): void {
    Sentry.addBreadcrumb({
      category: 'payment.processing',
      message: `Payment processing: ${amount} ${currency}`,
      data: {
        payment_method_code: paymentMethodCode,
        amount_cents: amount,
        currency: currency,
        success: success,
        error_code: errorCode,
        tenant_id: context.tenantId,
        payment_intent_id: context.paymentIntentId,
        duration_ms: durationMs,
        timestamp: new Date().toISOString()
      },
      level: success ? 'info' : 'error'
    });
    
    // Track performance metrics
    this.trackPaymentPerformance({
      operation: 'payment_processing',
      success: success,
      durationMs: durationMs || 0,
      errorCode: errorCode,
      paymentMethodType: paymentMethodCode,
      region: context.region,
      amount: amount
    });
  }
  
  /**
   * Track compliance violations
   */
  public trackComplianceViolation(
    violationType: string,
    framework: string,
    severity: PaymentSeverity,
    context: PaymentContext,
    details?: Record<string, unknown>
  ): void {
    Sentry.withScope((scope) => {
      scope.setContext('compliance_violation', {
        violation_type: violationType,
        framework: framework,
        severity: severity,
        tenant_id: context.tenantId,
        region: context.region,
        details: details,
        timestamp: new Date().toISOString()
      });
      
      scope.setTags({
        compliance_violation: violationType,
        compliance_framework: framework,
        compliance_severity: severity,
        tenant_id: context.tenantId || 'unknown'
      });
      
      scope.setLevel(this.getSentryLevel(severity));
      
      Sentry.captureMessage(`Compliance violation: ${violationType} (${framework})`, 'warning');
    });
  }
  
  /**
   * Track webhook processing events
   */
  public trackWebhookProcessing(
    eventType: string,
    success: boolean,
    context: PaymentContext,
    durationMs?: number,
    errorMessage?: string
  ): void {
    Sentry.addBreadcrumb({
      category: 'payment.webhook',
      message: `Webhook processing: ${eventType}`,
      data: {
        event_type: eventType,
        success: success,
        webhook_event_id: context.webhookEventId,
        stripe_event_type: context.stripeEventType,
        tenant_id: context.tenantId,
        duration_ms: durationMs,
        error_message: errorMessage,
        timestamp: new Date().toISOString()
      },
      level: success ? 'info' : 'error'
    });
  }
  
  /**
   * Set user context for payment operations
   */
  public setPaymentUserContext(
    userId: string,
    tenantId: string,
    region?: SupportedRegion,
    subscriptionPlan?: string
  ): void {
    Sentry.setUser({
      id: userId,
      segment: subscriptionPlan || 'unknown',
      data: {
        tenant_id: tenantId,
        region: region || 'unknown',
        context: 'payment_operations'
      }
    });
  }
  
  /**
   * Clear payment context (for security)
   */
  public clearPaymentContext(): void {
    Sentry.setUser(null);
    Sentry.setContext('payment', null);
  }
  
  // Private helper methods
  
  private getSentryLevel(severity: PaymentSeverity): Sentry.SeverityLevel {
    switch (severity) {
      case 'critical':
        return 'fatal';
      case 'high':
        return 'error';
      case 'medium':
        return 'warning';
      case 'low':
        return 'info';
      default:
        return 'error';
    }
  }
}

// Export singleton instance
export const paymentSentry = PaymentSentryIntegration.getInstance();
export default paymentSentry;
