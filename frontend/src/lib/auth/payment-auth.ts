/**
 * Payment API Authentication & Authorization
 * 
 * Comprehensive authentication and authorization system for payment endpoints
 * with JWT validation, tenant verification, and rate limiting.
 */

import type { NextRequest} from 'next/server';
import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { verify } from 'jsonwebtoken';
import { rateLimit } from '@/lib/middlewares/rate-limit';

// Types
export interface PaymentAuthUser {
  id: string;
  email: string;
  role: string;
  tenantId: string;
  metadata?: Record<string, unknown>;
}

export interface PaymentAuthContext {
  user: PaymentAuthUser;
  supabase: ReturnType<typeof createClient>;
  rateLimitInfo?: {
    remaining: number;
    resetTime: number;
  };
}

export type PaymentRouteHandler = (
  req: NextRequest,
  context: PaymentAuthContext
) => Promise<Response>;

// Environment configuration
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const JWT_SECRET = process.env.SUPABASE_JWT_SECRET!;
const PAYMENT_API_KEY = process.env.PAYMENT_API_KEY;

// Rate limiting configuration for payment endpoints
const PAYMENT_RATE_LIMITS = {
  create: { requests: 10, window: 60 }, // 10 payment method creations per minute
  process: { requests: 20, window: 60 }, // 20 payment processes per minute
  validate: { requests: 50, window: 60 }, // 50 validations per minute
  default: { requests: 30, window: 60 }   // 30 requests per minute for other endpoints
};

/**
 * Extract and validate JWT token from Authorization header
 */
async function validateJWTToken(authHeader: string): Promise<PaymentAuthUser> {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('Invalid authorization header format');
  }

  const token = authHeader.replace('Bearer ', '');
  
  try {
    const payload = verify(token, JWT_SECRET) as any;
    
    if (!payload.sub || !payload.email) {
      throw new Error('Invalid token payload');
    }

    // Extract tenant ID from token
    const tenantId = payload.tenant_id || payload.app_metadata?.tenant_id;
    if (!tenantId) {
      throw new Error('Token does not contain tenant information');
    }

    return {
      id: payload.sub,
      email: payload.email,
      role: payload.role || payload.app_metadata?.role || 'user',
      tenantId,
      metadata: payload.app_metadata || {}
    };
  } catch (error) {
    throw new Error(`JWT validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Validate API key for payment endpoints
 */
function validatePaymentAPIKey(apiKey?: string): boolean {
  if (!PAYMENT_API_KEY) {
    console.warn('PAYMENT_API_KEY not configured - API key validation disabled');
    return true; // Allow in development if not configured
  }

  return apiKey === PAYMENT_API_KEY;
}

/**
 * Verify tenant access permissions
 */
async function verifyTenantAccess(
  user: PaymentAuthUser, 
  requestedTenantId?: string,
  supabase?: ReturnType<typeof createClient>
): Promise<boolean> {
  // If no specific tenant requested, use user's tenant
  if (!requestedTenantId) {
    return true;
  }

  // User must match the requested tenant
  if (user.tenantId !== requestedTenantId) {
    // Check if user has cross-tenant permissions (super admin, etc.)
    if (user.role === 'super_admin' || user.metadata?.is_super_admin) {
      return true;
    }
    
    // Additional tenant access verification via database if needed
    if (supabase) {
      const { data, error } = await supabase
        .from('tenant_users')
        .select('role')
        .eq('user_id', user.id)
        .eq('tenant_id', requestedTenantId)
        .single();
      
      if (!error && data) {
        return true;
      }
    }
    
    return false;
  }

  return true;
}

/**
 * Apply rate limiting for payment endpoints
 */
async function applyPaymentRateLimit(
  req: NextRequest,
  user: PaymentAuthUser,
  endpoint: string
): Promise<{ allowed: boolean; remaining: number; resetTime: number }> {
  const limits = PAYMENT_RATE_LIMITS[endpoint as keyof typeof PAYMENT_RATE_LIMITS] || PAYMENT_RATE_LIMITS.default;

  // Create unique identifier for rate limiting (user + IP)
  const ip = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';
  const identifier = `${user.id}:${ip}`;

  try {
    const result = await rateLimit(`payment:${endpoint}`, limits.requests, limits.window, identifier);
    return {
      allowed: result.success,
      remaining: limits.requests - result.current,
      resetTime: Date.now() + (result.retryAfter * 1000)
    };
  } catch (error) {
    console.error('Rate limiting error:', error);
    // Allow request if rate limiting fails
    return {
      allowed: true,
      remaining: limits.requests,
      resetTime: Date.now() + (limits.window * 1000)
    };
  }
}

/**
 * Main authentication wrapper for payment endpoints
 */
export function withPaymentAuth(
  handler: PaymentRouteHandler,
  options: {
    requireAPIKey?: boolean;
    endpoint?: string;
    allowedRoles?: string[];
  } = {}
): (req: NextRequest) => Promise<Response> {
  return async (req: NextRequest) => {
    try {
      // 1. API Key validation (if required)
      if (options.requireAPIKey) {
        const apiKey = req.headers.get('X-API-Key');
        if (!validatePaymentAPIKey(apiKey || undefined)) {
          return NextResponse.json(
            { 
              error: 'Invalid or missing API key',
              code: 'INVALID_API_KEY'
            },
            { status: 401 }
          );
        }
      }

      // 2. JWT Authentication
      const authHeader = req.headers.get('Authorization');
      if (!authHeader) {
        return NextResponse.json(
          { 
            error: 'Missing authorization header',
            code: 'MISSING_AUTH_HEADER'
          },
          { status: 401 }
        );
      }

      let user: PaymentAuthUser;
      try {
        user = await validateJWTToken(authHeader);
      } catch (error) {
        return NextResponse.json(
          { 
            error: 'Authentication failed',
            code: 'AUTH_FAILED',
            details: error instanceof Error ? error.message : 'Unknown error'
          },
          { status: 401 }
        );
      }

      // 3. Role-based authorization
      if (options.allowedRoles && !options.allowedRoles.includes(user.role)) {
        return NextResponse.json(
          { 
            error: 'Insufficient permissions',
            code: 'INSUFFICIENT_PERMISSIONS',
            requiredRoles: options.allowedRoles,
            userRole: user.role
          },
          { status: 403 }
        );
      }

      // 4. Rate limiting
      const rateLimitResult = await applyPaymentRateLimit(
        req, 
        user, 
        options.endpoint || 'default'
      );

      if (!rateLimitResult.allowed) {
        return NextResponse.json(
          { 
            error: 'Rate limit exceeded',
            code: 'RATE_LIMIT_EXCEEDED',
            retryAfter: Math.ceil((rateLimitResult.resetTime - Date.now()) / 1000)
          },
          { 
            status: 429,
            headers: {
              'Retry-After': Math.ceil((rateLimitResult.resetTime - Date.now()) / 1000).toString(),
              'X-RateLimit-Remaining': rateLimitResult.remaining.toString(),
              'X-RateLimit-Reset': rateLimitResult.resetTime.toString()
            }
          }
        );
      }

      // 5. Create Supabase client with service role for payment operations
      const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

      // 6. Create authentication context
      const context: PaymentAuthContext = {
        user,
        supabase: supabase as ReturnType<typeof createClient>,
        rateLimitInfo: {
          remaining: rateLimitResult.remaining,
          resetTime: rateLimitResult.resetTime
        }
      };

      // 7. Call the protected handler
      const response = await handler(req, context);

      // 8. Add rate limit headers to response
      response.headers.set('X-RateLimit-Remaining', rateLimitResult.remaining.toString());
      response.headers.set('X-RateLimit-Reset', rateLimitResult.resetTime.toString());

      return response;

    } catch (error) {
      console.error('Payment authentication error:', error);
      return NextResponse.json(
        {
          error: 'Internal authentication error',
          code: 'INTERNAL_AUTH_ERROR'
        },
        { status: 500 }
      );
    }
  };
}

/**
 * Validate tenant access in request body
 */
export async function validateTenantInRequest(
  req: NextRequest,
  user: PaymentAuthUser,
  supabase: ReturnType<typeof createClient>
): Promise<{ valid: boolean; tenantId?: string; error?: string }> {
  try {
    const body = await req.json();
    const requestedTenantId = body.tenant_id;

    if (!requestedTenantId) {
      return {
        valid: false,
        error: 'Missing tenant_id in request body'
      };
    }

    const hasAccess = await verifyTenantAccess(user, requestedTenantId, supabase);

    if (!hasAccess) {
      return {
        valid: false,
        error: 'Access denied to requested tenant'
      };
    }

    return {
      valid: true,
      tenantId: requestedTenantId
    };
  } catch (error) {
    return {
      valid: false,
      error: 'Invalid request body'
    };
  }
}

/**
 * Security headers for payment endpoints
 */
export function addPaymentSecurityHeaders(response: Response): Response {
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
  response.headers.set('Pragma', 'no-cache');
  response.headers.set('Expires', '0');

  return response;
}
