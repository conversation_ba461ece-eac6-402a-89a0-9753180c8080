/**
 * Encrypted Payment Storage Service
 * 
 * Handles secure storage and retrieval of encrypted payment data
 * with automatic encryption/decryption and audit logging.
 */

import { createClient } from '@supabase/supabase-js';
import type { Database } from '@/lib/supabase/database.types';
import type {
  EncryptedData
} from '@/lib/security/payment-encryption';
import {
  encryptPaymentData,
  decryptPaymentData,
  encryptPaymentFields,
  decryptPaymentFields,
  hashPaymentData,
  generateSecureToken
} from '@/lib/security/payment-encryption';

// Types
export interface PaymentDataRecord {
  id: string;
  tenant_id: string;
  user_id: string;
  data_type: 'payment_method' | 'transaction' | 'customer_data' | 'billing_address';
  encrypted_data: EncryptedData;
  data_hash: string;
  metadata?: Record<string, unknown>;
  created_at: string;
  updated_at: string;
  expires_at?: string;
}

export interface StorageResult {
  success: boolean;
  id?: string;
  error?: string;
}

export interface RetrievalResult {
  success: boolean;
  data?: Record<string, unknown>;
  error?: string;
}

export class EncryptedPaymentStorage {
  private supabase: ReturnType<typeof createClient<Database>>;

  constructor() {
    this.supabase = createClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
  }

  /**
   * Store encrypted payment data
   */
  async storePaymentData(
    tenantId: string,
    userId: string,
    dataType: PaymentDataRecord['data_type'],
    paymentData: Record<string, unknown>,
    options: {
      expiresInHours?: number;
      metadata?: Record<string, unknown>;
    } = {}
  ): Promise<StorageResult> {
    try {
      // Encrypt sensitive fields
      const { encrypted, errors } = encryptPaymentFields(paymentData);
      
      if (errors.length > 0) {
        return {
          success: false,
          error: `Encryption errors: ${errors.join(', ')}`
        };
      }

      // Create data hash for integrity verification
      const dataString = JSON.stringify(paymentData);
      const dataHash = hashPaymentData(dataString);

      // Encrypt the entire payload
      const encryptionResult = encryptPaymentData(JSON.stringify(encrypted));
      if (!encryptionResult.success || !encryptionResult.data) {
        return {
          success: false,
          error: `Failed to encrypt payment data: ${encryptionResult.error}`
        };
      }

      // Calculate expiration time
      let expiresAt: string | undefined;
      if (options.expiresInHours) {
        const expiration = new Date();
        expiration.setHours(expiration.getHours() + options.expiresInHours);
        expiresAt = expiration.toISOString();
      }

      // Store in database
      const { data, error } = await this.supabase
        .schema('security')
        .from('encrypted_payment_data')
        .insert({
          tenant_id: tenantId,
          data_type: dataType,
          encrypted_data: JSON.stringify(encryptionResult.data),
          encryption_key_id: 'default',
          metadata: {
            user_id: userId,
            data_hash: dataHash,
            ...options.metadata
          },
          expires_at: expiresAt
        })
        .select('id')
        .single();

      if (error) {
        return {
          success: false,
          error: `Database error: ${error.message}`
        };
      }

      // Log the storage operation
      await this.logPaymentDataAccess(
        tenantId,
        userId,
        'data_stored',
        data.id,
        { data_type: dataType }
      );

      return {
        success: true,
        id: data.id
      };

    } catch (error) {
      return {
        success: false,
        error: `Storage failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Retrieve and decrypt payment data
   */
  async retrievePaymentData(
    id: string,
    tenantId: string,
    userId: string
  ): Promise<RetrievalResult> {
    try {
      // Retrieve from database with access control
      const { data, error } = await this.supabase
        .schema('security')
        .from('encrypted_payment_data')
        .select('*')
        .eq('id', id)
        .eq('tenant_id', tenantId)
        .eq('user_id', userId)
        .single();

      if (error) {
        return {
          success: false,
          error: `Database error: ${error.message}`
        };
      }

      if (!data) {
        return {
          success: false,
          error: 'Payment data not found or access denied'
        };
      }

      // Check expiration
      if (data.expires_at && new Date(data.expires_at) < new Date()) {
        // Delete expired data
        await this.deletePaymentData(id, tenantId, userId);
        return {
          success: false,
          error: 'Payment data has expired'
        };
      }

      // Decrypt the payload
      const encryptedDataObj = JSON.parse(data.encrypted_data) as EncryptedData;
      const decryptionResult = decryptPaymentData(encryptedDataObj);
      if (!decryptionResult.success || !decryptionResult.data) {
        return {
          success: false,
          error: `Failed to decrypt payment data: ${decryptionResult.error}`
        };
      }

      // Parse decrypted data
      let parsedData: Record<string, unknown>;
      try {
        parsedData = JSON.parse(decryptionResult.data);
      } catch (error) {
        return {
          success: false,
          error: 'Failed to parse decrypted data'
        };
      }

      // Decrypt individual fields
      const { decrypted, errors } = decryptPaymentFields(parsedData);
      
      if (errors.length > 0) {
        console.warn('Field decryption errors:', errors);
      }

      // Verify data integrity
      const dataString = JSON.stringify(decrypted);
      const dataHash = hashPaymentData(dataString);
      
      const storedDataHash = (data.metadata as any)?.data_hash;
      if (dataHash !== storedDataHash) {
        return {
          success: false,
          error: 'Data integrity check failed'
        };
      }

      // Log the retrieval operation
      await this.logPaymentDataAccess(
        tenantId,
        userId,
        'data_retrieved',
        id,
        { data_type: data.data_type }
      );

      return {
        success: true,
        data: decrypted
      };

    } catch (error) {
      return {
        success: false,
        error: `Retrieval failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Delete payment data
   */
  async deletePaymentData(
    id: string,
    tenantId: string,
    userId: string
  ): Promise<StorageResult> {
    try {
      const { error } = await this.supabase
        .schema('security')
        .from('encrypted_payment_data')
        .delete()
        .eq('id', id)
        .eq('tenant_id', tenantId)
        .eq('user_id', userId);

      if (error) {
        return {
          success: false,
          error: `Database error: ${error.message}`
        };
      }

      // Log the deletion operation
      await this.logPaymentDataAccess(
        tenantId,
        userId,
        'data_deleted',
        id,
        {}
      );

      return {
        success: true
      };

    } catch (error) {
      return {
        success: false,
        error: `Deletion failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Clean up expired payment data
   */
  async cleanupExpiredData(): Promise<{ deleted: number; errors: string[] }> {
    try {
      const { data, error } = await this.supabase
        .schema('security')
        .from('encrypted_payment_data')
        .delete()
        .lt('expires_at', new Date().toISOString())
        .select('id');

      if (error) {
        return {
          deleted: 0,
          errors: [`Database error: ${error.message}`]
        };
      }

      return {
        deleted: data?.length || 0,
        errors: []
      };

    } catch (error) {
      return {
        deleted: 0,
        errors: [`Cleanup failed: ${error instanceof Error ? error.message : 'Unknown error'}`]
      };
    }
  }

  /**
   * Log payment data access for audit trail
   */
  private async logPaymentDataAccess(
    tenantId: string,
    userId: string,
    action: string,
    dataId: string,
    details: Record<string, unknown>
  ): Promise<void> {
    try {
      await this.supabase
        .schema('security')
        .from('events')
        .insert({
          event_type: `payment.${action}`,
          event_category: 'payment_data',
          user_id: userId,
          tenant_id: tenantId,
          details: {
            data_id: dataId,
            ...details
          },
          created_at: new Date().toISOString()
        });
    } catch (error) {
      console.error('Failed to log payment data access:', error);
    }
  }

  /**
   * Get payment data access history
   */
  async getAccessHistory(
    tenantId: string,
    userId?: string,
    limit: number = 100
  ): Promise<any[]> {
    try {
      let query = this.supabase
        .schema('security')
        .from('events')
        .select('*')
        .eq('tenant_id', tenantId)
        .eq('event_category', 'payment_data')
        .order('created_at', { ascending: false })
        .limit(limit);

      if (userId) {
        query = query.eq('user_id', userId);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Failed to get access history:', error);
        return [];
      }

      return data || [];

    } catch (error) {
      console.error('Failed to get access history:', error);
      return [];
    }
  }
}
