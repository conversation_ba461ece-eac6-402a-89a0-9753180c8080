/**
 * SEPA Direct Debit Service
 * 
 * Service for implementing SEPA Direct Debit support for European customers
 * with proper mandate handling, IBAN validation, and Stripe integration.
 */

import <PERSON><PERSON> from 'stripe';
import { createClient } from '@supabase/supabase-js';
import type {
  SupportedRegion,
  SupportedCurrency} from '@/lib/types/payment-methods';
import {
  PaymentMethodError,
  StripeIntegrationError
} from '@/lib/types/payment-methods';

// SEPA-specific types
export interface SEPAMandateData {
  id: string;
  customer_id: string;
  iban: string;
  bic?: string;
  account_holder_name: string;
  mandate_reference: string;
  mandate_url: string;
  mandate_acceptance_date: string;
  mandate_acceptance_type: 'online' | 'offline';
  mandate_acceptance_user_agent?: string;
  mandate_acceptance_ip?: string;
  status: 'pending' | 'active' | 'inactive' | 'cancelled';
  created_at: string;
  updated_at: string;
}

export interface SEPAPaymentMethodRequest {
  iban: string;
  bic?: string;
  account_holder_name: string;
  customer_email: string;
  customer_name: string;
  country_code: SupportedRegion;
  currency_code: SupportedCurrency;
  tenant_id: string;
  mandate_acceptance_type: 'online' | 'offline';
  mandate_acceptance_ip?: string;
  mandate_acceptance_user_agent?: string;
}

export interface SEPAPaymentMethodResponse {
  stripe_payment_method_id: string;
  mandate_id: string;
  mandate_reference: string;
  mandate_url: string;
  status: 'requires_action' | 'succeeded' | 'processing';
  next_action?: {
    type: string;
    redirect_to_url?: {
      url: string;
      return_url: string;
    };
  };
}

export interface SEPAPaymentRequest {
  payment_method_id: string;
  amount_cents: number;
  currency: SupportedCurrency;
  tenant_id: string;
  description?: string;
  metadata?: Record<string, string>;
}

export interface SEPAPaymentResponse {
  payment_intent_id: string;
  status: 'requires_action' | 'requires_confirmation' | 'processing' | 'succeeded' | 'canceled';
  client_secret?: string;
  next_action?: any;
  error_message?: string;
}

export class SEPADirectDebitService {
  private stripe: Stripe;
  private supabase: any;

  constructor() {
    this.stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: '2025-06-30.basil'
    });
    
    this.supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
  }

  /**
   * Create SEPA Direct Debit payment method with mandate
   */
  async createSEPAPaymentMethod(request: SEPAPaymentMethodRequest): Promise<SEPAPaymentMethodResponse> {
    try {
      // Validate IBAN format
      const ibanValidation = this.validateIBAN(request.iban);
      if (!ibanValidation.isValid) {
        throw new PaymentMethodError(
          `Invalid IBAN: ${ibanValidation.error}`,
          'VALIDATION_ERROR',
          undefined,
          request.country_code
        );
      }

      // Create or get Stripe customer
      const customer = await this.getOrCreateStripeCustomer(
        request.customer_email,
        request.customer_name,
        request.tenant_id
      );

      // Create SEPA Direct Debit payment method
      const paymentMethod = await this.stripe.paymentMethods.create({
        type: 'sepa_debit',
        sepa_debit: {
          iban: request.iban.replace(/\s/g, '').toUpperCase()
        },
        billing_details: {
          name: request.account_holder_name,
          email: request.customer_email
        }
      });

      // Attach payment method to customer
      await this.stripe.paymentMethods.attach(paymentMethod.id, {
        customer: customer.id
      });

      // Create mandate record in database
      const mandateData: Omit<SEPAMandateData, 'id' | 'created_at' | 'updated_at'> = {
        customer_id: customer.id,
        iban: request.iban.replace(/\s/g, '').toUpperCase(),
        bic: request.bic,
        account_holder_name: request.account_holder_name,
        mandate_reference: this.generateMandateReference(request.tenant_id),
        mandate_url: '', // Will be updated after mandate creation
        mandate_acceptance_date: new Date().toISOString(),
        mandate_acceptance_type: request.mandate_acceptance_type,
        mandate_acceptance_user_agent: request.mandate_acceptance_user_agent,
        mandate_acceptance_ip: request.mandate_acceptance_ip,
        status: 'pending'
      };

      // Store mandate in database
      const { data: mandate, error: mandateError } = await this.supabase
        .from('sepa_mandates')
        .insert(mandateData)
        .select()
        .single();

      if (mandateError) {
        throw new PaymentMethodError(
          `Failed to store SEPA mandate: ${mandateError.message}`,
          'DATABASE_ERROR'
        );
      }

      // Create setup intent for mandate confirmation
      const setupIntent = await this.stripe.setupIntents.create({
        customer: customer.id,
        payment_method: paymentMethod.id,
        payment_method_types: ['sepa_debit'],
        usage: 'off_session',
        confirm: true,
        mandate_data: {
          customer_acceptance: {
            type: request.mandate_acceptance_type,
            online: request.mandate_acceptance_type === 'online' ? {
              ip_address: request.mandate_acceptance_ip || '',
              user_agent: request.mandate_acceptance_user_agent || ''
            } : undefined
          }
        }
      });

      // Update mandate with setup intent information
      await this.supabase
        .from('sepa_mandates')
        .update({
          mandate_url: setupIntent.next_action?.redirect_to_url?.url || '',
          status: setupIntent.status === 'succeeded' ? 'active' : 'pending'
        })
        .eq('id', mandate.id);

      return {
        stripe_payment_method_id: paymentMethod.id,
        mandate_id: mandate.id,
        mandate_reference: mandate.mandate_reference,
        mandate_url: setupIntent.next_action?.redirect_to_url?.url || '',
        status: setupIntent.status as any,
        next_action: setupIntent.next_action ? {
          type: setupIntent.next_action.type,
          redirect_to_url: setupIntent.next_action.redirect_to_url ? {
            url: setupIntent.next_action.redirect_to_url.url || '',
            return_url: setupIntent.next_action.redirect_to_url.return_url || ''
          } : undefined
        } : undefined
      };

    } catch (error) {
      if (error instanceof PaymentMethodError) {
        throw error;
      }

      if (error instanceof Stripe.errors.StripeError) {
        throw new StripeIntegrationError(
          `Stripe SEPA error: ${error.message}`,
          error.code || 'STRIPE_ERROR',
          error.type
        );
      }

      throw new PaymentMethodError(
        `Failed to create SEPA payment method: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'SEPA_CREATION_ERROR'
      );
    }
  }

  /**
   * Process SEPA Direct Debit payment
   */
  async processSEPAPayment(request: SEPAPaymentRequest): Promise<SEPAPaymentResponse> {
    try {
      // Get payment method details
      const paymentMethod = await this.stripe.paymentMethods.retrieve(request.payment_method_id);
      
      if (paymentMethod.type !== 'sepa_debit') {
        throw new PaymentMethodError(
          'Payment method is not SEPA Direct Debit',
          'INVALID_PAYMENT_METHOD'
        );
      }

      // Create payment intent
      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: request.amount_cents,
        currency: request.currency.toLowerCase(),
        payment_method: request.payment_method_id,
        customer: paymentMethod.customer as string,
        payment_method_types: ['sepa_debit'],
        confirmation_method: 'manual',
        confirm: true,
        description: request.description || `Payment for tenant ${request.tenant_id}`,
        metadata: {
          tenant_id: request.tenant_id,
          payment_type: 'sepa_direct_debit',
          ...request.metadata
        }
      });

      // Log payment processing
      await this.logSEPAPayment({
        payment_intent_id: paymentIntent.id,
        payment_method_id: request.payment_method_id,
        amount_cents: request.amount_cents,
        currency: request.currency,
        tenant_id: request.tenant_id,
        status: paymentIntent.status,
        processing_time_ms: 0 // Will be updated
      });

      return {
        payment_intent_id: paymentIntent.id,
        status: paymentIntent.status as any,
        client_secret: paymentIntent.client_secret || undefined,
        next_action: paymentIntent.next_action,
        error_message: paymentIntent.last_payment_error?.message
      };

    } catch (error) {
      if (error instanceof PaymentMethodError) {
        throw error;
      }

      if (error instanceof Stripe.errors.StripeError) {
        throw new StripeIntegrationError(
          `Stripe SEPA payment error: ${error.message}`,
          error.code || 'STRIPE_ERROR',
          error.type
        );
      }

      throw new PaymentMethodError(
        `Failed to process SEPA payment: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'SEPA_PAYMENT_ERROR'
      );
    }
  }

  /**
   * Get SEPA mandate status
   */
  async getMandateStatus(mandateId: string): Promise<SEPAMandateData> {
    try {
      const { data: mandate, error } = await this.supabase
        .from('sepa_mandates')
        .select('*')
        .eq('id', mandateId)
        .single();

      if (error) {
        throw new PaymentMethodError(
          `Failed to get mandate: ${error.message}`,
          'DATABASE_ERROR'
        );
      }

      return mandate;
    } catch (error) {
      throw new PaymentMethodError(
        `Failed to get mandate status: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'MANDATE_RETRIEVAL_ERROR'
      );
    }
  }

  /**
   * Cancel SEPA mandate
   */
  async cancelMandate(mandateId: string): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('sepa_mandates')
        .update({
          status: 'cancelled',
          updated_at: new Date().toISOString()
        })
        .eq('id', mandateId);

      if (error) {
        throw new PaymentMethodError(
          `Failed to cancel mandate: ${error.message}`,
          'DATABASE_ERROR'
        );
      }
    } catch (error) {
      throw new PaymentMethodError(
        `Failed to cancel mandate: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'MANDATE_CANCELLATION_ERROR'
      );
    }
  }

  /**
   * Validate IBAN using MOD-97 checksum
   */
  private validateIBAN(iban: string): { isValid: boolean; error?: string } {
    // Remove spaces and convert to uppercase
    const cleanIban = iban.replace(/\s/g, '').toUpperCase();

    // Check length (15-34 characters)
    if (cleanIban.length < 15 || cleanIban.length > 34) {
      return { isValid: false, error: 'IBAN length must be between 15 and 34 characters' };
    }

    // Check format (2 letters + 2 digits + alphanumeric)
    if (!/^[A-Z]{2}[0-9]{2}[A-Z0-9]+$/.test(cleanIban)) {
      return { isValid: false, error: 'Invalid IBAN format' };
    }

    // MOD-97 checksum validation
    const rearranged = cleanIban.slice(4) + cleanIban.slice(0, 4);
    const numericString = rearranged.replace(/[A-Z]/g, (char) => 
      (char.charCodeAt(0) - 55).toString()
    );

    // Calculate MOD-97
    let remainder = 0;
    for (let i = 0; i < numericString.length; i++) {
      remainder = (remainder * 10 + parseInt(numericString[i])) % 97;
    }

    if (remainder !== 1) {
      return { isValid: false, error: 'Invalid IBAN checksum' };
    }

    return { isValid: true };
  }

  /**
   * Generate unique mandate reference
   */
  private generateMandateReference(tenantId: string): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 8);
    return `SEPA-${tenantId.substring(0, 8)}-${timestamp}-${random}`.toUpperCase();
  }

  /**
   * Get or create Stripe customer
   */
  private async getOrCreateStripeCustomer(email: string, name: string, tenantId: string): Promise<Stripe.Customer> {
    // Try to find existing customer
    const customers = await this.stripe.customers.list({
      email: email,
      limit: 1
    });

    if (customers.data.length > 0) {
      return customers.data[0];
    }

    // Create new customer
    return await this.stripe.customers.create({
      email: email,
      name: name,
      metadata: {
        tenant_id: tenantId
      }
    });
  }

  /**
   * Log SEPA payment processing
   */
  private async logSEPAPayment(logData: any): Promise<void> {
    try {
      await this.supabase
        .from('payment_method_processing_logs')
        .insert({
          ...logData,
          payment_method_type: 'sepa_debit',
          created_at: new Date().toISOString()
        });
    } catch (error) {
      // Log error but don't throw - payment processing should continue
      console.error('Failed to log SEPA payment:', error);
    }
  }
}

export default SEPADirectDebitService;
