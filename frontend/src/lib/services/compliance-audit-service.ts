/**
 * Compliance Audit Service (Simplified)
 * 
 * Streamlined compliance validation focusing on 4 essential frameworks:
 * 1. PCI DSS (Payment Card Industry Data Security Standard)
 * 2. GDPR (General Data Protection Regulation) 
 * 3. SOX (Sarbanes-Oxley Act)
 * 4. Regional Payment Compliance (SEPA/PSD2)
 */

// Essential compliance frameworks only
export type ComplianceFramework = 'PCI_DSS' | 'GDPR' | 'SOX' | 'REGIONAL_PAYMENT';

export interface ComplianceResult {
  compliant: boolean;
  score: number;
  violations: ComplianceViolation[];
  level?: string;
  recommendations?: string[];
}

export interface ComplianceViolation {
  requirement: string;
  severity: 'critical' | 'high' | 'medium' | 'low';
  message: string;
  article?: string;
}

export interface GDPRResult extends ComplianceResult {
  dataSubjectRights: string[];
}

export interface SOXResult extends ComplianceResult {
  section302Compliant: boolean;
  section404Compliant: boolean;
  materialWeaknesses: string[];
  significantDeficiencies: string[];
}

export interface SEPAResult extends ComplianceResult {
  creditorIdentifierValid: boolean;
}

export interface PSD2Result extends ComplianceResult {
  scaCompliant: boolean;
}

export class ComplianceAuditService {
  
  /**
   * Validate PCI DSS compliance (simplified to essential requirements)
   */
  async validatePCIDSS(paymentData: Record<string, boolean>): Promise<ComplianceResult> {
    const violations: ComplianceViolation[] = [];
    
    // Essential PCI DSS requirements only
    const requirements = [
      { key: 'encryption_at_rest', severity: 'critical' as const, message: 'Data must be encrypted at rest' },
      { key: 'encryption_in_transit', severity: 'critical' as const, message: 'Data must be encrypted in transit' },
      { key: 'access_controls', severity: 'critical' as const, message: 'Access controls must be implemented' },
      { key: 'audit_logging', severity: 'high' as const, message: 'Audit logging must be enabled' },
      { key: 'network_security', severity: 'high' as const, message: 'Network security controls required' }
    ];
    
    requirements.forEach(req => {
      if (!paymentData[req.key]) {
        violations.push({
          requirement: req.key,
          severity: req.severity,
          message: req.message
        });
      }
    });
    
    const score = Math.max(0, 100 - (violations.length * 20));
    const compliant = violations.filter(v => v.severity === 'critical').length === 0 && score >= 80;
    
    return {
      compliant,
      score,
      violations,
      level: score >= 95 ? 'Level 1' : score >= 80 ? 'Level 2' : 'Non-compliant'
    };
  }
  
  /**
   * Validate GDPR compliance (simplified to core requirements)
   */
  async validateGDPR(dataProtection: Record<string, unknown>): Promise<GDPRResult> {
    const violations: ComplianceViolation[] = [];
    
    // Core GDPR requirements
    const requirements = [
      { key: 'lawful_basis', article: 'Article 6', message: 'Lawful basis for processing required' },
      { key: 'data_minimization', article: 'Article 5', message: 'Data minimization principle must be followed' },
      { key: 'data_subject_rights', article: 'Article 12-22', message: 'Data subject rights must be implemented' },
      { key: 'breach_notification', article: 'Article 33', message: 'Breach notification procedures required' }
    ];
    
    requirements.forEach(req => {
      if (!dataProtection[req.key]) {
        violations.push({
          requirement: req.key,
          severity: 'high',
          message: req.message,
          article: req.article
        });
      }
    });
    
    const score = Math.max(0, 100 - (violations.length * 25));
    const compliant = violations.length === 0;
    
    return {
      compliant,
      score,
      violations,
      dataSubjectRights: [
        'Right to information',
        'Right of access', 
        'Right to rectification',
        'Right to erasure',
        'Right to data portability'
      ]
    };
  }
  
  /**
   * Validate SOX compliance (simplified to essential controls)
   */
  async validateSOX(financialControls: Record<string, boolean>): Promise<SOXResult> {
    const violations: ComplianceViolation[] = [];
    const materialWeaknesses: string[] = [];
    const significantDeficiencies: string[] = [];
    
    // Essential SOX requirements
    const criticalControls = [
      'internal_controls',
      'financial_reporting_accuracy',
      'management_assessment'
    ];
    
    const importantControls = [
      'disclosure_controls',
      'audit_committee_independence',
      'ceo_cfo_certification'
    ];
    
    criticalControls.forEach(control => {
      if (!financialControls[control]) {
        materialWeaknesses.push(control);
        violations.push({
          requirement: control,
          severity: 'critical',
          message: `${control.replace(/_/g, ' ')} is a material weakness`
        });
      }
    });
    
    importantControls.forEach(control => {
      if (!financialControls[control]) {
        significantDeficiencies.push(control);
        violations.push({
          requirement: control,
          severity: 'high',
          message: `${control.replace(/_/g, ' ')} deficiency identified`
        });
      }
    });
    
    const score = Math.max(0, 100 - (materialWeaknesses.length * 30) - (significantDeficiencies.length * 15));
    const compliant = materialWeaknesses.length === 0 && significantDeficiencies.length <= 1;
    
    return {
      compliant,
      score,
      violations,
      section302Compliant: financialControls.disclosure_controls && financialControls.ceo_cfo_certification,
      section404Compliant: financialControls.internal_controls && financialControls.management_assessment,
      materialWeaknesses,
      significantDeficiencies
    };
  }
  
  /**
   * Validate SEPA compliance (simplified)
   */
  async validateSEPA(sepaData: Record<string, unknown>): Promise<SEPAResult> {
    const violations: ComplianceViolation[] = [];
    
    // Basic SEPA requirements
    const requirements = ['mandate_management', 'pre_notification', 'data_protection'];
    
    requirements.forEach(req => {
      if (!sepaData[req]) {
        violations.push({
          requirement: req,
          severity: 'medium',
          message: `${req.replace(/_/g, ' ')} is required for SEPA compliance`
        });
      }
    });
    
    // Validate creditor identifier format
    const creditorId = sepaData.sepa_creditor_identifier as string;
    const creditorIdentifierValid = Boolean(creditorId && /^[A-Z]{2}[0-9]{2}[A-Z0-9]{3}[0-9]{11}$/.test(creditorId));
    
    if (!creditorIdentifierValid) {
      violations.push({
        requirement: 'sepa_creditor_identifier',
        severity: 'high',
        message: 'Invalid SEPA creditor identifier format'
      });
    }
    
    const score = Math.max(0, 100 - (violations.length * 20));
    const compliant = violations.filter(v => v.severity === 'high').length === 0;
    
    return {
      compliant,
      score,
      violations,
      creditorIdentifierValid
    };
  }
  
  /**
   * Validate PSD2 compliance (simplified)
   */
  async validatePSD2(psd2Data: Record<string, boolean>): Promise<PSD2Result> {
    const violations: ComplianceViolation[] = [];
    
    // Core PSD2 requirements
    const requirements = [
      'strong_customer_authentication',
      'operational_resilience',
      'incident_reporting'
    ];
    
    requirements.forEach(req => {
      if (!psd2Data[req]) {
        violations.push({
          requirement: req,
          severity: req === 'strong_customer_authentication' ? 'critical' : 'medium',
          message: `${req.replace(/_/g, ' ')} is required for PSD2 compliance`
        });
      }
    });
    
    const score = Math.max(0, 100 - (violations.length * 25));
    const compliant = violations.filter(v => v.severity === 'critical').length === 0;
    
    return {
      compliant,
      score,
      violations,
      scaCompliant: psd2Data.strong_customer_authentication
    };
  }
  
  /**
   * Validate data subject rights implementation (GDPR helper)
   */
  async validateDataSubjectRights(rightsImplementation: Record<string, boolean>): Promise<{
    compliant: boolean;
    implementedRights: string[];
    missingRights: string[];
  }> {
    const requiredRights = [
      'right_to_information',
      'right_of_access',
      'right_to_rectification',
      'right_to_erasure',
      'right_to_data_portability'
    ];
    
    const implementedRights = requiredRights.filter(right => rightsImplementation[right]);
    const missingRights = requiredRights.filter(right => !rightsImplementation[right]);
    
    return {
      compliant: missingRights.length === 0,
      implementedRights,
      missingRights
    };
  }
}

// Export singleton instance
export const complianceAuditService = new ComplianceAuditService();
export default complianceAuditService;
