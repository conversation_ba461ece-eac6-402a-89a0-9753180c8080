/**
 * Enhanced TOTP Service
 * 
 * Enhanced Time-based One-Time Password service with advanced features
 * including QR code generation, backup codes, and security enhancements.
 */

import { createClient } from '@supabase/supabase-js';
import QRCode from 'qrcode';

// TOTP Configuration
export interface TOTPConfig {
  issuer: string;
  algorithm: 'SHA1' | 'SHA256' | 'SHA512';
  digits: 6 | 8;
  period: number; // seconds
  window: number; // tolerance window
}

// TOTP Setup Response
export interface TOTPSetupResponse {
  factorId: string;
  secret: string;
  qrCodeUri: string;
  qrCodeDataUrl: string;
  backupCodes: string[];
  setupInstructions: string[];
  expiresAt: string;
}

// TOTP Verification Request
export interface TOTPVerificationRequest {
  factorId: string;
  token: string;
  deviceName?: string;
  trustDevice?: boolean;
}

// TOTP Status Response
export interface TOTPStatusResponse {
  isEnabled: boolean;
  factorId?: string;
  deviceName?: string;
  lastUsed?: string;
  backupCodesRemaining: number;
  trustedDevices: TrustedDevice[];
}

// Trusted Device
export interface TrustedDevice {
  id: string;
  name: string;
  fingerprint: string;
  lastUsed: string;
  createdAt: string;
}

// TOTP Error Types
export class TOTPError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 400
  ) {
    super(message);
    this.name = 'TOTPError';
  }
}

export class EnhancedTOTPService {
  private supabase: any;
  private config: TOTPConfig;

  constructor() {
    this.supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    this.config = {
      issuer: process.env.TOTP_ISSUER || 'PI Lawyer AI',
      algorithm: 'SHA1',
      digits: 6,
      period: 30,
      window: 1
    };
  }

  /**
   * Setup TOTP authentication for a user
   */
  async setupTOTP(userId: string, userEmail: string): Promise<TOTPSetupResponse> {
    try {
      // Check if user already has TOTP enabled
      const existingFactor = await this.getActiveTOTPFactor(userId);
      if (existingFactor) {
        throw new TOTPError(
          'TOTP is already enabled for this user',
          'TOTP_ALREADY_ENABLED',
          409
        );
      }

      // Use Supabase MFA enrollment
      const { data, error } = await this.supabase.auth.mfa.enroll({
        factorType: 'totp',
        issuer: this.config.issuer,
        friendlyName: `${this.config.issuer} - ${userEmail}`
      });

      if (error) {
        throw new TOTPError(
          `Failed to enroll TOTP: ${error.message}`,
          'TOTP_ENROLLMENT_FAILED',
          500
        );
      }

      // Generate backup codes
      const backupCodes = this.generateBackupCodes();

      // Generate QR code data URL
      const qrCodeDataUrl = await this.generateQRCodeDataUrl(data.totp.qr_code);

      // Store additional metadata
      await this.storeTOTPMetadata(userId, data.id, backupCodes);

      return {
        factorId: data.id,
        secret: data.totp.secret,
        qrCodeUri: data.totp.qr_code,
        qrCodeDataUrl,
        backupCodes,
        setupInstructions: [
          '1. Install Google Authenticator, Authy, or similar TOTP app',
          '2. Scan the QR code or manually enter the secret key',
          '3. Enter the 6-digit code from your app to verify setup',
          '4. Save your backup codes in a secure location',
          '5. You can optionally trust this device for 30 days'
        ],
        expiresAt: new Date(Date.now() + 10 * 60 * 1000).toISOString() // 10 minutes
      };

    } catch (error) {
      if (error instanceof TOTPError) {
        throw error;
      }
      throw new TOTPError(
        `Failed to setup TOTP: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'TOTP_SETUP_ERROR',
        500
      );
    }
  }

  /**
   * Verify TOTP setup with initial token
   */
  async verifyTOTPSetup(request: TOTPVerificationRequest): Promise<boolean> {
    try {
      // Verify the TOTP token with Supabase
      const { data, error } = await this.supabase.auth.mfa.verify({
        factorId: request.factorId,
        code: request.token
      });

      if (error) {
        throw new TOTPError(
          `TOTP verification failed: ${error.message}`,
          'TOTP_VERIFICATION_FAILED',
          400
        );
      }

      // Update metadata with device information
      if (request.deviceName) {
        await this.updateTOTPMetadata(request.factorId, {
          deviceName: request.deviceName,
          verifiedAt: new Date().toISOString()
        });
      }

      // Add trusted device if requested
      if (request.trustDevice) {
        await this.addTrustedDevice(request.factorId, request.deviceName || 'Unknown Device');
      }

      return true;

    } catch (error) {
      if (error instanceof TOTPError) {
        throw error;
      }
      throw new TOTPError(
        `Failed to verify TOTP: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'TOTP_VERIFICATION_ERROR',
        500
      );
    }
  }

  /**
   * Verify TOTP token for authentication
   */
  async verifyTOTPToken(userId: string, token: string): Promise<boolean> {
    try {
      // Get active TOTP factor
      const factor = await this.getActiveTOTPFactor(userId);
      if (!factor) {
        throw new TOTPError(
          'No active TOTP factor found',
          'TOTP_NOT_ENABLED',
          404
        );
      }

      // Verify with Supabase
      const { data, error } = await this.supabase.auth.mfa.verify({
        factorId: factor.id,
        code: token
      });

      if (error) {
        // Log failed attempt
        await this.logTOTPAttempt(userId, false, error.message);
        return false;
      }

      // Log successful attempt
      await this.logTOTPAttempt(userId, true);

      // Update last used timestamp
      await this.updateTOTPMetadata(factor.id, {
        lastUsed: new Date().toISOString()
      });

      return true;

    } catch (error) {
      if (error instanceof TOTPError) {
        throw error;
      }
      throw new TOTPError(
        `Failed to verify TOTP token: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'TOTP_TOKEN_VERIFICATION_ERROR',
        500
      );
    }
  }

  /**
   * Get TOTP status for a user
   */
  async getTOTPStatus(userId: string): Promise<TOTPStatusResponse> {
    try {
      const factor = await this.getActiveTOTPFactor(userId);
      
      if (!factor) {
        return {
          isEnabled: false,
          backupCodesRemaining: 0,
          trustedDevices: []
        };
      }

      // Get metadata
      const metadata = await this.getTOTPMetadata(factor.id);
      const trustedDevices = await this.getTrustedDevices(factor.id);

      return {
        isEnabled: true,
        factorId: factor.id,
        deviceName: metadata?.deviceName,
        lastUsed: metadata?.lastUsed,
        backupCodesRemaining: metadata?.backupCodesRemaining || 0,
        trustedDevices
      };

    } catch (error) {
      throw new TOTPError(
        `Failed to get TOTP status: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'TOTP_STATUS_ERROR',
        500
      );
    }
  }

  /**
   * Disable TOTP for a user
   */
  async disableTOTP(userId: string): Promise<void> {
    try {
      const factor = await this.getActiveTOTPFactor(userId);
      if (!factor) {
        throw new TOTPError(
          'TOTP is not enabled for this user',
          'TOTP_NOT_ENABLED',
          404
        );
      }

      // Unenroll from Supabase
      const { error } = await this.supabase.auth.mfa.unenroll({
        factorId: factor.id
      });

      if (error) {
        throw new TOTPError(
          `Failed to disable TOTP: ${error.message}`,
          'TOTP_DISABLE_FAILED',
          500
        );
      }

      // Clean up metadata
      await this.deleteTOTPMetadata(factor.id);

    } catch (error) {
      if (error instanceof TOTPError) {
        throw error;
      }
      throw new TOTPError(
        `Failed to disable TOTP: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'TOTP_DISABLE_ERROR',
        500
      );
    }
  }

  /**
   * Generate backup codes
   */
  private generateBackupCodes(count: number = 10): string[] {
    const codes: string[] = [];
    for (let i = 0; i < count; i++) {
      // Generate 8-character alphanumeric code
      const code = Math.random().toString(36).substring(2, 10).toUpperCase();
      codes.push(code);
    }
    return codes;
  }

  /**
   * Generate QR code data URL
   */
  private async generateQRCodeDataUrl(qrCodeUri: string): Promise<string> {
    try {
      return await QRCode.toDataURL(qrCodeUri, {
        width: 256,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });
    } catch (error) {
      throw new TOTPError(
        'Failed to generate QR code',
        'QR_CODE_GENERATION_FAILED',
        500
      );
    }
  }

  /**
   * Get active TOTP factor for user
   */
  private async getActiveTOTPFactor(userId: string): Promise<any> {
    const { data: factors } = await this.supabase.auth.mfa.listFactors();
    return factors?.find((factor: any) => 
      factor.factor_type === 'totp' && 
      factor.status === 'verified'
    );
  }

  /**
   * Store TOTP metadata
   */
  private async storeTOTPMetadata(userId: string, factorId: string, backupCodes: string[]): Promise<void> {
    await this.supabase
      .from('totp_metadata')
      .insert({
        user_id: userId,
        factor_id: factorId,
        backup_codes: backupCodes,
        backup_codes_remaining: backupCodes.length,
        created_at: new Date().toISOString()
      });
  }

  /**
   * Update TOTP metadata
   */
  private async updateTOTPMetadata(factorId: string, updates: Record<string, any>): Promise<void> {
    await this.supabase
      .from('totp_metadata')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('factor_id', factorId);
  }

  /**
   * Get TOTP metadata
   */
  private async getTOTPMetadata(factorId: string): Promise<any> {
    const { data } = await this.supabase
      .from('totp_metadata')
      .select('*')
      .eq('factor_id', factorId)
      .single();
    
    return data;
  }

  /**
   * Delete TOTP metadata
   */
  private async deleteTOTPMetadata(factorId: string): Promise<void> {
    await this.supabase
      .from('totp_metadata')
      .delete()
      .eq('factor_id', factorId);
  }

  /**
   * Add trusted device
   */
  private async addTrustedDevice(factorId: string, deviceName: string): Promise<void> {
    const fingerprint = this.generateDeviceFingerprint();
    
    await this.supabase
      .from('trusted_devices')
      .insert({
        factor_id: factorId,
        name: deviceName,
        fingerprint,
        created_at: new Date().toISOString(),
        expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days
      });
  }

  /**
   * Get trusted devices
   */
  private async getTrustedDevices(factorId: string): Promise<TrustedDevice[]> {
    const { data } = await this.supabase
      .from('trusted_devices')
      .select('*')
      .eq('factor_id', factorId)
      .gt('expires_at', new Date().toISOString());
    
    return data || [];
  }

  /**
   * Generate device fingerprint
   */
  private generateDeviceFingerprint(): string {
    // Simple fingerprint based on timestamp and random data
    return btoa(`${Date.now()}-${Math.random()}`).substring(0, 16);
  }

  /**
   * Log TOTP attempt
   */
  private async logTOTPAttempt(userId: string, success: boolean, error?: string): Promise<void> {
    await this.supabase
      .from('totp_attempts')
      .insert({
        user_id: userId,
        success,
        error_message: error,
        attempted_at: new Date().toISOString()
      });
  }
}

export default EnhancedTOTPService;
