/**
 * Compliance Dashboard Service
 * 
 * Provides comprehensive compliance monitoring and reporting functionality
 * integrating all compliance frameworks (GDPR, CCPA, Professional Responsibility,
 * Data Retention, Consent Management, Data Residency, Regional Disclaimers)
 */

// Types
export interface ComplianceOverview {
  total_frameworks: number
  active_policies: number
  compliance_rate: number
  pending_reviews: number
  last_updated: string
  critical_violations: number
  upcoming_audits: number
}

export interface FrameworkStatus {
  framework: string
  status: 'compliant' | 'warning' | 'violation' | 'pending'
  compliance_rate: number
  last_audit: string
  next_review: string
  active_policies: number
  pending_actions: number
  critical_issues: number
  description: string
  regulatory_body: string
}

export interface ComplianceMetric {
  date: string
  framework: string
  events: number
  violations: number
  compliance_rate: number
  pending_reviews: number
  resolved_issues: number
}

export interface RegionalCompliance {
  region: string
  region_name: string
  frameworks: string[]
  compliance_rate: number
  data_residency_status: 'compliant' | 'warning' | 'violation'
  last_updated: string
  active_users: number
  data_volume_gb: number
  regulatory_requirements: string[]
}

export interface ComplianceAlert {
  id: string
  framework: string
  severity: 'critical' | 'high' | 'medium' | 'low'
  title: string
  description: string
  created_at: string
  due_date: string
  status: 'open' | 'in_progress' | 'resolved'
  assigned_to?: string
  region?: string
}

export interface AuditEvent {
  id: string
  event_type: string
  framework: string
  severity: string
  timestamp: string
  user_id?: string
  region: string
  description: string
  metadata: Record<string, any>
  compliance_status: string
}

export interface ComplianceReport {
  id: string
  report_type: string
  framework: string
  period_start: string
  period_end: string
  generated_at: string
  status: 'generating' | 'completed' | 'failed'
  file_url?: string
  summary: {
    total_events: number
    violations: number
    compliance_rate: number
    recommendations: string[]
  }
}

class ComplianceDashboardService {
  private baseUrl: string

  constructor() {
    this.baseUrl = '/api/compliance'
  }

  /**
   * Get comprehensive compliance overview
   */
  async getComplianceOverview(): Promise<ComplianceOverview> {
    try {
      const response = await fetch(`${this.baseUrl}/overview`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch compliance overview: ${response.statusText}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error('Error fetching compliance overview:', error)
      throw error
    }
  }

  /**
   * Get status for all compliance frameworks
   */
  async getFrameworkStatuses(): Promise<FrameworkStatus[]> {
    try {
      const response = await fetch(`${this.baseUrl}/frameworks/status`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch framework statuses: ${response.statusText}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error('Error fetching framework statuses:', error)
      throw error
    }
  }

  /**
   * Get compliance metrics over time
   */
  async getComplianceMetrics(
    timeframe: string = '30d',
    framework?: string
  ): Promise<ComplianceMetric[]> {
    try {
      const params = new URLSearchParams({ timeframe })
      if (framework) params.append('framework', framework)
      
      const response = await fetch(`${this.baseUrl}/metrics?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch compliance metrics: ${response.statusText}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error('Error fetching compliance metrics:', error)
      throw error
    }
  }

  /**
   * Get regional compliance status
   */
  async getRegionalCompliance(): Promise<RegionalCompliance[]> {
    try {
      const response = await fetch(`${this.baseUrl}/regional`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch regional compliance: ${response.statusText}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error('Error fetching regional compliance:', error)
      throw error
    }
  }

  /**
   * Get active compliance alerts
   */
  async getComplianceAlerts(
    severity?: string,
    framework?: string,
    status?: string
  ): Promise<ComplianceAlert[]> {
    try {
      const params = new URLSearchParams()
      if (severity) params.append('severity', severity)
      if (framework) params.append('framework', framework)
      if (status) params.append('status', status)
      
      const response = await fetch(`${this.baseUrl}/alerts?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch compliance alerts: ${response.statusText}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error('Error fetching compliance alerts:', error)
      throw error
    }
  }

  /**
   * Get recent audit events
   */
  async getAuditEvents(
    limit: number = 100,
    framework?: string,
    severity?: string
  ): Promise<AuditEvent[]> {
    try {
      const params = new URLSearchParams({ limit: limit.toString() })
      if (framework) params.append('framework', framework)
      if (severity) params.append('severity', severity)
      
      const response = await fetch(`${this.baseUrl}/audit/events?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch audit events: ${response.statusText}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error('Error fetching audit events:', error)
      throw error
    }
  }

  /**
   * Generate compliance report
   */
  async generateComplianceReport(
    reportType: string,
    framework: string,
    periodStart: string,
    periodEnd: string
  ): Promise<ComplianceReport> {
    try {
      const response = await fetch(`${this.baseUrl}/reports/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          report_type: reportType,
          framework,
          period_start: periodStart,
          period_end: periodEnd
        })
      })
      
      if (!response.ok) {
        throw new Error(`Failed to generate compliance report: ${response.statusText}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error('Error generating compliance report:', error)
      throw error
    }
  }

  /**
   * Get compliance reports
   */
  async getComplianceReports(
    framework?: string,
    status?: string
  ): Promise<ComplianceReport[]> {
    try {
      const params = new URLSearchParams()
      if (framework) params.append('framework', framework)
      if (status) params.append('status', status)
      
      const response = await fetch(`${this.baseUrl}/reports?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch compliance reports: ${response.statusText}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error('Error fetching compliance reports:', error)
      throw error
    }
  }

  /**
   * Update alert status
   */
  async updateAlertStatus(
    alertId: string,
    status: string,
    notes?: string
  ): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/alerts/${alertId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          status,
          notes
        })
      })
      
      if (!response.ok) {
        throw new Error(`Failed to update alert status: ${response.statusText}`)
      }
    } catch (error) {
      console.error('Error updating alert status:', error)
      throw error
    }
  }

  /**
   * Get compliance statistics for a specific framework
   */
  async getFrameworkStatistics(framework: string): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/frameworks/${framework}/statistics`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch framework statistics: ${response.statusText}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error('Error fetching framework statistics:', error)
      throw error
    }
  }

  /**
   * Trigger compliance audit for a framework
   */
  async triggerComplianceAudit(framework: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/frameworks/${framework}/audit`, {
        method: 'POST'
      })
      
      if (!response.ok) {
        throw new Error(`Failed to trigger compliance audit: ${response.statusText}`)
      }
    } catch (error) {
      console.error('Error triggering compliance audit:', error)
      throw error
    }
  }

  /**
   * Export compliance data
   */
  async exportComplianceData(
    format: 'csv' | 'json' | 'pdf',
    framework?: string,
    dateRange?: { start: string; end: string }
  ): Promise<Blob> {
    try {
      const params = new URLSearchParams({ format })
      if (framework) params.append('framework', framework)
      if (dateRange) {
        params.append('start_date', dateRange.start)
        params.append('end_date', dateRange.end)
      }
      
      const response = await fetch(`${this.baseUrl}/export?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to export compliance data: ${response.statusText}`)
      }
      
      return await response.blob()
    } catch (error) {
      console.error('Error exporting compliance data:', error)
      throw error
    }
  }
}

// Export singleton instance
export const complianceDashboardService = new ComplianceDashboardService()
export default complianceDashboardService
