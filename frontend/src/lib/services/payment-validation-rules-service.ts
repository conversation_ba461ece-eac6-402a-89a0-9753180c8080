/**
 * Payment Validation Rules Service (Simplified)
 *
 * Streamlined validation service focusing on essential validation
 * with direct validation functions instead of complex rule engine.
 */

import type {
  PaymentMethodCode,
  SupportedRegion,
  SupportedCurrency,
  PaymentMethodValidationError
} from '@/lib/types/payment-methods';

// Validation result interface
export interface ValidationResult {
  isValid: boolean;
  errors: PaymentMethodValidationError[];
  formattedData?: Record<string, unknown>;
}

// Card brand detection patterns
const CARD_PATTERNS = {
  visa: /^4[0-9]{12}(?:[0-9]{3})?$/,
  mastercard: /^5[1-5][0-9]{14}$/,
  amex: /^3[47][0-9]{13}$/,
  discover: /^6(?:011|5[0-9]{2})[0-9]{12}$/
};

// Regional validation patterns
const VALIDATION_PATTERNS = {
  iban: /^[A-Z]{2}[0-9]{2}[A-Z0-9]{4}[0-9]{7}([A-Z0-9]?){0,16}$/,
  bic: /^[A-Z]{4}[A-Z]{2}[A-Z0-9]{2}([A-Z0-9]{3})?$/,
  routing_number: /^[0-9]{9}$/,
  sort_code: /^[0-9]{6}$/,
  postal_code: {
    US: /^[0-9]{5}(-[0-9]{4})?$/,
    CA: /^[A-Z][0-9][A-Z] [0-9][A-Z][0-9]$/,
    GB: /^[A-Z]{1,2}[0-9][A-Z0-9]? [0-9][A-Z]{2}$/,
    DE: /^[0-9]{5}$/,
    FR: /^[0-9]{5}$/
  }
};

export class PaymentValidationRulesService {

  /**
   * Validate card number using Luhn algorithm
   */
  validateCardNumber(cardNumber: string): { isValid: boolean; brand?: string } {
    const cleanNumber = cardNumber.replace(/\s/g, '');

    // Check format
    if (!/^[0-9]{13,19}$/.test(cleanNumber)) {
      return { isValid: false };
    }

    // Detect brand
    let brand: string | undefined;
    for (const [brandName, pattern] of Object.entries(CARD_PATTERNS)) {
      if (pattern.test(cleanNumber)) {
        brand = brandName;
        break;
      }
    }

    // Luhn algorithm validation
    const isValid = this.luhnCheck(cleanNumber);

    return { isValid, brand };
  }


  /**
   * Validate CVC code
   */
  validateCVC(cvc: string, brand?: string): boolean {
    if (brand === 'amex') {
      return /^[0-9]{4}$/.test(cvc);
    }
    return /^[0-9]{3}$/.test(cvc);
  }

  /**
   * Validate expiration date
   */
  validateExpiration(month: number, year: number): boolean {
    if (month < 1 || month > 12) return false;

    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;

    if (year < currentYear) return false;
    if (year === currentYear && month < currentMonth) return false;

    return true;
  }


  /**
   * Validate IBAN using MOD-97 algorithm
   */
  validateIBAN(iban: string): { isValid: boolean; country?: string } {
    const cleanIban = iban.replace(/\s/g, '').toUpperCase();

    // Check format
    if (!VALIDATION_PATTERNS.iban.test(cleanIban)) {
      return { isValid: false };
    }

    // Extract country code
    const country = cleanIban.substring(0, 2);

    // MOD-97 validation
    const rearranged = cleanIban.substring(4) + cleanIban.substring(0, 4);
    const numericString = rearranged.replace(/[A-Z]/g, (char) =>
      (char.charCodeAt(0) - 55).toString()
    );

    const remainder = this.mod97(numericString);
    const isValid = remainder === 1;

    return { isValid, country };
  }

  /**
   * Validate BIC/SWIFT code
   */
  validateBIC(bic: string): boolean {
    return VALIDATION_PATTERNS.bic.test(bic.toUpperCase());
  }


  /**
   * Validate US routing number (ABA)
   */
  validateRoutingNumber(routingNumber: string): boolean {
    if (!VALIDATION_PATTERNS.routing_number.test(routingNumber)) {
      return false;
    }

    // ABA checksum validation
    const digits = routingNumber.split('').map(Number);
    const checksum = (
      3 * (digits[0] + digits[3] + digits[6]) +
      7 * (digits[1] + digits[4] + digits[7]) +
      1 * (digits[2] + digits[5] + digits[8])
    ) % 10;

    return checksum === 0;
  }

  /**
   * Validate UK sort code
   */
  validateSortCode(sortCode: string): boolean {
    const cleanSortCode = sortCode.replace(/-/g, '');
    return VALIDATION_PATTERNS.sort_code.test(cleanSortCode);
  }


  /**
   * Validate postal code by country
   */
  validatePostalCode(postalCode: string, country: SupportedRegion): boolean {
    const pattern = VALIDATION_PATTERNS.postal_code[country as keyof typeof VALIDATION_PATTERNS.postal_code];
    return pattern ? pattern.test(postalCode.toUpperCase()) : true;
  }

  /**
   * Validate payment data for a specific method and country
   */
  validatePaymentData(
    paymentMethodCode: PaymentMethodCode,
    countryCode: SupportedRegion,
    paymentData: Record<string, unknown>
  ): ValidationResult {
    const errors: PaymentMethodValidationError[] = [];
    const formattedData: Record<string, unknown> = { ...paymentData };

    switch (paymentMethodCode) {
      case 'card':
        return this.validateCardData(paymentData);
      case 'sepa_debit':
        return this.validateSEPAData(paymentData, countryCode);
      case 'ach':
        return this.validateACHData(paymentData);
      case 'bacs':
        return this.validateBACSData(paymentData);
      default:
        return { isValid: true, errors: [], formattedData };
    }
  }

  /**
   * Get regional validation rules for a country
   */
  getRegionalRules(countryCode: SupportedRegion) {
    const euCountries = ['BE', 'DE', 'FR', 'NL', 'IT', 'ES', 'AT'];

    return {
      supportedCardBrands: ['visa', 'mastercard', 'amex', 'discover'],
      supportsSepa: euCountries.includes(countryCode),
      requiresIban: euCountries.includes(countryCode),
      requiresZipCode: ['US', 'CA'].includes(countryCode),
      minimumAmount: countryCode === 'US' ? 50 : 100, // cents
      maximumAmount: countryCode === 'US' ? 999999 : 500000 // cents
    };
  }

  /**
   * Validate amount by region and currency
   */
  validateAmount(amount: number, country: SupportedRegion, currency: SupportedCurrency): { isValid: boolean; error?: string } {
    const rules = this.getRegionalRules(country);

    if (amount < rules.minimumAmount) {
      return {
        isValid: false,
        error: `Minimum amount is ${rules.minimumAmount} ${currency.toLowerCase()}`
      };
    }

    if (amount > rules.maximumAmount) {
      return {
        isValid: false,
        error: `Maximum amount is ${rules.maximumAmount} ${currency.toLowerCase()}`
      };
    }

    return { isValid: true };
  }

  /**
   * Validate address by country
   */
  validateAddress(address: Record<string, unknown>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!address.line1) errors.push('Address line 1 is required');
    if (!address.city) errors.push('City is required');
    if (!address.country) errors.push('Country is required');

    // Country-specific validation
    if (address.country === 'US') {
      if (!address.state) errors.push('State is required for US addresses');
      if (!address.postal_code) errors.push('ZIP code is required for US addresses');
      else if (!this.validatePostalCode(address.postal_code as string, 'US')) {
        errors.push('Invalid ZIP code format');
      }
    }

    return { isValid: errors.length === 0, errors };
  }
  

  // Private helper methods for specific payment method validation

  private validateCardData(paymentData: Record<string, unknown>): ValidationResult {
    const errors: PaymentMethodValidationError[] = [];
    const formattedData = { ...paymentData };

    // Validate card number
    if (paymentData.number) {
      const cardResult = this.validateCardNumber(paymentData.number as string);
      if (!cardResult.isValid) {
        errors.push({ field: 'number', message: 'Invalid card number', code: 'INVALID_CARD_NUMBER' });
      } else {
        formattedData.number = (paymentData.number as string).replace(/\s/g, '');
        formattedData.brand = cardResult.brand;
      }
    }

    // Validate CVC
    if (paymentData.cvc && !this.validateCVC(paymentData.cvc as string, formattedData.brand as string)) {
      errors.push({ field: 'cvc', message: 'Invalid CVC', code: 'INVALID_CVC' });
    }

    // Validate expiration
    if (paymentData.exp_month && paymentData.exp_year) {
      if (!this.validateExpiration(paymentData.exp_month as number, paymentData.exp_year as number)) {
        errors.push({ field: 'expiry', message: 'Card is expired', code: 'EXPIRED_CARD' });
      }
    }

    return { isValid: errors.length === 0, errors, formattedData };
  }

  private validateSEPAData(paymentData: Record<string, unknown>, country: SupportedRegion): ValidationResult {
    const errors: PaymentMethodValidationError[] = [];
    const formattedData = { ...paymentData };

    // Validate IBAN
    if (paymentData.iban) {
      const ibanResult = this.validateIBAN(paymentData.iban as string);
      if (!ibanResult.isValid) {
        errors.push({ field: 'iban', message: 'Invalid IBAN', code: 'INVALID_IBAN' });
      } else {
        formattedData.iban = (paymentData.iban as string).replace(/\s/g, '').toUpperCase();

        // Country-specific IBAN validation
        if (country === 'DE' && !(formattedData.iban as string).startsWith('DE')) {
          errors.push({ field: 'iban', message: 'German customers must use German IBAN', code: 'INVALID_COUNTRY_IBAN' });
        }
      }
    }

    // Validate BIC (required for Belgium)
    if (country === 'BE' && !paymentData.bic) {
      errors.push({ field: 'bic', message: 'BIC is required for Belgian SEPA payments', code: 'REQUIRED_FIELD' });
    } else if (paymentData.bic && !this.validateBIC(paymentData.bic as string)) {
      errors.push({ field: 'bic', message: 'Invalid BIC format', code: 'INVALID_BIC' });
    }

    return { isValid: errors.length === 0, errors, formattedData };
  }

  private validateACHData(paymentData: Record<string, unknown>): ValidationResult {
    const errors: PaymentMethodValidationError[] = [];

    if (!paymentData.routing_number || !paymentData.account_number) {
      errors.push({ field: 'general', message: 'Both routing number and account number are required', code: 'REQUIRED_FIELDS' });
    } else {
      if (!this.validateRoutingNumber(paymentData.routing_number as string)) {
        errors.push({ field: 'routing_number', message: 'Invalid routing number', code: 'INVALID_ROUTING_NUMBER' });
      }
      if (!/^[0-9]{4,17}$/.test(paymentData.account_number as string)) {
        errors.push({ field: 'account_number', message: 'Account number must be 4-17 digits', code: 'INVALID_ACCOUNT_NUMBER' });
      }
    }

    return { isValid: errors.length === 0, errors, formattedData: paymentData };
  }

  private validateBACSData(paymentData: Record<string, unknown>): ValidationResult {
    const errors: PaymentMethodValidationError[] = [];
    const formattedData = { ...paymentData };

    if (!paymentData.sort_code || !paymentData.account_number) {
      errors.push({ field: 'general', message: 'Both sort code and account number are required', code: 'REQUIRED_FIELDS' });
    } else {
      if (!this.validateSortCode(paymentData.sort_code as string)) {
        errors.push({ field: 'sort_code', message: 'Invalid sort code', code: 'INVALID_SORT_CODE' });
      } else {
        formattedData.sort_code = (paymentData.sort_code as string).replace(/-/g, '');
      }
      if (!/^[0-9]{6,8}$/.test(paymentData.account_number as string)) {
        errors.push({ field: 'account_number', message: 'Account number must be 6-8 digits', code: 'INVALID_ACCOUNT_NUMBER' });
      }
    }

    return { isValid: errors.length === 0, errors, formattedData };
  }


  // Algorithm implementations

  private luhnCheck(cardNumber: string): boolean {
    let sum = 0;
    let isEven = false;

    for (let i = cardNumber.length - 1; i >= 0; i--) {
      let digit = parseInt(cardNumber[i]);

      if (isEven) {
        digit *= 2;
        if (digit > 9) {
          digit -= 9;
        }
      }

      sum += digit;
      isEven = !isEven;
    }

    return sum % 10 === 0;
  }

  private mod97(numericString: string): number {
    let remainder = 0;
    for (let i = 0; i < numericString.length; i++) {
      remainder = (remainder * 10 + parseInt(numericString[i])) % 97;
    }
    return remainder;
  }
}

// Export singleton instance
export const paymentValidationRulesService = new PaymentValidationRulesService();
export default paymentValidationRulesService;
