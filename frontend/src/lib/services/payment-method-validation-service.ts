/**
 * Payment Method Validation Service
 * 
 * Comprehensive validation service for regional payment methods including
 * format validation, checksum validation, country-specific requirements,
 * and availability checks for multi-country operations.
 */

import type {
  PaymentMethodValidationRule,
  PaymentMethodValidationError,
  SupportedRegion,
  PaymentMethodCode
} from '@/lib/types/payment-methods';

// Validation rule configuration interface
interface ValidationRuleConfig {
  field: string;
  pattern?: string;
  min_length?: number;
  max_length?: number;
  required?: boolean;
  [key: string]: unknown;
}

// Validation result interface
export interface ValidationResult {
  isValid: boolean;
  field?: string;
  formattedValue?: Record<string, unknown>;
  errors: PaymentMethodValidationError[];
}

// Country-specific validation patterns
const VALIDATION_PATTERNS = {
  // Card validation patterns
  card: {
    number: /^[0-9]{13,19}$/,
    cvv: /^[0-9]{3,4}$/,
    expiry: /^(0[1-9]|1[0-2])\/([0-9]{2})$/
  },
  
  // IBAN validation patterns by country
  iban: {
    BE: /^BE[0-9]{2}[0-9]{12}$/,  // Belgium
    DE: /^DE[0-9]{2}[0-9]{18}$/,  // Germany
    FR: /^FR[0-9]{2}[0-9]{10}[A-Z0-9]{11}$/,  // France
    NL: /^NL[0-9]{2}[A-Z]{4}[0-9]{10}$/,  // Netherlands
    GB: /^GB[0-9]{2}[A-Z]{4}[0-9]{14}$/,  // United Kingdom
    IT: /^IT[0-9]{2}[A-Z][0-9]{10}[A-Z0-9]{12}$/,  // Italy
    ES: /^ES[0-9]{2}[0-9]{20}$/,  // Spain
    AT: /^AT[0-9]{2}[0-9]{16}$/,  // Austria
    CH: /^CH[0-9]{2}[0-9]{5}[A-Z0-9]{12}$/  // Switzerland
  },
  
  // US routing number validation
  routing_number: /^[0-9]{9}$/,
  
  // US account number validation
  account_number: /^[0-9]{4,17}$/,
  
  // Canadian transit number validation
  transit_number: /^[0-9]{5}$/,
  
  // Canadian institution number validation
  institution_number: /^[0-9]{3}$/,
  
  // BIC/SWIFT code validation
  bic: /^[A-Z]{4}[A-Z]{2}[A-Z0-9]{2}([A-Z0-9]{3})?$/,
  
  // Sort code validation (UK)
  sort_code: /^[0-9]{6}$/
};

// IBAN country code to length mapping
const IBAN_LENGTHS: Record<string, number> = {
  AD: 24, AE: 23, AL: 28, AT: 20, AZ: 28, BA: 20, BE: 16, BG: 22,
  BH: 22, BR: 29, BY: 28, CH: 21, CR: 22, CY: 28, CZ: 24, DE: 22,
  DK: 18, DO: 28, EE: 20, EG: 29, ES: 24, FI: 18, FO: 18, FR: 27,
  GB: 22, GE: 22, GI: 23, GL: 18, GR: 27, GT: 28, HR: 21, HU: 28,
  IE: 22, IL: 23, IS: 26, IT: 27, JO: 30, KW: 30, KZ: 20, LB: 28,
  LC: 32, LI: 21, LT: 20, LU: 20, LV: 21, MC: 27, MD: 24, ME: 22,
  MK: 19, MR: 27, MT: 31, MU: 30, NL: 18, NO: 15, PK: 24, PL: 28,
  PS: 29, PT: 25, QA: 29, RO: 24, RS: 22, SA: 24, SE: 24, SI: 19,
  SK: 24, SM: 27, TN: 24, TR: 26, UA: 29, VG: 24, XK: 20
};

export class PaymentMethodValidationService {
  
  /**
   * Validate payment method data based on validation rules
   */
  async validatePaymentData(
    paymentData: Record<string, unknown>,
    validationRules: PaymentMethodValidationRule[],
    paymentMethodCode: PaymentMethodCode,
    countryCode: SupportedRegion
  ): Promise<ValidationResult> {
    const errors: PaymentMethodValidationError[] = [];
    let formattedValue: Record<string, unknown> = { ...paymentData };
    
    // Apply each validation rule
    for (const rule of validationRules) {
      const result = await this.applyValidationRule(
        rule,
        paymentData,
        paymentMethodCode,
        countryCode
      );
      
      if (!result.isValid) {
        errors.push({
          field: result.field || 'general',
          message: rule.error_message,
          code: 'VALIDATION_FAILED'
        });
      }
      
      if (result.formattedValue) {
        formattedValue = { ...formattedValue, ...result.formattedValue };
      }
    }
    
    // Additional country-specific validation
    const countryValidation = await this.validateCountrySpecificRequirements(
      paymentData,
      paymentMethodCode,
      countryCode
    );
    
    if (!countryValidation.isValid) {
      errors.push(...countryValidation.errors);
    }
    
    if (countryValidation.formattedValue) {
      formattedValue = { ...formattedValue, ...countryValidation.formattedValue };
    }
    
    return {
      isValid: errors.length === 0,
      formattedValue,
      errors
    };
  }
  
  /**
   * Apply a single validation rule
   */
  private async applyValidationRule(
    rule: PaymentMethodValidationRule,
    paymentData: Record<string, unknown>,
    paymentMethodCode: PaymentMethodCode,
    countryCode: SupportedRegion
  ): Promise<{
    isValid: boolean;
    field?: string;
    formattedValue?: Record<string, unknown>;
  }> {
    switch (rule.validation_type) {
      case 'format':
        return this.validateFormat(rule, paymentData, paymentMethodCode, countryCode);
      case 'checksum':
        return this.validateChecksum(rule, paymentData, paymentMethodCode);
      case 'length':
        return this.validateLength(rule, paymentData);
      case 'bank_verification':
        return this.validateBankVerification(rule, paymentData, countryCode);
      default:
        return { isValid: true };
    }
  }
  
  /**
   * Validate format rules with comprehensive patterns
   */
  private validateFormat(
    rule: PaymentMethodValidationRule,
    paymentData: Record<string, unknown>,
    paymentMethodCode: PaymentMethodCode,
    countryCode: SupportedRegion
  ): { isValid: boolean; field?: string; formattedValue?: Record<string, unknown> } {
    const ruleConfig = rule.validation_rule as ValidationRuleConfig;
    const field = ruleConfig.field;
    const value = paymentData[field] as string;
    
    if (!value) {
      return { isValid: false, field };
    }
    
    let pattern: RegExp | undefined;
    const formattedValue: Record<string, unknown> = {};
    
    // Get validation pattern based on payment method and field
    switch (paymentMethodCode) {
      case 'card':
        if (field === 'number') {
          pattern = VALIDATION_PATTERNS.card.number;
          // Format card number (remove spaces and validate)
          const cleanNumber = value.replace(/\s/g, '');
          formattedValue[field] = cleanNumber;
          return {
            isValid: pattern.test(cleanNumber) && this.validateLuhnChecksum(cleanNumber),
            field,
            formattedValue
          };
        } else if (field === 'cvv') {
          pattern = VALIDATION_PATTERNS.card.cvv;
        } else if (field === 'expiry') {
          pattern = VALIDATION_PATTERNS.card.expiry;
          // Validate expiry date is in the future
          if (pattern.test(value)) {
            const [month, year] = value.split('/');
            const expiryDate = new Date(2000 + parseInt(year), parseInt(month) - 1);
            const now = new Date();
            return {
              isValid: expiryDate > now,
              field,
              formattedValue: { [field]: value }
            };
          }
        }
        break;
        
      case 'sepa_debit':
        if (field === 'iban') {
          return this.validateIBAN(value, countryCode);
        } else if (field === 'bic') {
          pattern = VALIDATION_PATTERNS.bic;
        }
        break;
        
      case 'ach':
        if (field === 'routing_number') {
          pattern = VALIDATION_PATTERNS.routing_number;
          // Validate ABA routing number checksum
          if (pattern.test(value)) {
            return {
              isValid: this.validateABAChecksum(value),
              field,
              formattedValue: { [field]: value }
            };
          }
        } else if (field === 'account_number') {
          pattern = VALIDATION_PATTERNS.account_number;
        }
        break;
        
      case 'bacs':
        if (field === 'sort_code') {
          pattern = VALIDATION_PATTERNS.sort_code;
          // Format sort code (remove hyphens)
          const cleanSortCode = value.replace(/-/g, '');
          formattedValue[field] = cleanSortCode;
          return {
            isValid: pattern.test(cleanSortCode),
            field,
            formattedValue
          };
        } else if (field === 'account_number') {
          // UK account numbers are 6-8 digits
          pattern = /^[0-9]{6,8}$/;
        }
        break;
    }
    
    if (pattern) {
      return {
        isValid: pattern.test(value),
        field,
        formattedValue: formattedValue[field] ? formattedValue : { [field]: value }
      };
    }
    
    return { isValid: true };
  }
  
  /**
   * Validate IBAN with country-specific rules and checksum
   */
  private validateIBAN(iban: string, countryCode: SupportedRegion): {
    isValid: boolean;
    field: string;
    formattedValue?: Record<string, unknown>;
  } {
    // Remove spaces and convert to uppercase
    const cleanIBAN = iban.replace(/\s/g, '').toUpperCase();
    
    // Check basic format
    if (!/^[A-Z]{2}[0-9]{2}[A-Z0-9]+$/.test(cleanIBAN)) {
      return { isValid: false, field: 'iban' };
    }
    
    // Extract country code and check length
    const ibanCountryCode = cleanIBAN.substring(0, 2);
    const expectedLength = IBAN_LENGTHS[ibanCountryCode];
    
    if (!expectedLength || cleanIBAN.length !== expectedLength) {
      return { isValid: false, field: 'iban' };
    }
    
    // Validate IBAN checksum (MOD-97)
    const isValidChecksum = this.validateIBANChecksum(cleanIBAN);
    
    return {
      isValid: isValidChecksum,
      field: 'iban',
      formattedValue: { iban: cleanIBAN }
    };
  }
  
  /**
   * Validate IBAN checksum using MOD-97 algorithm
   */
  private validateIBANChecksum(iban: string): boolean {
    // Move first 4 characters to end
    const rearranged = iban.substring(4) + iban.substring(0, 4);
    
    // Replace letters with numbers (A=10, B=11, ..., Z=35)
    const numericString = rearranged.replace(/[A-Z]/g, (char) =>
      (char.charCodeAt(0) - 55).toString()
    );
    
    // Calculate MOD-97
    let remainder = 0;
    for (let i = 0; i < numericString.length; i++) {
      remainder = (remainder * 10 + parseInt(numericString[i])) % 97;
    }
    
    return remainder === 1;
  }
  
  /**
   * Validate Luhn checksum for card numbers
   */
  private validateLuhnChecksum(cardNumber: string): boolean {
    let sum = 0;
    let isEven = false;
    
    for (let i = cardNumber.length - 1; i >= 0; i--) {
      let digit = parseInt(cardNumber[i]);
      
      if (isEven) {
        digit *= 2;
        if (digit > 9) {
          digit -= 9;
        }
      }
      
      sum += digit;
      isEven = !isEven;
    }
    
    return sum % 10 === 0;
  }
  
  /**
   * Validate ABA routing number checksum
   */
  private validateABAChecksum(routingNumber: string): boolean {
    const weights = [3, 7, 1, 3, 7, 1, 3, 7, 1];
    let sum = 0;
    
    for (let i = 0; i < 9; i++) {
      sum += parseInt(routingNumber[i]) * weights[i];
    }
    
    return sum % 10 === 0;
  }
  
  /**
   * Validate checksum rules
   */
  private validateChecksum(
    rule: PaymentMethodValidationRule,
    paymentData: Record<string, unknown>,
    paymentMethodCode: PaymentMethodCode
  ): { isValid: boolean; field?: string } {
    const ruleConfig = rule.validation_rule as ValidationRuleConfig;
    const field = ruleConfig.field;
    const value = paymentData[field] as string;
    
    if (!value) {
      return { isValid: false, field };
    }
    
    // Checksum validation is handled in format validation for most cases
    return { isValid: true };
  }
  
  /**
   * Validate length rules
   */
  private validateLength(
    rule: PaymentMethodValidationRule,
    paymentData: Record<string, unknown>
  ): { isValid: boolean; field?: string } {
    const ruleConfig = rule.validation_rule as ValidationRuleConfig;
    const field = ruleConfig.field;
    const value = paymentData[field] as string;
    const minLength = ruleConfig.min_length;
    const maxLength = ruleConfig.max_length;
    
    if (!value) {
      return { isValid: false, field };
    }
    
    const length = value.length;
    const isValid = (!minLength || length >= minLength) && (!maxLength || length <= maxLength);
    
    return { isValid, field };
  }
  
  /**
   * Validate bank verification requirements
   */
  private validateBankVerification(
    rule: PaymentMethodValidationRule,
    paymentData: Record<string, unknown>,
    countryCode: SupportedRegion
  ): { isValid: boolean; field?: string } {
    // Bank verification is typically done server-side
    // This is a placeholder for future implementation
    return { isValid: true };
  }
  
  /**
   * Validate country-specific requirements
   */
  private async validateCountrySpecificRequirements(
    paymentData: Record<string, unknown>,
    paymentMethodCode: PaymentMethodCode,
    countryCode: SupportedRegion
  ): Promise<{
    isValid: boolean;
    formattedValue?: Record<string, unknown>;
    errors: PaymentMethodValidationError[];
  }> {
    const errors: PaymentMethodValidationError[] = [];
    const formattedValue: Record<string, unknown> = {};
    
    // Country-specific validation logic
    switch (countryCode) {
      case 'BE': // Belgium
        if (paymentMethodCode === 'sepa_debit') {
          // Belgium requires BIC for SEPA
          if (!paymentData.bic) {
            errors.push({
              field: 'bic',
              message: 'BIC is required for Belgian SEPA payments',
              code: 'REQUIRED_FIELD'
            });
          }
        }
        break;
        
      case 'DE': // Germany
        if (paymentMethodCode === 'sepa_debit') {
          // Germany has specific IBAN validation
          const iban = paymentData.iban as string;
          if (iban && !iban.startsWith('DE')) {
            errors.push({
              field: 'iban',
              message: 'German customers must use German IBAN',
              code: 'INVALID_COUNTRY_IBAN'
            });
          }
        }
        break;
        
      case 'US': // United States
        if (paymentMethodCode === 'ach') {
          // US requires both routing and account number
          if (!paymentData.routing_number || !paymentData.account_number) {
            errors.push({
              field: 'general',
              message: 'Both routing number and account number are required for ACH',
              code: 'REQUIRED_FIELDS'
            });
          }
        }
        break;
        
      case 'GB': // United Kingdom
        if (paymentMethodCode === 'bacs') {
          // UK requires sort code and account number
          if (!paymentData.sort_code || !paymentData.account_number) {
            errors.push({
              field: 'general',
              message: 'Both sort code and account number are required for BACS',
              code: 'REQUIRED_FIELDS'
            });
          }
        }
        break;
    }
    
    return {
      isValid: errors.length === 0,
      formattedValue,
      errors
    };
  }
}

// Export singleton instance
export const paymentMethodValidationService = new PaymentMethodValidationService();
export default paymentMethodValidationService;
