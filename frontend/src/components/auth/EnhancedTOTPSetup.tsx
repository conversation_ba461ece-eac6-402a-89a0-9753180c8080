/**
 * Enhanced TOTP Setup Component
 * 
 * React component for setting up TOTP authentication with advanced features
 * including QR code display, backup codes, device trust, and step-by-step guidance.
 */

'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { 
  Shield, 
  Smartphone, 
  QrCode, 
  Key, 
  Download, 
  CheckCircle, 
  AlertCircle,
  Copy,
  Eye,
  EyeOff
} from 'lucide-react';
import { toast } from 'sonner';

interface TOTPSetupData {
  factorId: string;
  secret: string;
  qrCodeUri: string;
  qrCodeDataUrl: string;
  backupCodes: string[];
  setupInstructions: string[];
  expiresAt: string;
}

interface EnhancedTOTPSetupProps {
  userId: string;
  userEmail: string;
  onSetupComplete?: (_data: TOTPSetupData) => void;
  onCancel?: () => void;
  className?: string;
}

type SetupStep = 'start' | 'qr-code' | 'verify' | 'backup-codes' | 'complete';

export function EnhancedTOTPSetup({
  userId,
  userEmail,
  onSetupComplete,
  onCancel,
  className = ''
}: EnhancedTOTPSetupProps): React.ReactElement | null {
  const [currentStep, setCurrentStep] = useState<SetupStep>('start');
  const [setupData, setSetupData] = useState<TOTPSetupData | null>(null);
  const [verificationToken, setVerificationToken] = useState('');
  const [deviceName, setDeviceName] = useState('');
  const [trustDevice, setTrustDevice] = useState(false);
  const [showBackupCodes, setShowBackupCodes] = useState(false);
  const [backupCodesSaved, setBackupCodesSaved] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Auto-detect device name
  useEffect(() => {
    const detectDeviceName = () => {
      const userAgent = navigator.userAgent;
      let deviceType = 'Unknown Device';
      
      if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
        deviceType = 'Mobile Device';
      } else if (/Tablet|iPad/.test(userAgent)) {
        deviceType = 'Tablet';
      } else {
        deviceType = 'Desktop Computer';
      }
      
      setDeviceName(deviceType);
    };

    detectDeviceName();
  }, []);

  const startSetup = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/auth/mfa/totp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          userEmail
        }),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        setSetupData(result.data);
        setCurrentStep('qr-code');
        toast.success('TOTP setup initiated');
      } else {
        setError(result.error || 'Failed to start TOTP setup');
        toast.error(result.error || 'Failed to start TOTP setup');
      }
    } catch (error) {
      console.error('Error starting TOTP setup:', error);
      setError('Failed to start TOTP setup');
      toast.error('Failed to start TOTP setup');
    } finally {
      setLoading(false);
    }
  };

  const verifySetup = async () => {
    if (!setupData || !verificationToken) {
      setError('Please enter the verification code');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/auth/mfa/totp/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          factorId: setupData.factorId,
          token: verificationToken,
          deviceName: deviceName || 'Unknown Device',
          trustDevice,
          verificationType: 'setup'
        }),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        setCurrentStep('backup-codes');
        toast.success('TOTP setup verified successfully');
      } else {
        setError(result.error || 'Invalid verification code');
        toast.error(result.error || 'Invalid verification code');
      }
    } catch (error) {
      console.error('Error verifying TOTP setup:', error);
      setError('Failed to verify TOTP setup');
      toast.error('Failed to verify TOTP setup');
    } finally {
      setLoading(false);
    }
  };

  const completeSetup = () => {
    if (!backupCodesSaved) {
      setError('Please save your backup codes before completing setup');
      return;
    }

    setCurrentStep('complete');
    if (onSetupComplete && setupData) {
      onSetupComplete(setupData);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Copied to clipboard');
    } catch (_error) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const downloadBackupCodes = () => {
    if (!setupData) return;

    const content = [
      'PI Lawyer AI - TOTP Backup Codes',
      `Generated: ${new Date().toLocaleString()}`,
      `Email: ${userEmail}`,
      '',
      'IMPORTANT: Store these codes in a secure location.',
      'Each code can only be used once.',
      '',
      ...setupData.backupCodes.map((code, index) => `${index + 1}. ${code}`)
    ].join('\n');

    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `pi-lawyer-ai-backup-codes-${Date.now()}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    setBackupCodesSaved(true);
    toast.success('Backup codes downloaded');
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 'start':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <Shield className="h-16 w-16 text-blue-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Enable Two-Factor Authentication</h3>
              <p className="text-gray-600">
                Secure your account with TOTP (Time-based One-Time Password) authentication
              </p>
            </div>

            <Alert>
              <Smartphone className="h-4 w-4" />
              <AlertDescription>
                You&apos;ll need an authenticator app like Google Authenticator, Authy, or 1Password to continue.
              </AlertDescription>
            </Alert>

            <div className="space-y-3">
              <h4 className="font-medium">What you&apos;ll need:</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  An authenticator app installed on your device
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  About 2-3 minutes to complete setup
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  A secure place to store backup codes
                </li>
              </ul>
            </div>

            <Button onClick={startSetup} disabled={loading} className="w-full">
              {loading ? 'Starting Setup...' : 'Start TOTP Setup'}
            </Button>
          </div>
        );

      case 'qr-code':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <QrCode className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Scan QR Code</h3>
              <p className="text-gray-600">
                Use your authenticator app to scan this QR code
              </p>
            </div>

            {setupData && (
              <div className="space-y-4">
                <div className="flex justify-center">
                  <Image 
                    src={setupData.qrCodeDataUrl} 
                    alt="TOTP QR Code" 
                    width={200}
                    height={200}
                    className="border rounded-lg"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Or enter this secret key manually:</Label>
                  <div className="flex gap-2">
                    <Input 
                      value={setupData.secret} 
                      readOnly 
                      className="font-mono text-sm"
                    />
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => copyToClipboard(setupData.secret)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    After scanning, your app will generate 6-digit codes that change every 30 seconds.
                  </AlertDescription>
                </Alert>
              </div>
            )}

            <Button onClick={() => setCurrentStep('verify')} className="w-full">
              I&apos;ve Added the Account
            </Button>
          </div>
        );

      case 'verify':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <Key className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Verify Setup</h3>
              <p className="text-gray-600">
                Enter the 6-digit code from your authenticator app
              </p>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="verification-token">Verification Code</Label>
                <Input
                  id="verification-token"
                  type="text"
                  inputMode="numeric"
                  pattern="[0-9]*"
                  maxLength={6}
                  value={verificationToken}
                  onChange={(e) => setVerificationToken(e.target.value.replace(/\D/g, ''))}
                  placeholder="000000"
                  className="text-center text-lg font-mono"
                />
              </div>

              <div className="space-y-3">
                <div className="space-y-2">
                  <Label htmlFor="device-name">Device Name (Optional)</Label>
                  <Input
                    id="device-name"
                    value={deviceName}
                    onChange={(e) => setDeviceName(e.target.value)}
                    placeholder="My Device"
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="trust-device"
                    checked={trustDevice}
                    onCheckedChange={(checked) => setTrustDevice(checked as boolean)}
                  />
                  <Label htmlFor="trust-device" className="text-sm">
                    Trust this device for 30 days
                  </Label>
                </div>
              </div>

              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
            </div>

            <div className="flex gap-3">
              <Button 
                variant="outline" 
                onClick={() => setCurrentStep('qr-code')}
                className="flex-1"
              >
                Back
              </Button>
              <Button 
                onClick={verifySetup} 
                disabled={loading || verificationToken.length !== 6}
                className="flex-1"
              >
                {loading ? 'Verifying...' : 'Verify'}
              </Button>
            </div>
          </div>
        );

      case 'backup-codes':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <Download className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Save Backup Codes</h3>
              <p className="text-gray-600">
                Store these codes securely. You can use them if you lose access to your authenticator app.
              </p>
            </div>

            {setupData && (
              <div className="space-y-4">
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Each backup code can only be used once. Store them in a secure location.
                  </AlertDescription>
                </Alert>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label>Backup Codes</Label>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowBackupCodes(!showBackupCodes)}
                    >
                      {showBackupCodes ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      {showBackupCodes ? 'Hide' : 'Show'}
                    </Button>
                  </div>

                  {showBackupCodes && (
                    <div className="grid grid-cols-2 gap-2 p-4 bg-gray-50 rounded-lg">
                      {setupData.backupCodes.map((code, index) => (
                        <div key={index} className="font-mono text-sm p-2 bg-white rounded border">
                          {index + 1}. {code}
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                <Button onClick={downloadBackupCodes} variant="outline" className="w-full">
                  <Download className="h-4 w-4 mr-2" />
                  Download Backup Codes
                </Button>

                {backupCodesSaved && (
                  <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                      Backup codes have been saved. You can now complete the setup.
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            )}

            <Button 
              onClick={completeSetup} 
              disabled={!backupCodesSaved}
              className="w-full"
            >
              Complete Setup
            </Button>
          </div>
        );

      case 'complete':
        return (
          <div className="space-y-6 text-center">
            <CheckCircle className="h-16 w-16 text-green-600 mx-auto" />
            <div>
              <h3 className="text-lg font-semibold mb-2">TOTP Setup Complete!</h3>
              <p className="text-gray-600">
                Two-factor authentication is now enabled for your account.
              </p>
            </div>

            <div className="space-y-3">
              <Badge variant="outline" className="text-green-600 border-green-600">
                ✓ TOTP Enabled
              </Badge>
              {trustDevice && (
                <Badge variant="outline" className="text-blue-600 border-blue-600">
                  ✓ Device Trusted
                </Badge>
              )}
              <Badge variant="outline" className="text-purple-600 border-purple-600">
                ✓ Backup Codes Saved
              </Badge>
            </div>

            <Alert>
              <Shield className="h-4 w-4" />
              <AlertDescription>
                Your account is now protected with two-factor authentication. 
                You&apos;ll need your authenticator app to sign in.
              </AlertDescription>
            </Alert>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          TOTP Authentication Setup
        </CardTitle>
      </CardHeader>
      <CardContent>
        {renderStepContent()}
        
        {currentStep !== 'complete' && onCancel && (
          <>
            <Separator className="my-6" />
            <Button variant="ghost" onClick={onCancel} className="w-full">
              Cancel Setup
            </Button>
          </>
        )}
      </CardContent>
    </Card>
  );
}

export default EnhancedTOTPSetup;
