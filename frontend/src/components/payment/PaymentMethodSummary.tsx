/**
 * PaymentMethodSummary Component
 * 
 * Displays a summary of selected payment method with processing details,
 * fees, and estimated completion time.
 */

'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
  CreditCard,
  Building2,
  Clock,
  DollarSign,
  CheckCircle,
  AlertCircle,
  Info,
  Edit,
  Trash2
} from 'lucide-react';
import type {
  PaymentMethodAvailability,
  SupportedRegion,
  SupportedCurrency,
  PaymentMethodCode
} from '@/lib/types/payment-methods';

interface PaymentMethodSummaryProps {
  paymentMethod: PaymentMethodAvailability;
  amountCents: number;
  currency: SupportedCurrency;
  region: SupportedRegion;
  showActions?: boolean;
  onEdit?: () => void;
  onRemove?: () => void;
  onSelect?: () => void;
  className?: string;
}

const PAYMENT_METHOD_ICONS: Record<PaymentMethodCode, React.ComponentType<{ className?: string }>> = {
  card: CreditCard,
  ach: Building2,
  sepa_debit: Building2,
  bacs: Building2,
  pad: Building2,
  bancontact: CreditCard,
  apple_pay: CreditCard,
  google_pay: CreditCard,
  ideal: Building2,
  sofort: Building2,
  giropay: Building2,
  eps: Building2,
  p24: Building2,
  alipay: CreditCard,
  wechat_pay: CreditCard
};

export function PaymentMethodSummary({
  paymentMethod,
  amountCents,
  currency,
  region,
  showActions = false,
  onEdit,
  onRemove,
  onSelect,
  className = ''
}: PaymentMethodSummaryProps): React.ReactElement {
  const IconComponent = PAYMENT_METHOD_ICONS[paymentMethod.payment_method_type.code] || CreditCard;

  // Calculate processing fees
  const calculateFees = () => {
    const percentageFee = Math.round((amountCents * paymentMethod.regional_config.processing_fee_percentage) / 100);
    const fixedFee = paymentMethod.regional_config.processing_fee_fixed_cents || 0;
    const totalFee = percentageFee + fixedFee;

    return {
      percentageFee,
      fixedFee,
      totalFee,
      netAmount: amountCents - totalFee
    };
  };

  const fees = calculateFees();

  // Format currency amounts
  const formatAmount = (cents: number) => {
    const amount = cents / 100;
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(amount);
  };

  // Get status badge
  const getStatusBadge = () => {
    if (!paymentMethod.regional_config.is_available) {
      return <Badge variant="destructive">Unavailable</Badge>;
    }

    if (paymentMethod.regional_config.is_primary) {
      return <Badge variant="default">Recommended</Badge>;
    }

    return <Badge variant="secondary">Available</Badge>;
  };

  // Get completion time display
  const getCompletionTime = () => {
    const time = paymentMethod.estimated_processing_time || 'Unknown';
    const isInstant = time.toLowerCase().includes('instant');
    
    return (
      <div className="flex items-center gap-2">
        <Clock className="h-4 w-4 text-gray-500" />
        <span className={`text-sm ${isInstant ? 'text-green-600' : 'text-gray-600'}`}>
          {time}
        </span>
      </div>
    );
  };

  // Check if amount is within limits
  const isAmountValid = () => {
    if (paymentMethod.regional_config.min_amount_cents && amountCents < paymentMethod.regional_config.min_amount_cents) {
      return false;
    }
    if (paymentMethod.regional_config.max_amount_cents && amountCents > paymentMethod.regional_config.max_amount_cents) {
      return false;
    }
    return true;
  };

  const amountValid = isAmountValid();

  return (
    <Card className={`${className} ${!amountValid ? 'border-red-200 bg-red-50' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <IconComponent className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <CardTitle className="text-lg">{paymentMethod.payment_method_type.name}</CardTitle>
              <p className="text-sm text-gray-600 mt-1">
                {paymentMethod.payment_method_type.description}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {getStatusBadge()}
            <Badge variant="outline">{region}</Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Amount Validation */}
        {!amountValid && (
          <div className="flex items-center gap-2 p-3 bg-red-100 border border-red-200 rounded-lg">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <div className="text-sm text-red-700">
              {amountCents < (paymentMethod.regional_config.min_amount_cents || 0) && (
                <span>Minimum amount: {formatAmount(paymentMethod.regional_config.min_amount_cents || 0)}</span>
              )}
              {amountCents > (paymentMethod.regional_config.max_amount_cents || Infinity) && (
                <span>Maximum amount: {formatAmount(paymentMethod.regional_config.max_amount_cents || 0)}</span>
              )}
            </div>
          </div>
        )}

        {/* Processing Details */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Transaction Amount</span>
            <span className="text-sm">{formatAmount(amountCents)}</span>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Processing Fee</span>
            <div className="text-right">
              <div className="text-sm">{formatAmount(fees.totalFee)}</div>
              <div className="text-xs text-gray-500">
                {paymentMethod.regional_config.processing_fee_percentage}%
                {fees.fixedFee > 0 && ` + ${formatAmount(fees.fixedFee)}`}
              </div>
            </div>
          </div>

          <Separator />

          <div className="flex items-center justify-between font-medium">
            <span>Net Amount</span>
            <span>{formatAmount(fees.netAmount)}</span>
          </div>
        </div>

        {/* Processing Time */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Processing Time</span>
          {getCompletionTime()}
        </div>

        {/* Additional Information */}
        {paymentMethod.payment_method_type.requires_bank_details && (
          <div className="flex items-center gap-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <Info className="h-4 w-4 text-blue-600" />
            <span className="text-sm text-blue-700">
              Bank account details required for this payment method
            </span>
          </div>
        )}

        {paymentMethod.payment_method_type.supports_recurring && (
          <div className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <span className="text-sm text-green-700">
              Supports recurring payments
            </span>
          </div>
        )}

        {/* Actions */}
        {showActions && (
          <div className="flex items-center gap-2 pt-2">
            {onSelect && amountValid && (
              <Button 
                onClick={onSelect}
                className="flex-1"
                disabled={!paymentMethod.regional_config.is_available}
              >
                Select Payment Method
              </Button>
            )}
            
            {onEdit && (
              <Button
                variant="outline"
                size="sm"
                onClick={onEdit}
              >
                <Edit className="h-4 w-4" />
              </Button>
            )}
            
            {onRemove && (
              <Button
                variant="outline"
                size="sm"
                onClick={onRemove}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
          </div>
        )}

        {/* Validation Rules Info */}
        {paymentMethod.validation_rules && paymentMethod.validation_rules.length > 0 && (
          <div className="pt-2">
            <details className="text-sm">
              <summary className="cursor-pointer text-gray-600 hover:text-gray-800">
                Validation Requirements
              </summary>
              <div className="mt-2 space-y-1">
                {paymentMethod.validation_rules.map((rule, index) => (
                  <div key={index} className="text-xs text-gray-500">
                    • {rule.field_name}: {rule.error_message || 'Required field'}
                  </div>
                ))}
              </div>
            </details>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default PaymentMethodSummary;
