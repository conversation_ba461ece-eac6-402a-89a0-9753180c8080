/**
 * SEPA Direct Debit Form Component
 * 
 * React component for collecting SEPA Direct Debit payment information
 * with IBAN validation, mandate acceptance, and compliance features.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { 
  Building2, 
  CreditCard, 
  Shield, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Info,
  FileText
} from 'lucide-react';
import type { SupportedRegion, SupportedCurrency } from '@/lib/types/payment-methods';

interface SEPAFormData {
  iban: string;
  bic: string;
  accountHolderName: string;
  customerEmail: string;
  customerName: string;
  mandateAccepted: boolean;
  privacyAccepted: boolean;
}

interface SEPADirectDebitFormProps {
  countryCode: SupportedRegion;
  currencyCode: SupportedCurrency;
  tenantId: string;
  customerEmail?: string;
  customerName?: string;
  onSubmit: (_data: SEPAFormData) => Promise<void>;
  onCancel?: () => void;
  loading?: boolean;
  className?: string;
}

export function SEPADirectDebitForm({
  countryCode,
  currencyCode,
  tenantId,
  customerEmail = '',
  customerName = '',
  onSubmit,
  onCancel,
  loading = false,
  className = ''
}: SEPADirectDebitFormProps): React.ReactElement {
  const [formData, setFormData] = useState<SEPAFormData>({
    iban: '',
    bic: '',
    accountHolderName: '',
    customerEmail: customerEmail,
    customerName: customerName,
    mandateAccepted: false,
    privacyAccepted: false
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [ibanValid, setIbanValid] = useState<boolean | null>(null);

  // Update form data when props change
  useEffect(() => {
    setFormData(prev => ({
      ...prev,
      customerEmail: customerEmail,
      customerName: customerName
    }));
  }, [customerEmail, customerName]);

  // Validate IBAN in real-time
  useEffect(() => {
    if (formData.iban.length > 0) {
      const isValid = validateIBAN(formData.iban);
      setIbanValid(isValid);
      
      if (!isValid && formData.iban.length >= 15) {
        setErrors(prev => ({ ...prev, iban: 'Invalid IBAN format or checksum' }));
      } else {
        setErrors(prev => {
          const { iban, ...rest } = prev;
          return rest;
        });
      }
    } else {
      setIbanValid(null);
    }
  }, [formData.iban]);

  const validateIBAN = (iban: string): boolean => {
    // Remove spaces and convert to uppercase
    const cleanIban = iban.replace(/\s/g, '').toUpperCase();

    // Check length (15-34 characters)
    if (cleanIban.length < 15 || cleanIban.length > 34) {
      return false;
    }

    // Check format (2 letters + 2 digits + alphanumeric)
    if (!/^[A-Z]{2}[0-9]{2}[A-Z0-9]+$/.test(cleanIban)) {
      return false;
    }

    // MOD-97 checksum validation
    const rearranged = cleanIban.slice(4) + cleanIban.slice(0, 4);
    const numericString = rearranged.replace(/[A-Z]/g, (char) => 
      (char.charCodeAt(0) - 55).toString()
    );

    let remainder = 0;
    for (let i = 0; i < numericString.length; i++) {
      remainder = (remainder * 10 + parseInt(numericString[i])) % 97;
    }

    return remainder === 1;
  };

  const formatIBAN = (value: string): string => {
    // Remove all non-alphanumeric characters and convert to uppercase
    const clean = value.replace(/[^A-Z0-9]/gi, '').toUpperCase();
    
    // Add spaces every 4 characters
    return clean.replace(/(.{4})/g, '$1 ').trim();
  };

  const handleInputChange = (field: keyof SEPAFormData, value: string | boolean): void => {
    if (field === 'iban' && typeof value === 'string') {
      value = formatIBAN(value);
    }

    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => {
        const { [field]: removed, ...rest } = prev;
        return rest;
      });
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.iban) {
      newErrors.iban = 'IBAN is required';
    } else if (!validateIBAN(formData.iban)) {
      newErrors.iban = 'Invalid IBAN format or checksum';
    }

    if (!formData.accountHolderName.trim()) {
      newErrors.accountHolderName = 'Account holder name is required';
    }

    if (!formData.customerEmail.trim()) {
      newErrors.customerEmail = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.customerEmail)) {
      newErrors.customerEmail = 'Invalid email format';
    }

    if (!formData.customerName.trim()) {
      newErrors.customerName = 'Customer name is required';
    }

    if (!formData.mandateAccepted) {
      newErrors.mandateAccepted = 'You must accept the SEPA mandate';
    }

    if (!formData.privacyAccepted) {
      newErrors.privacyAccepted = 'You must accept the privacy policy';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('SEPA form submission error:', error);
    }
  };

  const getCountryName = (code: SupportedRegion): string => {
    const countries: Record<SupportedRegion, string> = {
      'BE': 'Belgium',
      'DE': 'Germany',
      'FR': 'France',
      'NL': 'Netherlands',
      'IT': 'Italy',
      'ES': 'Spain',
      'AT': 'Austria',
      'EU': 'European Union',
      'US': 'United States',
      'GB': 'United Kingdom',
      'CA': 'Canada'
    };
    return countries[code] || code;
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Building2 className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <CardTitle className="text-lg">SEPA Direct Debit</CardTitle>
            <p className="text-sm text-gray-600 mt-1">
              Secure bank transfer for {getCountryName(countryCode)}
            </p>
          </div>
          <Badge variant="outline" className="ml-auto">
            {currencyCode}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* SEPA Information */}
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            SEPA Direct Debit allows secure payments directly from your bank account. 
            Processing typically takes 1-3 business days.
          </AlertDescription>
        </Alert>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* IBAN Input */}
          <div className="space-y-2">
            <Label htmlFor="iban">
              IBAN <span className="text-red-500">*</span>
            </Label>
            <div className="relative">
              <Input
                id="iban"
                type="text"
                placeholder="DE89 3704 0044 0532 0130 00"
                value={formData.iban}
                onChange={(e) => handleInputChange('iban', e.target.value)}
                className={`font-mono ${errors.iban ? 'border-red-500' : ''} ${
                  ibanValid === true ? 'border-green-500' : ''
                }`}
                maxLength={34}
              />
              {ibanValid === true && (
                <CheckCircle className="absolute right-3 top-3 h-4 w-4 text-green-500" />
              )}
              {ibanValid === false && formData.iban.length >= 15 && (
                <AlertCircle className="absolute right-3 top-3 h-4 w-4 text-red-500" />
              )}
            </div>
            {errors.iban && (
              <p className="text-sm text-red-500">{errors.iban}</p>
            )}
          </div>

          {/* BIC Input (Optional) */}
          <div className="space-y-2">
            <Label htmlFor="bic">
              BIC/SWIFT Code <span className="text-gray-500">(Optional)</span>
            </Label>
            <Input
              id="bic"
              type="text"
              placeholder="DEUTDEFF"
              value={formData.bic}
              onChange={(e) => handleInputChange('bic', e.target.value.toUpperCase())}
              className="font-mono"
              maxLength={11}
            />
            <p className="text-xs text-gray-500">
              BIC is optional for SEPA countries but may be required by some banks
            </p>
          </div>

          {/* Account Holder Name */}
          <div className="space-y-2">
            <Label htmlFor="accountHolderName">
              Account Holder Name <span className="text-red-500">*</span>
            </Label>
            <Input
              id="accountHolderName"
              type="text"
              placeholder="John Doe"
              value={formData.accountHolderName}
              onChange={(e) => handleInputChange('accountHolderName', e.target.value)}
              className={errors.accountHolderName ? 'border-red-500' : ''}
            />
            {errors.accountHolderName && (
              <p className="text-sm text-red-500">{errors.accountHolderName}</p>
            )}
          </div>

          {/* Customer Email */}
          <div className="space-y-2">
            <Label htmlFor="customerEmail">
              Email Address <span className="text-red-500">*</span>
            </Label>
            <Input
              id="customerEmail"
              type="email"
              placeholder="<EMAIL>"
              value={formData.customerEmail}
              onChange={(e) => handleInputChange('customerEmail', e.target.value)}
              className={errors.customerEmail ? 'border-red-500' : ''}
            />
            {errors.customerEmail && (
              <p className="text-sm text-red-500">{errors.customerEmail}</p>
            )}
          </div>

          {/* Customer Name */}
          <div className="space-y-2">
            <Label htmlFor="customerName">
              Customer Name <span className="text-red-500">*</span>
            </Label>
            <Input
              id="customerName"
              type="text"
              placeholder="John Doe"
              value={formData.customerName}
              onChange={(e) => handleInputChange('customerName', e.target.value)}
              className={errors.customerName ? 'border-red-500' : ''}
            />
            {errors.customerName && (
              <p className="text-sm text-red-500">{errors.customerName}</p>
            )}
          </div>

          <Separator />

          {/* Processing Information */}
          <div className="space-y-3">
            <h4 className="font-medium flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Processing Information
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <p className="font-medium">Processing Time</p>
                <p className="text-gray-600">1-3 business days</p>
              </div>
              <div>
                <p className="font-medium">Processing Fee</p>
                <p className="text-gray-600">0.35% (no fixed fee)</p>
              </div>
            </div>
          </div>

          <Separator />

          {/* Mandate Acceptance */}
          <div className="space-y-4">
            <h4 className="font-medium flex items-center gap-2">
              <FileText className="h-4 w-4" />
              SEPA Mandate Agreement
            </h4>
            
            <div className="space-y-3">
              <div className="flex items-start space-x-2">
                <Checkbox
                  id="mandateAccepted"
                  checked={formData.mandateAccepted}
                  onCheckedChange={(checked) => handleInputChange('mandateAccepted', checked as boolean)}
                />
                <div className="grid gap-1.5 leading-none">
                  <Label
                    htmlFor="mandateAccepted"
                    className="text-sm font-normal cursor-pointer"
                  >
                    I authorize PI Lawyer AI to collect payments from my bank account via SEPA Direct Debit
                  </Label>
                  <p className="text-xs text-gray-500">
                    You can cancel this authorization at any time by contacting your bank or us
                  </p>
                </div>
              </div>
              {errors.mandateAccepted && (
                <p className="text-sm text-red-500">{errors.mandateAccepted}</p>
              )}

              <div className="flex items-start space-x-2">
                <Checkbox
                  id="privacyAccepted"
                  checked={formData.privacyAccepted}
                  onCheckedChange={(checked) => handleInputChange('privacyAccepted', checked as boolean)}
                />
                <div className="grid gap-1.5 leading-none">
                  <Label
                    htmlFor="privacyAccepted"
                    className="text-sm font-normal cursor-pointer"
                  >
                    I accept the privacy policy and terms of service
                  </Label>
                </div>
              </div>
              {errors.privacyAccepted && (
                <p className="text-sm text-red-500">{errors.privacyAccepted}</p>
              )}
            </div>
          </div>

          {/* Security Notice */}
          <Alert>
            <Shield className="h-4 w-4" />
            <AlertDescription>
              Your banking information is encrypted and processed securely through Stripe. 
              We never store your complete IBAN or banking details.
            </AlertDescription>
          </Alert>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              type="submit"
              disabled={loading || !formData.mandateAccepted || !formData.privacyAccepted}
              className="flex-1"
            >
              {loading ? 'Creating Mandate...' : 'Create SEPA Mandate'}
            </Button>
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={loading}
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
}

export default SEPADirectDebitForm;
