/**
 * Payment Authentication Tests
 * 
 * Tests for the payment API authentication and authorization system
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { NextRequest } from 'next/server';

// Mock environment variables before any imports using hoisted
vi.hoisted(() => {
  process.env.PAYMENT_API_KEY = 'test-payment-api-key';
  process.env.SUPABASE_JWT_SECRET = 'test-jwt-secret';
  process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co';
  process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-service-key';
});

import { withPaymentAuth } from '@/lib/auth/payment-auth';
import { verify } from 'jsonwebtoken';
import { createClient } from '@supabase/supabase-js';
import { rateLimit } from '@/lib/middlewares/rate-limit';

// Mock modules
vi.mock('jsonwebtoken', () => ({
  verify: vi.fn()
}));

vi.mock('@supabase/supabase-js', () => ({
  createClient: vi.fn()
}));

vi.mock('@/lib/middlewares/rate-limit', () => ({
  rateLimit: vi.fn()
}));

describe('Payment Authentication', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock rate limiting to allow requests
    (rateLimit as any).mockResolvedValue({
      success: true,
      current: 1,
      limit: 30,
      retryAfter: 0
    });

    // Mock Supabase client
    (createClient as any).mockReturnValue({
      from: vi.fn().mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({ data: null, _error: null })
          })
        })
      })
    });
  });

  describe('JWT Authentication', () => {
    it('should reject requests without Authorization header', async () => {
      const handler = withPaymentAuth(async () => new Response('OK'));
      
      const request = new NextRequest('http://localhost/api/test');
      const response = await handler(request);
      
      expect(response.status).toBe(401);
      const body = await response.json();
      expect(body.code).toBe('MISSING_AUTH_HEADER');
    });

    it('should reject requests with invalid JWT token', async () => {
      (verify as any).mockImplementation(() => {
        throw new Error('Invalid token');
      });
      
      const handler = withPaymentAuth(async () => new Response('OK'));
      
      const request = new NextRequest('http://localhost/api/test', {
        headers: {
          'Authorization': 'Bearer invalid-token'
        }
      });
      
      const response = await handler(request);
      
      expect(response.status).toBe(401);
      const body = await response.json();
      expect(body.code).toBe('AUTH_FAILED');
    });

    it('should accept requests with valid JWT token', async () => {
      (verify as any).mockReturnValue({
        sub: 'user-123',
        email: '<EMAIL>',
        role: 'attorney',
        tenant_id: 'tenant-123'
      });
      
      const handler = withPaymentAuth(async (req, { user }) => {
        return new Response(JSON.stringify({ userId: user.id }));
      });
      
      const request = new NextRequest('http://localhost/api/test', {
        headers: {
          'Authorization': 'Bearer valid-token'
        }
      });
      
      const response = await handler(request);
      
      expect(response.status).toBe(200);
      const body = await response.json();
      expect(body.userId).toBe('user-123');
    });
  });

  describe('API Key Validation', () => {
    beforeEach(() => {
      (verify as any).mockReturnValue({
        sub: 'user-123',
        email: '<EMAIL>',
        role: 'attorney',
        tenant_id: 'tenant-123'
      });
    });

    it('should reject requests without API key when required', async () => {
      const handler = withPaymentAuth(
        async () => new Response('OK'),
        { requireAPIKey: true }
      );

      const request = new NextRequest('http://localhost/api/test', {
        headers: {
          'Authorization': 'Bearer valid-token'
        }
      });

      const response = await handler(request);

      expect(response.status).toBe(401);
      const body = await response.json();
      expect(body.code).toBe('INVALID_API_KEY');
    });

    it('should accept requests with valid API key', async () => {
      const handler = withPaymentAuth(
        async () => new Response('OK'),
        { requireAPIKey: true }
      );
      
      const request = new NextRequest('http://localhost/api/test', {
        headers: {
          'Authorization': 'Bearer valid-token',
          'X-API-Key': 'test-payment-api-key'
        }
      });
      
      const response = await handler(request);
      
      expect(response.status).toBe(200);
    });
  });

  describe('Role-Based Authorization', () => {
    beforeEach(() => {
      (verify as any).mockReturnValue({
        sub: 'user-123',
        email: '<EMAIL>',
        role: 'client',
        tenant_id: 'tenant-123'
      });
    });

    it('should reject requests from unauthorized roles', async () => {
      const handler = withPaymentAuth(
        async () => new Response('OK'),
        { allowedRoles: ['attorney', 'partner'] }
      );
      
      const request = new NextRequest('http://localhost/api/test', {
        headers: {
          'Authorization': 'Bearer valid-token'
        }
      });
      
      const response = await handler(request);
      
      expect(response.status).toBe(403);
      const body = await response.json();
      expect(body.code).toBe('INSUFFICIENT_PERMISSIONS');
    });

    it('should accept requests from authorized roles', async () => {
      (verify as any).mockReturnValue({
        sub: 'user-123',
        email: '<EMAIL>',
        role: 'attorney',
        tenant_id: 'tenant-123'
      });
      
      const handler = withPaymentAuth(
        async () => new Response('OK'),
        { allowedRoles: ['attorney', 'partner'] }
      );
      
      const request = new NextRequest('http://localhost/api/test', {
        headers: {
          'Authorization': 'Bearer valid-token'
        }
      });
      
      const response = await handler(request);
      
      expect(response.status).toBe(200);
    });
  });

  describe('Rate Limiting', () => {
    beforeEach(() => {
      (verify as any).mockReturnValue({
        sub: 'user-123',
        email: '<EMAIL>',
        role: 'attorney',
        tenant_id: 'tenant-123'
      });
    });

    it('should reject requests when rate limit exceeded', async () => {
      (rateLimit as any).mockResolvedValue({
        success: false,
        current: 31,
        limit: 30,
        retryAfter: 60
      });
      
      const handler = withPaymentAuth(async () => new Response('OK'));
      
      const request = new NextRequest('http://localhost/api/test', {
        headers: {
          'Authorization': 'Bearer valid-token'
        }
      });
      
      const response = await handler(request);
      
      expect(response.status).toBe(429);
      const body = await response.json();
      expect(body.code).toBe('RATE_LIMIT_EXCEEDED');
    });

    it('should include rate limit headers in response', async () => {
      const handler = withPaymentAuth(async () => new Response('OK'));
      
      const request = new NextRequest('http://localhost/api/test', {
        headers: {
          'Authorization': 'Bearer valid-token'
        }
      });
      
      const response = await handler(request);
      
      expect(response.headers.get('X-RateLimit-Remaining')).toBeDefined();
      expect(response.headers.get('X-RateLimit-Reset')).toBeDefined();
    });
  });
});
