/**
 * E2E 3D Secure Authentication Tests
 * 
 * Comprehensive tests for 3D Secure payment flows including authentication,
 * failures, timeouts, and various card issuer scenarios.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { 
  createMockStripe, 
  MOCK_PAYMENT_METHODS,
  MOCK_PAYMENT_INTENTS,
  MOCK_3DS_RESPONSES,
  simulate3DSecureFlow
} from '../../setup/stripe-mocks';

// Mock environment setup
vi.hoisted(() => {
  process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co';
  process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-service-key';
  process.env.STRIPE_SECRET_KEY = 'sk_test_123';
  process.env.PAYMENT_ENCRYPTION_KEY = 'a'.repeat(64);
  process.env.NEXT_PUBLIC_APP_URL = 'https://test.example.com';
  process.env.NODE_ENV = 'test';
  process.env.SENTRY_DSN = '';
});

// Mock Sentry before any imports
vi.mock('@sentry/nextjs', () => ({
  withSentryConfig: vi.fn((config) => config),
  captureException: vi.fn(),
  captureMessage: vi.fn(),
  withSentry: vi.fn((handler) => handler),
}));

import { StripePaymentMethodService } from '@/lib/services/stripe-payment-method-service';
import type { Stripe } from 'stripe';

// Mock Stripe
vi.mock('stripe', () => ({
  default: vi.fn(() => createMockStripe()),
  Stripe: {
    errors: {
      StripeError: class StripeError extends Error {
        constructor(message: string, public code?: string, public type?: string) {
          super(message);
          this.name = 'StripeError';
        }
      }
    }
  }
}));

describe('E2E 3D Secure Authentication Tests', () => {
  let paymentService: StripePaymentMethodService;
  let mockStripe: ReturnType<typeof createMockStripe>;

  beforeEach(() => {
    vi.clearAllMocks();
    mockStripe = createMockStripe();
    paymentService = new StripePaymentMethodService();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('3D Secure Authentication Flow', () => {
    it('should handle successful 3D Secure authentication', async () => {
      // Create payment method that requires 3DS
      const paymentMethod = await mockStripe.paymentMethods.create({
        type: 'card',
        card: {
          number: '****************', // 3DS required card
          exp_month: 12,
          exp_year: 2025,
          cvc: '123'
        }
      });

      expect(paymentMethod.id).toBe(MOCK_PAYMENT_METHODS.VALID_CARD_3DS);
      expect(paymentMethod.card?.three_d_secure_usage?.supported).toBe(true);

      // Create payment intent that requires 3DS
      const paymentIntent = await mockStripe.paymentIntents.create({
        amount: 5000,
        currency: 'usd',
        payment_method: paymentMethod.id,
        confirm: true,
        return_url: 'https://test.example.com/return'
      });

      expect(paymentIntent.status).toBe('requires_action');
      expect(paymentIntent.next_action?.type).toBe('redirect_to_url');
      expect(paymentIntent.next_action?.redirect_to_url?.url).toContain('stripe.com');

      // Simulate successful 3DS authentication
      const authenticatedIntent = await simulate3DSecureFlow(paymentIntent.id, true);

      expect(authenticatedIntent.status).toBe('succeeded');
      expect(authenticatedIntent.outcome.network_status).toBe('approved_by_network');
      expect(authenticatedIntent.outcome.risk_level).toBe('normal');
    });

    it('should handle failed 3D Secure authentication', async () => {
      // Create payment intent requiring 3DS
      const paymentIntent = await mockStripe.paymentIntents.create({
        amount: 10000,
        currency: 'eur',
        payment_method: MOCK_PAYMENT_METHODS.VALID_CARD_3DS,
        confirm: true,
        return_url: 'https://test.example.com/return'
      });

      expect(paymentIntent.status).toBe('requires_action');

      // Simulate failed 3DS authentication
      const failedIntent = await simulate3DSecureFlow(paymentIntent.id, false);

      expect(failedIntent.status).toBe('requires_payment_method');
      expect(failedIntent.last_payment_error?.code).toBe('authentication_failed');
      expect(failedIntent.last_payment_error?.message).toContain('3D Secure authentication failed');
    });

    it('should handle 3D Secure challenge flow', async () => {
      // Create payment with 3DS challenge required
      const paymentIntent = await mockStripe.paymentIntents.create({
        amount: 15000,
        currency: 'gbp',
        payment_method: MOCK_PAYMENT_METHODS.VALID_CARD_3DS,
        confirm: true,
        return_url: 'https://test.example.com/return',
        metadata: {
          tenant_id: 'tenant-123',
          order_id: 'order-456'
        }
      });

      // Verify challenge is required
      expect(paymentIntent.status).toBe('requires_action');
      expect(paymentIntent.next_action).toEqual(MOCK_3DS_RESPONSES.REQUIRES_CHALLENGE.next_action);

      // Simulate user completing challenge
      mockStripe.paymentIntents.confirm.mockResolvedValueOnce({
        id: paymentIntent.id,
        status: 'processing'
      });

      const processingIntent = await mockStripe.paymentIntents.confirm(paymentIntent.id);
      expect(processingIntent.status).toBe('processing');

      // Simulate final success after processing
      mockStripe.paymentIntents.retrieve.mockResolvedValueOnce({
        id: paymentIntent.id,
        status: 'succeeded',
        amount: 15000,
        currency: 'gbp'
      });

      const succeededIntent = await mockStripe.paymentIntents.retrieve(paymentIntent.id);
      expect(succeededIntent.status).toBe('succeeded');
    });

    it('should handle 3D Secure timeout scenarios', async () => {
      // Create payment requiring 3DS
      const paymentIntent = await mockStripe.paymentIntents.create({
        amount: 5000,
        currency: 'usd',
        payment_method: MOCK_PAYMENT_METHODS.VALID_CARD_3DS,
        confirm: true,
        return_url: 'https://test.example.com/return'
      });

      expect(paymentIntent.status).toBe('requires_action');

      // Simulate timeout by not completing authentication
      const timeoutPromise = new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            id: paymentIntent.id,
            status: 'requires_payment_method',
            last_payment_error: {
              code: 'authentication_timeout',
              message: '3D Secure authentication timed out'
            }
          });
        }, 5000); // 5 second timeout
      });

      // Use race condition to simulate timeout
      const result = await Promise.race([
        simulate3DSecureFlow(paymentIntent.id, true),
        timeoutPromise
      ]);

      // In real scenario, timeout would win
      expect(result).toBeDefined();
    });

    it('should handle 3D Secure with different card brands', async () => {
      const cardBrands = [
        { number: '****************', brand: 'visa' },
        { number: '****************', brand: 'mastercard' },
        { number: '***************', brand: 'amex' }
      ];

      for (const card of cardBrands) {
        mockStripe.paymentMethods.create.mockResolvedValueOnce({
          id: `pm_${card.brand}_3ds`,
          type: 'card',
          card: {
            brand: card.brand,
            last4: card.number.slice(-4),
            three_d_secure_usage: { supported: true }
          }
        });

        const paymentMethod = await mockStripe.paymentMethods.create({
          type: 'card',
          card: {
            number: card.number,
            exp_month: 12,
            exp_year: 2025,
            cvc: card.brand === 'amex' ? '1234' : '123'
          }
        });

        expect(paymentMethod.card?.brand).toBe(card.brand);
        expect(paymentMethod.card?.three_d_secure_usage?.supported).toBe(true);
      }
    });
  });

  describe('3D Secure Exemptions and Regulations', () => {
    it('should handle low-value exemption for 3D Secure', async () => {
      // Payment under 30 EUR may qualify for low-value exemption
      mockStripe.paymentIntents.create.mockResolvedValueOnce({
        id: 'pi_low_value',
        status: 'succeeded', // No 3DS required
        amount: 2000, // 20 EUR
        currency: 'eur',
        payment_method: MOCK_PAYMENT_METHODS.VALID_CARD
      });

      const paymentIntent = await mockStripe.paymentIntents.create({
        amount: 2000,
        currency: 'eur',
        payment_method: MOCK_PAYMENT_METHODS.VALID_CARD,
        confirm: true,
        payment_method_options: {
          card: {
            request_three_d_secure: 'automatic' // Let Stripe decide
          }
        }
      });

      expect(paymentIntent.status).toBe('succeeded');
      expect(paymentIntent.id).toBe('pi_low_value');
    });

    it('should enforce 3D Secure for high-risk transactions', async () => {
      // High amount or risk score should trigger 3DS
      const paymentIntent = await mockStripe.paymentIntents.create({
        amount: 100000, // 1000 EUR
        currency: 'eur',
        payment_method: MOCK_PAYMENT_METHODS.VALID_CARD_3DS,
        confirm: true,
        payment_method_options: {
          card: {
            request_three_d_secure: 'any' // Always request if available
          }
        }
      });

      expect(paymentIntent.status).toBe('requires_action');
      expect(paymentIntent.next_action?.type).toBe('redirect_to_url');
    });

    it('should handle merchant-initiated transactions without 3D Secure', async () => {
      // MIT (Merchant Initiated Transaction) may not require 3DS
      mockStripe.paymentIntents.create.mockResolvedValueOnce({
        id: 'pi_mit',
        status: 'succeeded',
        amount: 5000,
        currency: 'usd',
        payment_method: MOCK_PAYMENT_METHODS.VALID_CARD,
        setup_future_usage: 'off_session'
      });

      const paymentIntent = await mockStripe.paymentIntents.create({
        amount: 5000,
        currency: 'usd',
        payment_method: MOCK_PAYMENT_METHODS.VALID_CARD,
        confirm: true,
        off_session: true,
        payment_method_options: {
          card: {
            mit_exemption: {
              network_transaction_id: 'prev_network_txn_id'
            }
          }
        }
      });

      expect(paymentIntent.status).toBe('succeeded');
      expect(paymentIntent.setup_future_usage).toBe('off_session');
    });
  });

  describe('3D Secure Error Handling', () => {
    it('should handle network errors during 3D Secure', async () => {
      mockStripe.paymentIntents.create.mockRejectedValueOnce(
        new Error('Network _error: Unable to reach authentication server')
      );

      await expect(
        paymentService.processPayment(
          MOCK_PAYMENT_METHODS.VALID_CARD_3DS,
          5000,
          'EUR',
          'tenant-123'
        )
      ).rejects.toThrow('Network error');
    });

    it('should handle invalid 3D Secure response', async () => {
      // Create payment requiring 3DS
      const paymentIntent = await mockStripe.paymentIntents.create({
        amount: 5000,
        currency: 'eur',
        payment_method: MOCK_PAYMENT_METHODS.VALID_CARD_3DS,
        confirm: true
      });

      // Mock invalid authentication response
      mockStripe.paymentIntents.retrieve.mockResolvedValueOnce({
        id: paymentIntent.id,
        status: 'requires_payment_method',
        last_payment_error: {
          code: 'invalid_authentication_response',
          message: 'The authentication response was invalid or malformed'
        }
      });

      const failedIntent = await mockStripe.paymentIntents.retrieve(paymentIntent.id);
      
      expect(failedIntent.status).toBe('requires_payment_method');
      expect(failedIntent.last_payment_error?.code).toBe('invalid_authentication_response');
    });

    it('should handle card issuer authentication errors', async () => {
      mockStripe.paymentIntents.create.mockResolvedValueOnce({
        id: 'pi_issuer_error',
        status: 'requires_payment_method',
        last_payment_error: {
          code: 'card_declined',
          decline_code: 'authentication_required',
          message: 'Your card issuer requires authentication for this payment'
        }
      });

      const result = await paymentService.processPayment(
        MOCK_PAYMENT_METHODS.VALID_CARD,
        10000,
        'EUR',
        'tenant-123'
      );

      expect(result.status).toBe('requires_payment_method');
    });
  });

  describe('3D Secure with Saved Cards', () => {
    it('should handle 3D Secure for saved payment methods', async () => {
      // Attach payment method to customer
      const attachedMethod = await mockStripe.paymentMethods.attach(
        MOCK_PAYMENT_METHODS.VALID_CARD_3DS,
        { customer: 'cus_test' }
      );

      expect(attachedMethod.id).toBe(MOCK_PAYMENT_METHODS.VALID_CARD);

      // Use saved card for payment requiring 3DS
      mockStripe.paymentIntents.create.mockResolvedValueOnce({
        id: 'pi_saved_card_3ds',
        status: 'requires_action',
        payment_method: MOCK_PAYMENT_METHODS.VALID_CARD_3DS,
        next_action: MOCK_3DS_RESPONSES.REQUIRES_CHALLENGE.next_action
      });

      const paymentIntent = await mockStripe.paymentIntents.create({
        amount: 7500,
        currency: 'usd',
        customer: 'cus_test',
        payment_method: MOCK_PAYMENT_METHODS.VALID_CARD_3DS,
        confirm: true,
        off_session: false // Customer is present for authentication
      });

      expect(paymentIntent.status).toBe('requires_action');
      expect(paymentIntent.next_action).toBeDefined();
    });

    it('should handle CVC re-collection for 3D Secure', async () => {
      // Some cards may require CVC re-entry for 3DS
      mockStripe.paymentIntents.create.mockResolvedValueOnce({
        id: 'pi_cvc_required',
        status: 'requires_action',
        next_action: {
          type: 'verify_with_microdeposits',
          verify_with_microdeposits: {
            arrival_date: Math.floor(Date.now() / 1000) + 86400,
            hosted_verification_url: 'https://stripe.com/verify'
          }
        }
      });

      const paymentIntent = await mockStripe.paymentIntents.create({
        amount: 5000,
        currency: 'usd',
        payment_method: MOCK_PAYMENT_METHODS.VALID_CARD,
        payment_method_options: {
          card: {
            cvc: '123' // Re-collected CVC
          }
        },
        confirm: true
      });

      expect(paymentIntent.status).toBe('requires_action');
    });
  });
});