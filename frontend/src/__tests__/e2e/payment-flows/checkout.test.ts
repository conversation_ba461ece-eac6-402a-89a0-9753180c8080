/**
 * E2E Stripe Checkout Flow Tests
 * 
 * Comprehensive tests for checkout session creation, payment completion,
 * cancellation scenarios, and invalid payment method handling.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { 
  createMockStripe, 
  MOCK_PAYMENT_METHODS,
  MOCK_PAYMENT_INTENTS,
  MOCK_CUSTOMERS,
  simulate3DSecureFlow,
  createMockWebhookEvent
} from '../../setup/stripe-mocks';

// Mock environment setup
vi.hoisted(() => {
  process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co';
  process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-service-key';
  process.env.STRIPE_SECRET_KEY = 'sk_test_123';
  process.env.STRIPE_WEBHOOK_SECRET = 'whsec_test_123';
  process.env.PAYMENT_ENCRYPTION_KEY = 'a'.repeat(64);
  process.env.PAYMENT_MASTER_KEY = 'test-master-key-for-field-encryption-minimum-32-chars';
  process.env.NEXT_PUBLIC_APP_URL = 'https://test.example.com';
  process.env.NODE_ENV = 'test';
  process.env.SENTRY_DSN = '';
});

// Mock Sentry before any imports
vi.mock('@sentry/nextjs', () => ({
  withSentryConfig: vi.fn((config) => config),
  captureException: vi.fn(),
  captureMessage: vi.fn(),
  withSentry: vi.fn((handler) => handler),
}));

// Mock payment monitoring
vi.mock('@/lib/monitoring/payment-sentry-integration', () => ({
  paymentSentry: {
    setPaymentUserContext: vi.fn(),
    trackPaymentMethodCreation: vi.fn(),
    trackPaymentProcessing: vi.fn(),
    capturePaymentError: vi.fn(),
  }
}));

vi.mock('@/lib/monitoring/payment-prometheus-metrics', () => ({
  paymentMetrics: {
    recordPaymentMethodCreation: vi.fn(),
    recordPaymentProcessing: vi.fn(),
    recordPaymentError: vi.fn(),
  }
}));

// Mock regional payment method service
vi.mock('@/lib/services/regional-payment-method-service', () => ({
  RegionalPaymentMethodService: vi.fn().mockImplementation(() => ({
    getAvailablePaymentMethods: vi.fn().mockResolvedValue([
      {
        payment_method_type: { code: 'card' },
        regional_config: {
          stripe_configuration: {}
        }
      }
    ]),
    validatePaymentMethod: vi.fn().mockResolvedValue({
      is_valid: true,
      formatted_data: null,
      validation_errors: []
    })
  }))
}));

// Mock encrypted payment storage
vi.mock('@/lib/services/encrypted-payment-storage', () => ({
  EncryptedPaymentStorage: vi.fn().mockImplementation(() => ({
    storePaymentData: vi.fn().mockResolvedValue({
      success: true,
      id: 'encrypted-data-123'
    }),
    retrievePaymentData: vi.fn().mockResolvedValue({
      success: true,
      data: {}
    })
  }))
}));

// Mock payment encryption
vi.mock('@/lib/security/payment-encryption', () => ({
  encryptPaymentFields: vi.fn().mockImplementation((data) => data),
  decryptPaymentFields: vi.fn().mockImplementation((data) => data)
}));

// Mock Stripe
vi.mock('stripe', () => {
  const StripeErrorClass = class StripeError extends Error {
    constructor(message: string, public code?: string, public type?: string) {
      super(message);
      this.name = 'StripeError';
    }
  };

  const Stripe = vi.fn(() => createMockStripe());
  Stripe.errors = {
    StripeError: StripeErrorClass
  };
  return {
    default: Stripe,
    Stripe: Stripe
  };
});

import { StripePaymentMethodService } from '@/lib/services/stripe-payment-method-service';
import type { Stripe } from 'stripe';
import { createClient } from '@supabase/supabase-js';

describe('E2E Stripe Checkout Flow Tests', () => {
  let paymentService: StripePaymentMethodService;
  let mockStripe: ReturnType<typeof createMockStripe>;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock the Supabase client methods used by the service
    const mockSupabaseClient = {
      from: vi.fn(() => ({
        insert: vi.fn(() => ({
          select: vi.fn(() => ({
            single: vi.fn(() => Promise.resolve({ 
              data: { id: 'log-123' }, 
              error: null 
            }))
          }))
        })),
        update: vi.fn(() => ({
          eq: vi.fn(() => Promise.resolve({ error: null }))
        }))
      }))
    };
    
    // Mock createClient to return our mock
    vi.mocked(createClient).mockReturnValue(mockSupabaseClient as any);
    
    mockStripe = createMockStripe();
    paymentService = new StripePaymentMethodService();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Checkout Session Creation', () => {
    it('should create a complete checkout session with line items and customer', async () => {
      const sessionParams = {
        mode: 'payment' as const,
        customer_email: '<EMAIL>',
        line_items: [
          {
            price_data: {
              currency: 'usd',
              unit_amount: 5000,
              product_data: {
                name: 'Legal Consultation',
                description: '1 hour legal consultation'
              }
            },
            quantity: 1
          }
        ],
        success_url: 'https://test.example.com/success?session_id={CHECKOUT_SESSION_ID}',
        cancel_url: 'https://test.example.com/cancel',
        metadata: {
          tenant_id: 'tenant-123',
          user_id: 'user-123',
          case_id: 'case-456'
        }
      };

      const session = await mockStripe.checkout.sessions.create(sessionParams);

      expect(session).toBeDefined();
      expect(session.id).toBe('cs_test_session');
      expect(session.url).toContain('https://checkout.stripe.com');
      expect(session.payment_status).toBe('unpaid');
      expect(session.status).toBe('open');
      expect(mockStripe.checkout.sessions.create).toHaveBeenCalledWith(sessionParams);
    });

    it('should create checkout session with multiple line items and tax', async () => {
      const sessionParams = {
        mode: 'payment' as const,
        customer_email: '<EMAIL>',
        line_items: [
          {
            price_data: {
              currency: 'usd',
              unit_amount: 10000,
              product_data: {
                name: 'Document Review',
                description: 'Comprehensive legal document review'
              }
            },
            quantity: 1
          },
          {
            price_data: {
              currency: 'usd',
              unit_amount: 5000,
              product_data: {
                name: 'Filing Fee',
                description: 'Court filing fee'
              }
            },
            quantity: 1
          }
        ],
        automatic_tax: { enabled: true },
        tax_id_collection: { enabled: true },
        success_url: 'https://test.example.com/success',
        cancel_url: 'https://test.example.com/cancel'
      };

      const session = await mockStripe.checkout.sessions.create(sessionParams);

      expect(session).toBeDefined();
      expect(session.id).toBeTruthy();
      expect(mockStripe.checkout.sessions.create).toHaveBeenCalledWith(
        expect.objectContaining({
          line_items: expect.arrayContaining([
            expect.objectContaining({
              price_data: expect.objectContaining({
                unit_amount: 10000
              })
            }),
            expect.objectContaining({
              price_data: expect.objectContaining({
                unit_amount: 5000
              })
            })
          ])
        })
      );
    });

    it('should handle subscription checkout sessions', async () => {
      const sessionParams = {
        mode: 'subscription' as const,
        customer_email: '<EMAIL>',
        line_items: [
          {
            price: 'price_monthly_plan',
            quantity: 1
          }
        ],
        subscription_data: {
          trial_period_days: 14,
          metadata: {
            tenant_id: 'tenant-123'
          }
        },
        success_url: 'https://test.example.com/success',
        cancel_url: 'https://test.example.com/cancel'
      };

      const session = await mockStripe.checkout.sessions.create(sessionParams);

      expect(session).toBeDefined();
      expect(mockStripe.checkout.sessions.create).toHaveBeenCalledWith(
        expect.objectContaining({
          mode: 'subscription',
          subscription_data: expect.objectContaining({
            trial_period_days: 14
          })
        })
      );
    });
  });

  describe('Payment Completion Flow', () => {
    it('should handle successful payment completion', async () => {
      // Create checkout session
      const session = await mockStripe.checkout.sessions.create({
        mode: 'payment' as const,
        customer_email: '<EMAIL>',
        line_items: [{
          price_data: {
            currency: 'usd',
            unit_amount: 5000,
            product_data: { name: 'Legal Service' }
          },
          quantity: 1
        }],
        success_url: 'https://test.example.com/success',
        cancel_url: 'https://test.example.com/cancel'
      });

      // Simulate successful payment completion
      mockStripe.checkout.sessions.retrieve.mockResolvedValueOnce({
        id: session.id,
        payment_status: 'paid',
        status: 'complete',
        payment_intent: MOCK_PAYMENT_INTENTS.SUCCEEDED,
        customer: MOCK_CUSTOMERS.VALID,
        amount_total: 5000
      });

      // Retrieve completed session
      const completedSession = await mockStripe.checkout.sessions.retrieve(session.id);

      expect(completedSession.payment_status).toBe('paid');
      expect(completedSession.status).toBe('complete');
      expect(completedSession.payment_intent).toBe(MOCK_PAYMENT_INTENTS.SUCCEEDED);

      // Verify payment intent details
      const paymentIntent = await mockStripe.paymentIntents.retrieve(
        completedSession.payment_intent
      );
      expect(paymentIntent.status).toBe('succeeded');
    });

    it('should handle webhook for successful checkout completion', async () => {
      const webhookEvent = createMockWebhookEvent('checkout.session.completed', {
        id: 'cs_test_completed',
        payment_status: 'paid',
        status: 'complete',
        customer_email: '<EMAIL>',
        metadata: {
          tenant_id: 'tenant-123',
          user_id: 'user-123'
        },
        amount_total: 5000,
        currency: 'usd'
      });

      // Process webhook event
      expect(webhookEvent.type).toBe('checkout.session.completed');
      expect(webhookEvent.data.object.payment_status).toBe('paid');
      expect(webhookEvent.data.object.metadata.tenant_id).toBe('tenant-123');
    });

    it('should handle expired checkout sessions', async () => {
      mockStripe.checkout.sessions.retrieve.mockResolvedValueOnce({
        id: 'cs_test_expired',
        payment_status: 'unpaid',
        status: 'expired',
        expires_at: Math.floor(Date.now() / 1000) - 3600 // 1 hour ago
      });

      const expiredSession = await mockStripe.checkout.sessions.retrieve('cs_test_expired');

      expect(expiredSession.status).toBe('expired');
      expect(expiredSession.payment_status).toBe('unpaid');
      expect(expiredSession.expires_at).toBeLessThan(Math.floor(Date.now() / 1000));
    });
  });

  describe('Payment Cancellation Scenarios', () => {
    it('should handle customer-initiated checkout cancellation', async () => {
      // Create checkout session
      const session = await mockStripe.checkout.sessions.create({
        mode: 'payment' as const,
        customer_email: '<EMAIL>',
        line_items: [{
          price_data: {
            currency: 'usd',
            unit_amount: 5000,
            product_data: { name: 'Legal Service' }
          },
          quantity: 1
        }],
        success_url: 'https://test.example.com/success',
        cancel_url: 'https://test.example.com/cancel'
      });

      // Simulate user cancellation
      mockStripe.checkout.sessions.retrieve.mockResolvedValueOnce({
        id: session.id,
        payment_status: 'unpaid',
        status: 'open',
        url: session.url
      });

      const canceledSession = await mockStripe.checkout.sessions.retrieve(session.id);

      expect(canceledSession.payment_status).toBe('unpaid');
      expect(canceledSession.status).toBe('open');
    });

    it('should handle payment intent cancellation after checkout', async () => {
      const paymentIntentId = 'pi_test_cancel';
      
      // Create payment intent through checkout
      mockStripe.paymentIntents.create.mockResolvedValueOnce({
        id: paymentIntentId,
        status: 'requires_payment_method',
        amount: 5000,
        currency: 'usd'
      });

      // Cancel payment intent
      const canceledIntent = await mockStripe.paymentIntents.cancel(paymentIntentId);

      expect(canceledIntent.id).toBe(paymentIntentId);
      expect(canceledIntent.status).toBe('canceled');
      expect(canceledIntent.cancellation_reason).toBe('requested_by_customer');
    });

    it('should handle webhook for canceled checkout session', async () => {
      const webhookEvent = createMockWebhookEvent('checkout.session.expired', {
        id: 'cs_test_expired',
        payment_status: 'unpaid',
        status: 'expired',
        customer_email: '<EMAIL>',
        metadata: {
          tenant_id: 'tenant-123'
        }
      });

      expect(webhookEvent.type).toBe('checkout.session.expired');
      expect(webhookEvent.data.object.payment_status).toBe('unpaid');
      expect(webhookEvent.data.object.status).toBe('expired');
    });
  });

  describe('Invalid Payment Method Handling', () => {
    it('should handle declined card during checkout', async () => {
      // Create payment method that will be declined
      const stripeError = new Error('Your card was declined.');
      stripeError.name = 'StripeError';
      (stripeError as any).code = 'card_declined';
      (stripeError as any).type = 'card_error';
      mockStripe.paymentMethods.create.mockRejectedValueOnce(stripeError);

      await expect(
        paymentService.createPaymentMethod({
          tenant_id: 'tenant-123',
          customer_id: 'customer-123',
          customer_email: '<EMAIL>',
          customer_name: 'John Doe',
          payment_method_code: 'card',
          country_code: 'US',
          currency_code: 'USD',
          payment_data: {
            number: '****************', // Declined card
            exp_month: 12,
            exp_year: 2025,
            cvc: '123'
          }
        })
      ).rejects.toThrow('Your card was declined');
    });

    it('should handle insufficient funds error', async () => {
      // Create payment intent with insufficient funds
      mockStripe.paymentIntents.create.mockResolvedValueOnce({
        id: 'pi_insufficient_funds',
        status: 'requires_payment_method',
        amount: 5000,
        currency: 'usd',
        last_payment_error: {
          code: 'card_declined',
          decline_code: 'insufficient_funds',
          message: 'Your card has insufficient funds.'
        }
      });

      const result = await paymentService.processPayment(
        MOCK_PAYMENT_METHODS.INSUFFICIENT_FUNDS,
        5000,
        'USD',
        'tenant-123'
      );

      expect(result.status).toBe('requires_payment_method');
      expect(result.requires_action).toBe(true);
    });

    it('should handle expired card error', async () => {
      const expiredError = new Error('Your card has expired.');
      expiredError.name = 'StripeError';
      (expiredError as any).code = 'expired_card';
      (expiredError as any).type = 'card_error';
      mockStripe.paymentMethods.create.mockRejectedValueOnce(expiredError);

      await expect(
        paymentService.createPaymentMethod({
          tenant_id: 'tenant-123',
          customer_id: 'customer-123',
          customer_email: '<EMAIL>',
          customer_name: 'John Doe',
          payment_method_code: 'card',
          country_code: 'US',
          currency_code: 'USD',
          payment_data: {
            number: '****************', // Expired card
            exp_month: 1,
            exp_year: 2020,
            cvc: '123'
          }
        })
      ).rejects.toThrow('Your card has expired');
    });

    it('should handle invalid CVC error', async () => {
      const cvcError = new Error('Your card\'s security code is incorrect.');
      cvcError.name = 'StripeError';
      (cvcError as any).code = 'incorrect_cvc';
      (cvcError as any).type = 'card_error';
      mockStripe.paymentMethods.create.mockRejectedValueOnce(cvcError);

      await expect(
        paymentService.createPaymentMethod({
          tenant_id: 'tenant-123',
          customer_id: 'customer-123',
          customer_email: '<EMAIL>',
          customer_name: 'John Doe',
          payment_method_code: 'card',
          country_code: 'US',
          currency_code: 'USD',
          payment_data: {
            number: '****************',
            exp_month: 12,
            exp_year: 2025,
            cvc: '99' // Invalid CVC
          }
        })
      ).rejects.toThrow('security code is incorrect');
    });

    it('should handle processing errors gracefully', async () => {
      mockStripe.paymentIntents.create.mockRejectedValueOnce(
        new Error('Network error: Unable to connect to Stripe')
      );

      await expect(
        paymentService.processPayment(
          'pm_test',
          5000,
          'USD',
          'tenant-123'
        )
      ).rejects.toThrow('Network error');
    });
  });

  describe('Checkout Session with Customer Portal', () => {
    it('should create checkout with customer portal access', async () => {
      const sessionParams = {
        mode: 'subscription' as const,
        customer: MOCK_CUSTOMERS.VALID,
        line_items: [{
          price: 'price_monthly_plan',
          quantity: 1
        }],
        success_url: 'https://test.example.com/success',
        cancel_url: 'https://test.example.com/cancel',
        customer_update: {
          address: 'auto',
          name: 'auto'
        },
        billing_address_collection: 'required',
        allow_promotion_codes: true
      };

      const session = await mockStripe.checkout.sessions.create(sessionParams);

      expect(session).toBeDefined();
      expect(mockStripe.checkout.sessions.create).toHaveBeenCalledWith(
        expect.objectContaining({
          customer: MOCK_CUSTOMERS.VALID,
          billing_address_collection: 'required',
          allow_promotion_codes: true
        })
      );
    });
  });
});