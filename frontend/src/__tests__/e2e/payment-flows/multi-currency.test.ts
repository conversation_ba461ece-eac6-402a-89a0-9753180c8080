/**
 * E2E Multi-Currency Payment Tests
 * 
 * Comprehensive tests for multi-currency payment flows including USD, EUR, GBP, CAD,
 * currency conversion accuracy, and region-specific payment methods.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { 
  createMockStripe, 
  MOCK_PAYMENT_METHODS,
  MOCK_EXCHANGE_RATES
} from '../../setup/stripe-mocks';

// Mock environment setup
vi.hoisted(() => {
  process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co';
  process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-service-key';
  process.env.STRIPE_SECRET_KEY = 'sk_test_123';
  process.env.PAYMENT_ENCRYPTION_KEY = 'a'.repeat(64);
  process.env.NODE_ENV = 'test';
  process.env.SENTRY_DSN = '';
});

// Mock Sentry before any imports
vi.mock('@sentry/nextjs', () => ({
  withSentryConfig: vi.fn((config) => config),
  captureException: vi.fn(),
  captureMessage: vi.fn(),
  withSentry: vi.fn((handler) => handler),
}));

import { StripePaymentMethodService } from '@/lib/services/stripe-payment-method-service';
import { RegionalPaymentMethodService } from '@/lib/services/regional-payment-method-service';

// Mock Stripe
vi.mock('stripe', () => ({
  default: vi.fn(() => createMockStripe()),
  Stripe: {
    errors: {
      StripeError: class StripeError extends Error {
        constructor(message: string, public code?: string, public type?: string) {
          super(message);
          this.name = 'StripeError';
        }
      }
    }
  }
}));

describe('E2E Multi-Currency Payment Tests', () => {
  let paymentService: StripePaymentMethodService;
  let regionalService: RegionalPaymentMethodService;
  let mockStripe: ReturnType<typeof createMockStripe>;

  beforeEach(() => {
    vi.clearAllMocks();
    mockStripe = createMockStripe();
    paymentService = new StripePaymentMethodService();
    regionalService = new RegionalPaymentMethodService();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Multi-Currency Payment Processing', () => {
    it('should process payments in USD', async () => {
      const result = await paymentService.processPayment(
        MOCK_PAYMENT_METHODS.VALID_CARD,
        10000, // $100.00
        'USD',
        'tenant-123',
        {
          confirm_immediately: true,
          metadata: {
            currency_test: 'USD',
            region: 'US'
          }
        }
      );

      expect(result.status).toBe('succeeded');
      expect(mockStripe.paymentIntents.create).toHaveBeenCalledWith(
        expect.objectContaining({
          amount: 10000,
          currency: 'usd'
        }),
        expect.any(Object)
      );
    });

    it('should process payments in EUR with proper conversion', async () => {
      const amountUSD = 10000; // $100.00
      const expectedEUR = Math.round(amountUSD * MOCK_EXCHANGE_RATES.EUR); // €85.00

      const result = await paymentService.processPayment(
        MOCK_PAYMENT_METHODS.VALID_CARD,
        expectedEUR,
        'EUR',
        'tenant-123',
        {
          confirm_immediately: true,
          metadata: {
            currency_test: 'EUR',
            region: 'EU',
            original_amount_usd: amountUSD
          }
        }
      );

      expect(result.status).toBe('succeeded');
      expect(mockStripe.paymentIntents.create).toHaveBeenCalledWith(
        expect.objectContaining({
          amount: expectedEUR,
          currency: 'eur'
        }),
        expect.any(Object)
      );
    });

    it('should process payments in GBP with proper conversion', async () => {
      const amountUSD = 10000; // $100.00
      const expectedGBP = Math.round(amountUSD * MOCK_EXCHANGE_RATES.GBP); // £73.00

      const result = await paymentService.processPayment(
        MOCK_PAYMENT_METHODS.VALID_CARD,
        expectedGBP,
        'GBP',
        'tenant-123',
        {
          confirm_immediately: true,
          metadata: {
            currency_test: 'GBP',
            region: 'GB',
            original_amount_usd: amountUSD
          }
        }
      );

      expect(result.status).toBe('succeeded');
      expect(mockStripe.paymentIntents.create).toHaveBeenCalledWith(
        expect.objectContaining({
          amount: expectedGBP,
          currency: 'gbp'
        }),
        expect.any(Object)
      );
    });

    it('should process payments in CAD with proper conversion', async () => {
      const amountUSD = 10000; // $100.00
      const expectedCAD = Math.round(amountUSD * MOCK_EXCHANGE_RATES.CAD); // $125.00 CAD

      const result = await paymentService.processPayment(
        MOCK_PAYMENT_METHODS.VALID_CARD,
        expectedCAD,
        'CAD',
        'tenant-123',
        {
          confirm_immediately: true,
          metadata: {
            currency_test: 'CAD',
            region: 'CA',
            original_amount_usd: amountUSD
          }
        }
      );

      expect(result.status).toBe('succeeded');
      expect(mockStripe.paymentIntents.create).toHaveBeenCalledWith(
        expect.objectContaining({
          amount: expectedCAD,
          currency: 'cad'
        }),
        expect.any(Object)
      );
    });

    it('should handle currency conversion for checkout sessions', async () => {
      const baseAmountUSD = 50000; // $500.00

      // Create checkout sessions in different currencies
      const currencies = ['USD', 'EUR', 'GBP', 'CAD'];
      
      for (const currency of currencies) {
        const exchangeRate = MOCK_EXCHANGE_RATES[currency as keyof typeof MOCK_EXCHANGE_RATES];
        const convertedAmount = Math.round(baseAmountUSD * exchangeRate);

        const session = await mockStripe.checkout.sessions.create({
          mode: 'payment',
          line_items: [{
            price_data: {
              currency: currency.toLowerCase(),
              unit_amount: convertedAmount,
              product_data: {
                name: `Legal Services (${currency})`,
                description: `Professional legal consultation billed in ${currency}`
              }
            },
            quantity: 1
          }],
          success_url: `https://test.example.com/success?currency=${currency}`,
          cancel_url: 'https://test.example.com/cancel'
        });

        expect(session).toBeDefined();
        expect(mockStripe.checkout.sessions.create).toHaveBeenCalledWith(
          expect.objectContaining({
            line_items: expect.arrayContaining([
              expect.objectContaining({
                price_data: expect.objectContaining({
                  currency: currency.toLowerCase(),
                  unit_amount: convertedAmount
                })
              })
            ])
          })
        );
      }
    });
  });

  describe('Region-Specific Payment Methods', () => {
    it('should process SEPA Direct Debit payments for EUR', async () => {
      // Create SEPA payment method
      const sepaPaymentMethod = await paymentService.createPaymentMethod({
        tenant_id: 'tenant-123',
        customer_id: 'customer-123',
        customer_email: '<EMAIL>',
        customer_name: 'Hans Schmidt',
        payment_method_code: 'sepa_debit',
        country_code: 'DE',
        currency_code: 'EUR',
        payment_data: {
          iban: '**********************'
        },
        billing_address: {
          line1: 'Hauptstraße 123',
          city: 'Berlin',
          postal_code: '10115',
          country: 'DE'
        }
      });

      expect(sepaPaymentMethod.stripe_payment_method_id).toBeDefined();
      expect(sepaPaymentMethod.status).toBe('succeeded');

      // Process payment with SEPA
      const payment = await paymentService.processPayment(
        sepaPaymentMethod.stripe_payment_method_id,
        5000, // €50.00
        'EUR',
        'tenant-123',
        {
          statement_descriptor: 'Legal Services DE',
          metadata: {
            payment_method: 'sepa_debit',
            region: 'DE'
          }
        }
      );

      expect(payment.status).toBe('succeeded');
    });

    it('should process ACH Direct Debit payments for USD', async () => {
      // Create ACH payment method
      const achPaymentMethod = await paymentService.createPaymentMethod({
        tenant_id: 'tenant-123',
        customer_id: 'customer-123',
        customer_email: '<EMAIL>',
        customer_name: 'John Smith',
        payment_method_code: 'ach',
        country_code: 'US',
        currency_code: 'USD',
        payment_data: {
          routing_number: '*********',
          account_number: '************',
          account_holder_type: 'individual',
          account_type: 'checking'
        },
        billing_address: {
          line1: '123 Main St',
          city: 'New York',
          state: 'NY',
          postal_code: '10001',
          country: 'US'
        }
      });

      expect(achPaymentMethod.stripe_payment_method_id).toBeDefined();
      expect(achPaymentMethod.status).toBe('succeeded');

      // Process payment with ACH
      const payment = await paymentService.processPayment(
        achPaymentMethod.stripe_payment_method_id,
        10000, // $100.00
        'USD',
        'tenant-123',
        {
          statement_descriptor: 'Legal Services US',
          metadata: {
            payment_method: 'ach',
            region: 'US'
          }
        }
      );

      expect(payment.status).toBe('succeeded');
    });

    it('should process Bancontact payments for EUR in Belgium', async () => {
      // Create Bancontact payment method
      const bancontactPaymentMethod = await paymentService.createPaymentMethod({
        tenant_id: 'tenant-123',
        customer_id: 'customer-123',
        customer_email: '<EMAIL>',
        customer_name: 'Jean Dupont',
        payment_method_code: 'bancontact',
        country_code: 'BE',
        currency_code: 'EUR',
        payment_data: {}, // Bancontact doesn't require additional data
        billing_address: {
          line1: 'Rue de la Loi 42',
          city: 'Brussels',
          postal_code: '1000',
          country: 'BE'
        }
      });

      expect(bancontactPaymentMethod.stripe_payment_method_id).toBeDefined();
      expect(bancontactPaymentMethod.status).toBe('succeeded');
    });

    it('should process iDEAL payments for EUR in Netherlands', async () => {
      // Create iDEAL payment method
      const idealPaymentMethod = await paymentService.createPaymentMethod({
        tenant_id: 'tenant-123',
        customer_id: 'customer-123',
        customer_email: '<EMAIL>',
        customer_name: 'Jan van der Berg',
        payment_method_code: 'ideal',
        country_code: 'NL',
        currency_code: 'EUR',
        payment_data: {
          bank: 'rabobank'
        },
        billing_address: {
          line1: 'Damrak 123',
          city: 'Amsterdam',
          postal_code: '1012 LG',
          country: 'NL'
        }
      });

      expect(idealPaymentMethod.stripe_payment_method_id).toBeDefined();
      expect(idealPaymentMethod.status).toBe('succeeded');
    });

    it('should process Sofort payments for multiple European countries', async () => {
      const sofortCountries = [
        { code: 'DE', name: 'Germany' },
        { code: 'AT', name: 'Austria' },
        { code: 'BE', name: 'Belgium' },
        { code: 'NL', name: 'Netherlands' }
      ];

      for (const country of sofortCountries) {
        const sofortPaymentMethod = await paymentService.createPaymentMethod({
          tenant_id: 'tenant-123',
          customer_id: 'customer-123',
          customer_email: `test@example.${country.code.toLowerCase()}`,
          customer_name: `Test User ${country.name}`,
          payment_method_code: 'sofort',
          country_code: country.code as any,
          currency_code: 'EUR',
          payment_data: {},
          billing_address: {
            line1: 'Test Street 123',
            city: 'Test City',
            postal_code: '12345',
            country: country.code
          }
        });

        expect(sofortPaymentMethod.stripe_payment_method_id).toBeDefined();
        expect(sofortPaymentMethod.status).toBe('succeeded');
      }
    });
  });

  describe('Currency Conversion Accuracy', () => {
    it('should accurately convert between currencies for display', async () => {
      const testAmounts = [
        { usd: 10000, description: '$100.00' },
        { usd: 25050, description: '$250.50' },
        { usd: 99999, description: '$999.99' },
        { usd: 150000, description: '$1,500.00' }
      ];

      for (const test of testAmounts) {
        const conversions = {
          USD: test.usd,
          EUR: Math.round(test.usd * MOCK_EXCHANGE_RATES.EUR),
          GBP: Math.round(test.usd * MOCK_EXCHANGE_RATES.GBP),
          CAD: Math.round(test.usd * MOCK_EXCHANGE_RATES.CAD)
        };

        // Verify conversion accuracy
        expect(conversions.EUR).toBe(Math.round(test.usd * 0.85));
        expect(conversions.GBP).toBe(Math.round(test.usd * 0.73));
        expect(conversions.CAD).toBe(Math.round(test.usd * 1.25));

        // Create prices in each currency
        for (const [currency, amount] of Object.entries(conversions)) {
          const price = await mockStripe.prices.create({
            unit_amount: amount,
            currency: currency.toLowerCase(),
            product_data: {
              name: `Legal Service (${test.description} USD)`
            }
          });

          expect(price.unit_amount).toBe(amount);
          expect(price.currency).toBe(currency.toLowerCase());
        }
      }
    });

    it('should handle currency rounding edge cases', async () => {
      // Test amounts that could cause rounding issues
      const edgeCases = [
        { usd: 333, expectedEur: 283 }, // $3.33 -> €2.83
        { usd: 1337, expectedEur: 1136 }, // $13.37 -> €11.36
        { usd: 9999, expectedEur: 8499 } // $99.99 -> €84.99
      ];

      for (const testCase of edgeCases) {
        const eurAmount = Math.round(testCase.usd * MOCK_EXCHANGE_RATES.EUR);
        expect(eurAmount).toBe(testCase.expectedEur);

        // Process payment with exact amounts
        const result = await paymentService.processPayment(
          MOCK_PAYMENT_METHODS.VALID_CARD,
          eurAmount,
          'EUR',
          'tenant-123'
        );

        expect(result.status).toBe('succeeded');
      }
    });

    it('should maintain precision for large currency amounts', async () => {
      const largeAmounts = [
        { usd: 1000000, description: '$10,000.00' }, // 10k USD
        { usd: 10000000, description: '$100,000.00' }, // 100k USD
        { usd: 100000000, description: '$1,000,000.00' } // 1M USD
      ];

      for (const amount of largeAmounts) {
        const conversions = {
          EUR: Math.round(amount.usd * MOCK_EXCHANGE_RATES.EUR),
          GBP: Math.round(amount.usd * MOCK_EXCHANGE_RATES.GBP),
          CAD: Math.round(amount.usd * MOCK_EXCHANGE_RATES.CAD)
        };

        // Verify large amount conversions maintain accuracy
        for (const [currency, convertedAmount] of Object.entries(conversions)) {
          const result = await paymentService.processPayment(
            MOCK_PAYMENT_METHODS.VALID_CARD,
            convertedAmount,
            currency as any,
            'tenant-123',
            {
              metadata: {
                large_amount_test: true,
                original_usd: amount.usd,
                description: amount.description
              }
            }
          );

          expect(result.status).toBe('succeeded');
        }
      }
    });
  });

  describe('Multi-Currency Subscription Handling', () => {
    it('should create subscriptions in different currencies', async () => {
      const subscriptionPlans = [
        { currency: 'USD', monthlyPrice: 9900 }, // $99/month
        { currency: 'EUR', monthlyPrice: 8415 }, // €84.15/month
        { currency: 'GBP', monthlyPrice: 7227 }, // £72.27/month
        { currency: 'CAD', monthlyPrice: 12375 } // $123.75 CAD/month
      ];

      for (const plan of subscriptionPlans) {
        // Create price for subscription
        const price = await mockStripe.prices.create({
          unit_amount: plan.monthlyPrice,
          currency: plan.currency.toLowerCase(),
          recurring: { interval: 'month' },
          product_data: {
            name: `Legal Services Subscription (${plan.currency})`
          }
        });

        // Create subscription
        const subscription = await mockStripe.subscriptions.create({
          customer: 'cus_test',
          items: [{ price: price.id }],
          payment_behavior: 'default_incomplete',
          metadata: {
            currency: plan.currency,
            tenant_id: 'tenant-123'
          }
        });

        expect(subscription).toBeDefined();
        expect(subscription.status).toBe('active');
      }
    });
  });
});