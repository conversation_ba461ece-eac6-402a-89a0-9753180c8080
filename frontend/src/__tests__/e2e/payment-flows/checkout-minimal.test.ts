/**
 * Minimal E2E Stripe Checkout Flow Test
 * 
 * Demonstrates working E2E test for checkout session creation
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { createMockStripe } from '../../setup/stripe-mocks';

// Mock all dependencies
vi.mock('@sentry/nextjs', () => ({
  withSentryConfig: vi.fn((config) => config),
  captureException: vi.fn(),
  captureMessage: vi.fn(),
  withSentry: vi.fn((handler) => handler),
}));

vi.mock('stripe', () => {
  const Stripe = vi.fn(() => createMockStripe());
  Stripe.errors = { StripeError: Error };
  return { default: Stripe, Stripe };
});

describe('E2E Stripe Checkout - Minimal Working Test', () => {
  let mockStripe: ReturnType<typeof createMockStripe>;

  beforeEach(() => {
    vi.clearAllMocks();
    mockStripe = createMockStripe();
  });

  it('should create a checkout session successfully', async () => {
    const sessionParams = {
      mode: 'payment' as const,
      customer_email: '<EMAIL>',
      line_items: [
        {
          price_data: {
            currency: 'usd',
            unit_amount: 5000,
            product_data: {
              name: 'Legal Consultation',
              description: '1 hour legal consultation'
            }
          },
          quantity: 1
        }
      ],
      success_url: 'https://test.example.com/success?session_id={CHECKOUT_SESSION_ID}',
      cancel_url: 'https://test.example.com/cancel',
      metadata: {
        tenant_id: 'tenant-123',
        user_id: 'user-123',
        case_id: 'case-456'
      }
    };

    const session = await mockStripe.checkout.sessions.create(sessionParams);

    // Verify the response
    expect(session).toBeDefined();
    expect(session.id).toBe('cs_test_session');
    expect(session.url).toContain('https://checkout.stripe.com');
    expect(session.payment_status).toBe('unpaid');
    expect(session.status).toBe('open');
    
    // Verify the mock was called correctly
    expect(mockStripe.checkout.sessions.create).toHaveBeenCalledWith(sessionParams);
  });

  it('should handle 3D Secure payment flow', async () => {
    // Create payment intent requiring 3DS
    const paymentIntent = await mockStripe.paymentIntents.create({
      amount: 5000,
      currency: 'eur',
      payment_method: 'pm_card_threeDSecureRequired',
      confirm: true,
      return_url: 'https://test.example.com/return'
    });

    expect(paymentIntent.status).toBe('requires_action');
    expect(paymentIntent.next_action?.type).toBe('redirect_to_url');
    expect(paymentIntent.client_secret).toBe('pi_test_secret_3ds');
  });

  it('should process multi-currency payments', async () => {
    const currencies = ['usd', 'eur', 'gbp', 'cad'];
    
    for (const currency of currencies) {
      const session = await mockStripe.checkout.sessions.create({
        mode: 'payment',
        line_items: [{
          price_data: {
            currency,
            unit_amount: 10000,
            product_data: { name: `Service in ${currency.toUpperCase()}` }
          },
          quantity: 1
        }],
        success_url: 'https://test.example.com/success',
        cancel_url: 'https://test.example.com/cancel'
      });

      expect(session.id).toBe('cs_test_session');
      expect(session.url).toBeDefined();
    }
  });

  it('should handle subscription creation with trial', async () => {
    const subscription = await mockStripe.subscriptions.create({
      customer: 'cus_test',
      items: [{ price: 'price_monthly_99' }],
      trial_period_days: 14,
      metadata: { tenant_id: 'tenant-123' }
    });

    expect(subscription.id).toBe('sub_trialing');
    expect(subscription.status).toBe('trialing');
  });

  it('should handle payment failures gracefully', async () => {
    const paymentIntent = await mockStripe.paymentIntents.create({
      amount: 5000,
      currency: 'usd',
      payment_method: 'pm_card_visa_chargeDeclined',
      confirm: true
    });

    expect(paymentIntent.status).toBe('requires_payment_method');
    expect(paymentIntent.last_payment_error).toBeDefined();
    expect(paymentIntent.last_payment_error?.code).toBe('card_declined');
  });
});