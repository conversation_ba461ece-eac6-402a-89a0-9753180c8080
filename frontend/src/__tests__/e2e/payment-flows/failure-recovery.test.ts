/**
 * E2E Payment Failure & Recovery Tests
 * 
 * Comprehensive tests for handling payment failures, network issues,
 * retry mechanisms, and recovery flows.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { 
  createMockStripe, 
  MOCK_PAYMENT_METHODS,
  MOCK_PAYMENT_INTENTS,
  createMockWebhookEvent
} from '../../setup/stripe-mocks';

// Mock environment setup
vi.hoisted(() => {
  process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co';
  process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-service-key';
  process.env.STRIPE_SECRET_KEY = 'sk_test_123';
  process.env.PAYMENT_ENCRYPTION_KEY = 'a'.repeat(64);
  process.env.NODE_ENV = 'test';
});

import { StripePaymentMethodService } from '@/lib/services/stripe-payment-method-service';
import type { Stripe } from 'stripe';

// Mock Stripe
vi.mock('stripe', () => ({
  default: vi.fn(() => createMockStripe()),
  Stripe: {
    errors: {
      StripeError: class StripeError extends Error {
        constructor(message: string, public code?: string, public type?: string) {
          super(message);
          this.name = 'StripeError';
        }
      },
      StripeCardError: class StripeCardError extends Error {
        constructor(message: string, public code?: string, public decline_code?: string) {
          super(message);
          this.name = 'StripeCardError';
          this.type = 'card_error';
        }
      },
      StripeInvalidRequestError: class StripeInvalidRequestError extends Error {
        constructor(message: string, public code?: string) {
          super(message);
          this.name = 'StripeInvalidRequestError';
          this.type = 'invalid_request_error';
        }
      },
      StripeAPIError: class StripeAPIError extends Error {
        constructor(message: string, public code?: string) {
          super(message);
          this.name = 'StripeAPIError';
          this.type = 'api_error';
        }
      },
      StripeConnectionError: class StripeConnectionError extends Error {
        constructor(message: string) {
          super(message);
          this.name = 'StripeConnectionError';
          this.type = 'api_connection_error';
        }
      },
      StripeAuthenticationError: class StripeAuthenticationError extends Error {
        constructor(message: string) {
          super(message);
          this.name = 'StripeAuthenticationError';
          this.type = 'authentication_error';
        }
      },
      StripeRateLimitError: class StripeRateLimitError extends Error {
        constructor(message: string) {
          super(message);
          this.name = 'StripeRateLimitError';
          this.type = 'rate_limit_error';
        }
      }
    }
  }
}));

describe('E2E Payment Failure & Recovery Tests', () => {
  let paymentService: StripePaymentMethodService;
  let mockStripe: ReturnType<typeof createMockStripe>;

  beforeEach(() => {
    vi.clearAllMocks();
    mockStripe = createMockStripe();
    paymentService = new StripePaymentMethodService();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Insufficient Funds Scenarios', () => {
    it('should handle insufficient funds error', async () => {
      mockStripe.paymentIntents.create.mockResolvedValueOnce({
        id: 'pi_insufficient_funds',
        status: 'requires_payment_method',
        amount: 10000,
        currency: 'usd',
        last_payment_error: {
          code: 'card_declined',
          decline_code: 'insufficient_funds',
          message: 'Your card has insufficient funds.',
          payment_method: {
            id: MOCK_PAYMENT_METHODS.INSUFFICIENT_FUNDS
          }
        }
      });

      const result = await paymentService.processPayment(
        MOCK_PAYMENT_METHODS.INSUFFICIENT_FUNDS,
        10000,
        'USD',
        'tenant-123'
      );

      expect(result.status).toBe('requires_payment_method');
      expect(result.requires_action).toBe(true);
    });

    it('should retry payment with different payment method after insufficient funds', async () => {
      // First attempt fails
      mockStripe.paymentIntents.create.mockResolvedValueOnce({
        id: 'pi_retry_after_insufficient',
        status: 'requires_payment_method',
        last_payment_error: {
          code: 'card_declined',
          decline_code: 'insufficient_funds'
        }
      });

      const firstAttempt = await paymentService.processPayment(
        MOCK_PAYMENT_METHODS.INSUFFICIENT_FUNDS,
        5000,
        'USD',
        'tenant-123'
      );

      expect(firstAttempt.status).toBe('requires_payment_method');

      // Update payment method and retry
      mockStripe.paymentIntents.confirm.mockResolvedValueOnce({
        id: 'pi_retry_after_insufficient',
        status: 'succeeded',
        amount: 5000,
        currency: 'usd'
      });

      const secondAttempt = await paymentService.confirmPayment(
        'pi_retry_after_insufficient',
        MOCK_PAYMENT_METHODS.VALID_CARD
      );

      expect(secondAttempt.status).toBe('succeeded');
    });

    it('should handle recurring insufficient funds for subscriptions', async () => {
      // Simulate subscription payment failure
      const webhookEvent = createMockWebhookEvent('invoice.payment_failed', {
        id: 'in_subscription_insufficient',
        subscription: 'sub_monthly',
        amount_due: 9900,
        payment_intent: {
          id: 'pi_subscription_insufficient',
          status: 'requires_payment_method',
          last_payment_error: {
            code: 'card_declined',
            decline_code: 'insufficient_funds'
          }
        },
        attempt_count: 1,
        next_payment_attempt: Math.floor(Date.now() / 1000) + 3 * 24 * 60 * 60
      });

      expect(webhookEvent.type).toBe('invoice.payment_failed');
      expect(webhookEvent.data.object.payment_intent.last_payment_error.decline_code).toBe('insufficient_funds');
      expect(webhookEvent.data.object.next_payment_attempt).toBeGreaterThan(Math.floor(Date.now() / 1000));
    });
  });

  describe('Expired Card Handling', () => {
    it('should detect and handle expired card error', async () => {
      mockStripe.paymentMethods.create.mockRejectedValueOnce(
        new (Stripe as any).errors.StripeCardError(
          'Your card has expired.',
          'expired_card',
          'expired_card'
        )
      );

      await expect(
        paymentService.createPaymentMethod({
          tenant_id: 'tenant-123',
          customer_id: 'customer-123',
          customer_email: '<EMAIL>',
          customer_name: 'John Doe',
          payment_method_code: 'card',
          country_code: 'US',
          currency_code: 'USD',
          payment_data: {
            number: '****************',
            exp_month: 1,
            exp_year: 2020,
            cvc: '123'
          }
        })
      ).rejects.toThrow('Your card has expired');
    });

    it('should prompt for card update when expired', async () => {
      mockStripe.paymentIntents.create.mockResolvedValueOnce({
        id: 'pi_expired_card',
        status: 'requires_payment_method',
        last_payment_error: {
          code: 'expired_card',
          message: 'Your card has expired. Please update your payment method.',
          payment_method: {
            id: MOCK_PAYMENT_METHODS.EXPIRED_CARD,
            card: {
              exp_month: 1,
              exp_year: 2020
            }
          }
        }
      });

      const result = await paymentService.processPayment(
        MOCK_PAYMENT_METHODS.EXPIRED_CARD,
        5000,
        'USD',
        'tenant-123'
      );

      expect(result.status).toBe('requires_payment_method');
    });

    it('should update expired card and retry payment', async () => {
      // Create new payment method
      const newPaymentMethod = await mockStripe.paymentMethods.create({
        type: 'card',
        card: {
          number: '****************',
          exp_month: 12,
          exp_year: 2030,
          cvc: '123'
        }
      });

      // Attach to customer
      await mockStripe.paymentMethods.attach(newPaymentMethod.id, {
        customer: 'cus_test'
      });

      // Retry payment with new card
      mockStripe.paymentIntents.confirm.mockResolvedValueOnce({
        id: 'pi_expired_retry',
        status: 'succeeded',
        payment_method: newPaymentMethod.id
      });

      const result = await paymentService.confirmPayment(
        'pi_expired_retry',
        newPaymentMethod.id
      );

      expect(result.status).toBe('succeeded');
    });
  });

  describe('Network Timeout Recovery', () => {
    it('should handle network timeout with retry', async () => {
      let attemptCount = 0;
      
      mockStripe.paymentIntents.create.mockImplementation(() => {
        attemptCount++;
        if (attemptCount === 1) {
          // First attempt times out
          return Promise.reject(new (Stripe as any).errors.StripeConnectionError(
            'Request timed out'
          ));
        }
        // Second attempt succeeds
        return Promise.resolve({
          id: 'pi_after_timeout',
          status: 'succeeded',
          amount: 5000,
          currency: 'usd'
        });
      });

      // First attempt should fail
      await expect(
        paymentService.processPayment(
          MOCK_PAYMENT_METHODS.VALID_CARD,
          5000,
          'USD',
          'tenant-123'
        )
      ).rejects.toThrow('Request timed out');

      // Reset mock for retry
      attemptCount = 1; // Set to allow success on next call
      
      // Retry should succeed
      const retryResult = await paymentService.processPayment(
        MOCK_PAYMENT_METHODS.VALID_CARD,
        5000,
        'USD',
        'tenant-123',
        { idempotency_key: 'retry-key-123' }
      );

      expect(retryResult.status).toBe('succeeded');
    });

    it('should handle intermittent network failures', async () => {
      const networkErrors = [
        'ECONNREFUSED',
        'ETIMEDOUT',
        'ENOTFOUND',
        'ENETUNREACH'
      ];

      for (const errorCode of networkErrors) {
        mockStripe.paymentIntents.create.mockRejectedValueOnce(
          new (Stripe as any).errors.StripeConnectionError(
            `Network error: ${errorCode}`
          )
        );

        await expect(
          paymentService.processPayment(
            MOCK_PAYMENT_METHODS.VALID_CARD,
            5000,
            'USD',
            'tenant-123'
          )
        ).rejects.toThrow(`Network error: ${errorCode}`);
      }
    });

    it('should implement exponential backoff for retries', async () => {
      const delays = [1000, 2000, 4000]; // Exponential backoff delays
      let attemptCount = 0;

      const retryWithBackoff = async (
        fn: () => Promise<any>,
        maxRetries: number = 3
      ): Promise<any> => {
        for (let i = 0; i < maxRetries; i++) {
          try {
            return await fn();
          } catch (error) {
            attemptCount++;
            if (i === maxRetries - 1) throw error;
            
            // Wait with exponential backoff
            await new Promise(resolve => setTimeout(resolve, delays[i]));
          }
        }
      };

      mockStripe.paymentIntents.create
        .mockRejectedValueOnce(new Error('Network error'))
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({
          id: 'pi_backoff_success',
          status: 'succeeded'
        });

      const result = await retryWithBackoff(() => 
        mockStripe.paymentIntents.create({
          amount: 5000,
          currency: 'usd',
          payment_method: MOCK_PAYMENT_METHODS.VALID_CARD
        })
      );

      expect(result.status).toBe('succeeded');
      expect(attemptCount).toBe(2); // Failed twice before succeeding
    });
  });

  describe('Retry Mechanisms', () => {
    it('should implement idempotent retries', async () => {
      const idempotencyKey = 'idempotent-key-123';
      
      // First call fails
      mockStripe.paymentIntents.create.mockRejectedValueOnce(
        new Error('Temporary failure')
      );

      await expect(
        paymentService.processPayment(
          MOCK_PAYMENT_METHODS.VALID_CARD,
          5000,
          'USD',
          'tenant-123',
          { idempotency_key: idempotencyKey }
        )
      ).rejects.toThrow('Temporary failure');

      // Retry with same idempotency key should return same result
      mockStripe.paymentIntents.create.mockResolvedValueOnce({
        id: 'pi_idempotent',
        status: 'succeeded',
        amount: 5000,
        currency: 'usd'
      });

      const retryResult = await paymentService.processPayment(
        MOCK_PAYMENT_METHODS.VALID_CARD,
        5000,
        'USD',
        'tenant-123',
        { idempotency_key: idempotencyKey }
      );

      expect(retryResult.status).toBe('succeeded');
      expect(mockStripe.paymentIntents.create).toHaveBeenCalledWith(
        expect.any(Object),
        expect.objectContaining({ idempotencyKey })
      );
    });

    it('should handle rate limit errors with retry', async () => {
      mockStripe.paymentIntents.create.mockRejectedValueOnce(
        new (Stripe as any).errors.StripeRateLimitError(
          'Too many requests'
        )
      );

      await expect(
        paymentService.processPayment(
          MOCK_PAYMENT_METHODS.VALID_CARD,
          5000,
          'USD',
          'tenant-123'
        )
      ).rejects.toThrow('Too many requests');

      // Wait and retry
      await new Promise(resolve => setTimeout(resolve, 1000));

      mockStripe.paymentIntents.create.mockResolvedValueOnce({
        id: 'pi_after_rate_limit',
        status: 'succeeded'
      });

      const retryResult = await paymentService.processPayment(
        MOCK_PAYMENT_METHODS.VALID_CARD,
        5000,
        'USD',
        'tenant-123'
      );

      expect(retryResult.status).toBe('succeeded');
    });

    it('should implement circuit breaker pattern', async () => {
      let failureCount = 0;
      const failureThreshold = 3;
      let circuitOpen = false;

      const circuitBreaker = async (fn: () => Promise<any>) => {
        if (circuitOpen) {
          throw new Error('Circuit breaker is open');
        }

        try {
          const result = await fn();
          failureCount = 0; // Reset on success
          return result;
        } catch (error) {
          failureCount++;
          if (failureCount >= failureThreshold) {
            circuitOpen = true;
            setTimeout(() => {
              circuitOpen = false;
              failureCount = 0;
            }, 5000); // Reset after 5 seconds
          }
          throw error;
        }
      };

      // Simulate multiple failures
      for (let i = 0; i < failureThreshold; i++) {
        mockStripe.paymentIntents.create.mockRejectedValueOnce(
          new Error('Service unavailable')
        );

        await expect(
          circuitBreaker(() => 
            mockStripe.paymentIntents.create({
              amount: 5000,
              currency: 'usd'
            })
          )
        ).rejects.toThrow();
      }

      // Circuit should now be open
      await expect(
        circuitBreaker(() => 
          mockStripe.paymentIntents.create({
            amount: 5000,
            currency: 'usd'
          })
        )
      ).rejects.toThrow('Circuit breaker is open');
    });
  });

  describe('Payment Recovery Workflows', () => {
    it('should recover failed payment with customer intervention', async () => {
      // Initial payment fails
      const failedPayment = {
        id: 'pi_recovery_needed',
        status: 'requires_payment_method',
        last_payment_error: {
          code: 'card_declined',
          message: 'Your card was declined.'
        }
      };

      mockStripe.paymentIntents.retrieve.mockResolvedValueOnce(failedPayment);

      // Customer updates payment method
      const newPaymentMethod = await mockStripe.paymentMethods.create({
        type: 'card',
        card: {
          number: '****************',
          exp_month: 12,
          exp_year: 2025,
          cvc: '123'
        }
      });

      // Confirm payment with new method
      mockStripe.paymentIntents.confirm.mockResolvedValueOnce({
        id: failedPayment.id,
        status: 'succeeded',
        payment_method: newPaymentMethod.id
      });

      const recoveredPayment = await paymentService.confirmPayment(
        failedPayment.id,
        newPaymentMethod.id
      );

      expect(recoveredPayment.status).toBe('succeeded');
    });

    it('should handle payment recovery webhooks', async () => {
      // Payment requires action webhook
      const actionRequiredEvent = createMockWebhookEvent('payment_intent.requires_action', {
        id: 'pi_action_required',
        status: 'requires_action',
        next_action: {
          type: 'use_stripe_sdk',
          use_stripe_sdk: {
            type: 'three_d_secure_redirect',
            stripe_js: 'https://hooks.stripe.com/redirect'
          }
        }
      });

      expect(actionRequiredEvent.type).toBe('payment_intent.requires_action');

      // Customer completes action, payment succeeds
      const succeededEvent = createMockWebhookEvent('payment_intent.succeeded', {
        id: 'pi_action_required',
        status: 'succeeded',
        amount: 10000,
        currency: 'usd'
      });

      expect(succeededEvent.type).toBe('payment_intent.succeeded');
      expect(succeededEvent.data.object.status).toBe('succeeded');
    });

    it('should implement dunning process for failed subscriptions', async () => {
      const dunningStages = [
        { day: 0, action: 'initial_failure', status: 'past_due' },
        { day: 3, action: 'first_retry', status: 'past_due' },
        { day: 7, action: 'second_retry', status: 'past_due' },
        { day: 14, action: 'final_notice', status: 'past_due' },
        { day: 21, action: 'subscription_canceled', status: 'canceled' }
      ];

      for (const stage of dunningStages) {
        const event = createMockWebhookEvent(
          stage.action === 'subscription_canceled' 
            ? 'customer.subscription.deleted' 
            : 'invoice.payment_action_required',
          {
            subscription: 'sub_dunning',
            status: stage.status,
            metadata: {
              dunning_stage: stage.action,
              days_since_failure: stage.day
            }
          }
        );

        if (stage.action === 'subscription_canceled') {
          expect(event.data.object.status).toBe('canceled');
        } else {
          expect(event.data.object.status).toBe('past_due');
        }
      }
    });
  });

  describe('Error State Management', () => {
    it('should track and report error patterns', async () => {
      const errorPatterns = [
        { code: 'card_declined', count: 0, threshold: 5 },
        { code: 'insufficient_funds', count: 0, threshold: 3 },
        { code: 'expired_card', count: 0, threshold: 2 }
      ];

      const trackError = (errorCode: string) => {
        const pattern = errorPatterns.find(p => p.code === errorCode);
        if (pattern) {
          pattern.count++;
          if (pattern.count >= pattern.threshold) {
            console.warn(`High frequency of ${errorCode} errors detected`);
          }
        }
      };

      // Simulate multiple errors
      const errors = [
        'card_declined',
        'card_declined',
        'insufficient_funds',
        'card_declined',
        'expired_card',
        'card_declined',
        'insufficient_funds',
        'card_declined' // This triggers the threshold
      ];

      for (const errorCode of errors) {
        trackError(errorCode);
      }

      const declinedPattern = errorPatterns.find(p => p.code === 'card_declined');
      expect(declinedPattern?.count).toBe(5);
      expect(declinedPattern?.count).toBeGreaterThanOrEqual(declinedPattern?.threshold || 0);
    });

    it('should provide actionable error messages', async () => {
      const errorMappings = {
        card_declined: 'Your card was declined. Please try a different payment method.',
        insufficient_funds: 'Your card has insufficient funds. Please add funds or use a different card.',
        expired_card: 'Your card has expired. Please update your card information.',
        incorrect_cvc: 'The security code is incorrect. Please check and try again.',
        processing_error: 'A processing error occurred. Please try again later.'
      };

      for (const [code, message] of Object.entries(errorMappings)) {
        mockStripe.paymentIntents.create.mockResolvedValueOnce({
          id: `pi_${code}`,
          status: 'requires_payment_method',
          last_payment_error: {
            code,
            message
          }
        });

        const result = await paymentService.processPayment(
          MOCK_PAYMENT_METHODS.VALID_CARD,
          5000,
          'USD',
          'tenant-123'
        );

        expect(result.status).toBe('requires_payment_method');
      }
    });
  });
});