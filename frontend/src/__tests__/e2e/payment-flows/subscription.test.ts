/**
 * E2E Subscription Flow Tests
 * 
 * Comprehensive tests for subscription lifecycle including trial periods,
 * modifications, cancellations, and payment failures.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { 
  createMockStripe, 
  MOCK_PAYMENT_METHODS,
  MOCK_SUBSCRIPTIONS,
  MOCK_CUSTOMERS,
  createMockWebhookEvent
} from '../../setup/stripe-mocks';

// Mock environment setup
vi.hoisted(() => {
  process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co';
  process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-service-key';
  process.env.STRIPE_SECRET_KEY = 'sk_test_123';
  process.env.STRIPE_WEBHOOK_SECRET = 'whsec_test_123';
  process.env.PAYMENT_ENCRYPTION_KEY = 'a'.repeat(64);
  process.env.NODE_ENV = 'test';
  process.env.SENTRY_DSN = '';
});

// Mock Sentry before any imports
vi.mock('@sentry/nextjs', () => ({
  withSentryConfig: vi.fn((config) => config),
  captureException: vi.fn(),
  captureMessage: vi.fn(),
  withSentry: vi.fn((handler) => handler),
}));

import { StripePaymentMethodService } from '@/lib/services/stripe-payment-method-service';
import type { Stripe } from 'stripe';

// Mock Stripe
vi.mock('stripe', () => ({
  default: vi.fn(() => createMockStripe()),
  Stripe: {
    errors: {
      StripeError: class StripeError extends Error {
        constructor(message: string, public code?: string, public type?: string) {
          super(message);
          this.name = 'StripeError';
        }
      }
    }
  }
}));

describe('E2E Subscription Flow Tests', () => {
  let paymentService: StripePaymentMethodService;
  let mockStripe: ReturnType<typeof createMockStripe>;

  beforeEach(() => {
    vi.clearAllMocks();
    mockStripe = createMockStripe();
    paymentService = new StripePaymentMethodService();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Trial to Paid Conversion', () => {
    it('should create subscription with trial period', async () => {
      // Create customer
      const customer = await mockStripe.customers.create({
        email: '<EMAIL>',
        name: 'Trial User',
        metadata: {
          tenant_id: 'tenant-123'
        }
      });

      // Create subscription with 14-day trial
      const subscription = await mockStripe.subscriptions.create({
        customer: customer.id,
        items: [{
          price: 'price_monthly_99'
        }],
        trial_period_days: 14,
        payment_behavior: 'default_incomplete',
        metadata: {
          tenant_id: 'tenant-123',
          plan_type: 'professional'
        }
      });

      expect(subscription.id).toBe(MOCK_SUBSCRIPTIONS.TRIALING);
      expect(subscription.status).toBe('trialing');
      expect(subscription.trial_end).toBeGreaterThan(Math.floor(Date.now() / 1000));
    });

    it('should convert trial to paid subscription automatically', async () => {
      // Simulate trial end webhook
      const trialEndEvent = createMockWebhookEvent('customer.subscription.trial_will_end', {
        id: MOCK_SUBSCRIPTIONS.TRIALING,
        customer: MOCK_CUSTOMERS.VALID,
        status: 'trialing',
        trial_end: Math.floor(Date.now() / 1000) + 3 * 24 * 60 * 60, // 3 days from now
        items: {
          data: [{
            price: {
              id: 'price_monthly_99',
              unit_amount: 9900,
              currency: 'usd'
            }
          }]
        }
      });

      expect(trialEndEvent.type).toBe('customer.subscription.trial_will_end');

      // Simulate successful trial conversion
      mockStripe.subscriptions.retrieve.mockResolvedValueOnce({
        id: MOCK_SUBSCRIPTIONS.TRIALING,
        status: 'active',
        current_period_start: Math.floor(Date.now() / 1000),
        current_period_end: Math.floor(Date.now() / 1000) + 30 * 24 * 60 * 60,
        trial_end: null
      });

      const activeSubscription = await mockStripe.subscriptions.retrieve(MOCK_SUBSCRIPTIONS.TRIALING);
      
      expect(activeSubscription.status).toBe('active');
      expect(activeSubscription.trial_end).toBeNull();
    });

    it('should handle failed payment at trial end', async () => {
      // Simulate payment failure at trial end
      const failedPaymentEvent = createMockWebhookEvent('invoice.payment_failed', {
        id: 'in_trial_end_failed',
        subscription: MOCK_SUBSCRIPTIONS.TRIALING,
        amount_due: 9900,
        currency: 'usd',
        payment_intent: {
          id: 'pi_trial_failed',
          status: 'requires_payment_method',
          last_payment_error: {
            code: 'card_declined',
            message: 'Your card was declined.'
          }
        }
      });

      expect(failedPaymentEvent.type).toBe('invoice.payment_failed');

      // Update subscription status to incomplete
      mockStripe.subscriptions.retrieve.mockResolvedValueOnce({
        id: MOCK_SUBSCRIPTIONS.TRIALING,
        status: 'incomplete',
        latest_invoice: 'in_trial_end_failed'
      });

      const incompleteSubscription = await mockStripe.subscriptions.retrieve(MOCK_SUBSCRIPTIONS.TRIALING);
      
      expect(incompleteSubscription.status).toBe('incomplete');
    });

    it('should extend trial period', async () => {
      // Extend trial by 7 days
      mockStripe.subscriptions.update.mockResolvedValueOnce({
        id: MOCK_SUBSCRIPTIONS.TRIALING,
        status: 'trialing',
        trial_end: Math.floor(Date.now() / 1000) + 21 * 24 * 60 * 60 // Extended to 21 days
      });

      const extendedSubscription = await mockStripe.subscriptions.update(
        MOCK_SUBSCRIPTIONS.TRIALING,
        {
          trial_end: Math.floor(Date.now() / 1000) + 21 * 24 * 60 * 60
        }
      );

      expect(extendedSubscription.status).toBe('trialing');
      expect(extendedSubscription.trial_end).toBeGreaterThan(
        Math.floor(Date.now() / 1000) + 20 * 24 * 60 * 60
      );
    });
  });

  describe('Subscription Modifications', () => {
    it('should upgrade subscription plan', async () => {
      // Current subscription on basic plan
      const currentSubscription = {
        id: MOCK_SUBSCRIPTIONS.ACTIVE,
        status: 'active',
        items: {
          data: [{
            id: 'si_basic',
            price: {
              id: 'price_basic_49',
              unit_amount: 4900,
              currency: 'usd'
            }
          }]
        }
      };

      // Upgrade to professional plan
      mockStripe.subscriptions.update.mockResolvedValueOnce({
        ...currentSubscription,
        items: {
          data: [{
            id: 'si_professional',
            price: {
              id: 'price_professional_99',
              unit_amount: 9900,
              currency: 'usd'
            }
          }]
        },
        proration_date: Math.floor(Date.now() / 1000)
      });

      const upgradedSubscription = await mockStripe.subscriptions.update(
        MOCK_SUBSCRIPTIONS.ACTIVE,
        {
          items: [{
            id: 'si_basic',
            price: 'price_professional_99'
          }],
          proration_behavior: 'create_prorations'
        }
      );

      expect(upgradedSubscription.items.data[0].price.unit_amount).toBe(9900);
      expect(upgradedSubscription.proration_date).toBeDefined();
    });

    it('should downgrade subscription plan', async () => {
      // Downgrade from professional to basic
      mockStripe.subscriptions.update.mockResolvedValueOnce({
        id: MOCK_SUBSCRIPTIONS.ACTIVE,
        status: 'active',
        items: {
          data: [{
            id: 'si_basic',
            price: {
              id: 'price_basic_49',
              unit_amount: 4900,
              currency: 'usd'
            }
          }]
        },
        proration_behavior: 'create_prorations'
      });

      const downgradedSubscription = await mockStripe.subscriptions.update(
        MOCK_SUBSCRIPTIONS.ACTIVE,
        {
          items: [{
            id: 'si_professional',
            price: 'price_basic_49'
          }],
          proration_behavior: 'create_prorations'
        }
      );

      expect(downgradedSubscription.items.data[0].price.unit_amount).toBe(4900);
    });

    it('should add subscription add-ons', async () => {
      // Add multiple add-ons to existing subscription
      mockStripe.subscriptions.update.mockResolvedValueOnce({
        id: MOCK_SUBSCRIPTIONS.ACTIVE,
        status: 'active',
        items: {
          data: [
            {
              id: 'si_base',
              price: {
                id: 'price_professional_99',
                unit_amount: 9900,
                currency: 'usd'
              }
            },
            {
              id: 'si_addon_storage',
              price: {
                id: 'price_addon_storage_20',
                unit_amount: 2000,
                currency: 'usd'
              }
            },
            {
              id: 'si_addon_users',
              price: {
                id: 'price_addon_users_30',
                unit_amount: 3000,
                currency: 'usd'
              },
              quantity: 5 // 5 additional users
            }
          ]
        }
      });

      const subscriptionWithAddons = await mockStripe.subscriptions.update(
        MOCK_SUBSCRIPTIONS.ACTIVE,
        {
          items: [
            { price: 'price_addon_storage_20' },
            { price: 'price_addon_users_30', quantity: 5 }
          ]
        }
      );

      expect(subscriptionWithAddons.items.data).toHaveLength(3);
      expect(subscriptionWithAddons.items.data[2].quantity).toBe(5);
    });

    it('should change billing cycle from monthly to annual', async () => {
      // Switch to annual billing
      mockStripe.subscriptions.update.mockResolvedValueOnce({
        id: MOCK_SUBSCRIPTIONS.ACTIVE,
        status: 'active',
        items: {
          data: [{
            id: 'si_annual',
            price: {
              id: 'price_professional_annual_999',
              unit_amount: 99900, // $999/year
              currency: 'usd',
              recurring: {
                interval: 'year'
              }
            }
          }]
        },
        current_period_end: Math.floor(Date.now() / 1000) + 365 * 24 * 60 * 60
      });

      const annualSubscription = await mockStripe.subscriptions.update(
        MOCK_SUBSCRIPTIONS.ACTIVE,
        {
          items: [{
            id: 'si_monthly',
            price: 'price_professional_annual_999'
          }],
          proration_behavior: 'create_prorations'
        }
      );

      expect(annualSubscription.items.data[0].price.recurring?.interval).toBe('year');
      expect(annualSubscription.items.data[0].price.unit_amount).toBe(99900);
    });
  });

  describe('Subscription Cancellation', () => {
    it('should cancel subscription immediately', async () => {
      const canceledSubscription = await mockStripe.subscriptions.cancel(
        MOCK_SUBSCRIPTIONS.ACTIVE
      );

      expect(canceledSubscription.id).toBe(MOCK_SUBSCRIPTIONS.ACTIVE);
      expect(canceledSubscription.status).toBe('canceled');
      expect(canceledSubscription.canceled_at).toBeDefined();
    });

    it('should cancel subscription at period end', async () => {
      mockStripe.subscriptions.update.mockResolvedValueOnce({
        id: MOCK_SUBSCRIPTIONS.ACTIVE,
        status: 'active',
        cancel_at_period_end: true,
        cancel_at: Math.floor(Date.now() / 1000) + 30 * 24 * 60 * 60,
        current_period_end: Math.floor(Date.now() / 1000) + 30 * 24 * 60 * 60
      });

      const scheduledCancellation = await mockStripe.subscriptions.update(
        MOCK_SUBSCRIPTIONS.ACTIVE,
        {
          cancel_at_period_end: true
        }
      );

      expect(scheduledCancellation.cancel_at_period_end).toBe(true);
      expect(scheduledCancellation.status).toBe('active'); // Still active until period end
      expect(scheduledCancellation.cancel_at).toBe(scheduledCancellation.current_period_end);
    });

    it('should reactivate canceled subscription before period end', async () => {
      // First schedule cancellation
      mockStripe.subscriptions.update.mockResolvedValueOnce({
        id: MOCK_SUBSCRIPTIONS.ACTIVE,
        status: 'active',
        cancel_at_period_end: true
      });

      await mockStripe.subscriptions.update(MOCK_SUBSCRIPTIONS.ACTIVE, {
        cancel_at_period_end: true
      });

      // Then reactivate
      mockStripe.subscriptions.update.mockResolvedValueOnce({
        id: MOCK_SUBSCRIPTIONS.ACTIVE,
        status: 'active',
        cancel_at_period_end: false,
        cancel_at: null
      });

      const reactivatedSubscription = await mockStripe.subscriptions.update(
        MOCK_SUBSCRIPTIONS.ACTIVE,
        {
          cancel_at_period_end: false
        }
      );

      expect(reactivatedSubscription.cancel_at_period_end).toBe(false);
      expect(reactivatedSubscription.cancel_at).toBeNull();
    });

    it('should handle refund on cancellation', async () => {
      // Cancel with prorated refund
      mockStripe.subscriptions.cancel.mockResolvedValueOnce({
        id: MOCK_SUBSCRIPTIONS.ACTIVE,
        status: 'canceled',
        canceled_at: Math.floor(Date.now() / 1000),
        latest_invoice: 'in_refunded'
      });

      // Create refund for unused time
      mockStripe.refunds = {
        create: vi.fn().mockResolvedValue({
          id: 're_subscription_prorate',
          amount: 4950, // Half month refund
          currency: 'usd',
          status: 'succeeded',
          metadata: {
            subscription_id: MOCK_SUBSCRIPTIONS.ACTIVE,
            reason: 'subscription_cancellation_prorate'
          }
        })
      };

      const canceledSubscription = await mockStripe.subscriptions.cancel(
        MOCK_SUBSCRIPTIONS.ACTIVE,
        { prorate: true }
      );

      const refund = await mockStripe.refunds.create({
        charge: 'ch_last_subscription_charge',
        amount: 4950,
        metadata: {
          subscription_id: MOCK_SUBSCRIPTIONS.ACTIVE,
          reason: 'subscription_cancellation_prorate'
        }
      });

      expect(canceledSubscription.status).toBe('canceled');
      expect(refund.amount).toBe(4950);
      expect(refund.status).toBe('succeeded');
    });
  });

  describe('Payment Failure Handling', () => {
    it('should handle failed subscription payment', async () => {
      // Simulate failed payment webhook
      const failedPaymentEvent = createMockWebhookEvent('invoice.payment_failed', {
        id: 'in_failed',
        subscription: MOCK_SUBSCRIPTIONS.ACTIVE,
        amount_due: 9900,
        attempt_count: 1,
        next_payment_attempt: Math.floor(Date.now() / 1000) + 3 * 24 * 60 * 60
      });

      // Update subscription to past_due
      mockStripe.subscriptions.retrieve.mockResolvedValueOnce({
        id: MOCK_SUBSCRIPTIONS.ACTIVE,
        status: 'past_due',
        latest_invoice: 'in_failed'
      });

      const pastDueSubscription = await mockStripe.subscriptions.retrieve(MOCK_SUBSCRIPTIONS.ACTIVE);
      
      expect(pastDueSubscription.status).toBe('past_due');
    });

    it('should retry failed payments with smart retries', async () => {
      const retrySchedule = [
        { attempt: 1, days_after: 3 },
        { attempt: 2, days_after: 5 },
        { attempt: 3, days_after: 7 }
      ];

      for (const retry of retrySchedule) {
        const retryEvent = createMockWebhookEvent('invoice.payment_action_required', {
          id: `in_retry_${retry.attempt}`,
          subscription: MOCK_SUBSCRIPTIONS.PAST_DUE,
          amount_due: 9900,
          attempt_count: retry.attempt,
          next_payment_attempt: Math.floor(Date.now() / 1000) + retry.days_after * 24 * 60 * 60
        });

        expect(retryEvent.data.object.attempt_count).toBe(retry.attempt);
      }
    });

    it('should cancel subscription after max payment failures', async () => {
      // Simulate final failed payment attempt
      const finalFailureEvent = createMockWebhookEvent('customer.subscription.deleted', {
        id: MOCK_SUBSCRIPTIONS.PAST_DUE,
        status: 'canceled',
        cancellation_details: {
          reason: 'payment_failed',
          comment: 'Subscription canceled due to failed payment after 3 attempts'
        }
      });

      expect(finalFailureEvent.type).toBe('customer.subscription.deleted');
      expect(finalFailureEvent.data.object.cancellation_details.reason).toBe('payment_failed');
    });

    it('should update payment method and retry', async () => {
      // Update payment method for past_due subscription
      const updatedPaymentMethod = await mockStripe.paymentMethods.attach(
        MOCK_PAYMENT_METHODS.VALID_CARD,
        { customer: MOCK_CUSTOMERS.VALID }
      );

      // Set as default payment method
      mockStripe.customers.update.mockResolvedValueOnce({
        id: MOCK_CUSTOMERS.VALID,
        invoice_settings: {
          default_payment_method: MOCK_PAYMENT_METHODS.VALID_CARD
        }
      });

      await mockStripe.customers.update(MOCK_CUSTOMERS.VALID, {
        invoice_settings: {
          default_payment_method: MOCK_PAYMENT_METHODS.VALID_CARD
        }
      });

      // Retry payment with new method
      mockStripe.invoices = {
        pay: vi.fn().mockResolvedValue({
          id: 'in_retry_success',
          status: 'paid',
          payment_intent: 'pi_retry_success'
        })
      };

      const paidInvoice = await mockStripe.invoices.pay('in_failed');
      
      expect(paidInvoice.status).toBe('paid');

      // Subscription returns to active
      mockStripe.subscriptions.retrieve.mockResolvedValueOnce({
        id: MOCK_SUBSCRIPTIONS.PAST_DUE,
        status: 'active'
      });

      const activeSubscription = await mockStripe.subscriptions.retrieve(MOCK_SUBSCRIPTIONS.PAST_DUE);
      expect(activeSubscription.status).toBe('active');
    });
  });

  describe('Subscription Webhooks', () => {
    it('should handle subscription created webhook', async () => {
      const createdEvent = createMockWebhookEvent('customer.subscription.created', {
        id: 'sub_new',
        customer: MOCK_CUSTOMERS.VALID,
        status: 'active',
        items: {
          data: [{
            price: {
              id: 'price_starter_29',
              unit_amount: 2900,
              currency: 'usd'
            }
          }]
        },
        metadata: {
          tenant_id: 'tenant-123',
          created_by: 'signup_flow'
        }
      });

      expect(createdEvent.type).toBe('customer.subscription.created');
      expect(createdEvent.data.object.metadata.created_by).toBe('signup_flow');
    });

    it('should handle subscription updated webhook', async () => {
      const updatedEvent = createMockWebhookEvent('customer.subscription.updated', {
        id: MOCK_SUBSCRIPTIONS.ACTIVE,
        previous_attributes: {
          items: {
            data: [{
              price: { id: 'price_basic_49' }
            }]
          }
        },
        items: {
          data: [{
            price: { id: 'price_professional_99' }
          }]
        }
      });

      expect(updatedEvent.type).toBe('customer.subscription.updated');
      expect(updatedEvent.data.previous_attributes.items.data[0].price.id).toBe('price_basic_49');
    });

    it('should handle subscription paused webhook', async () => {
      const pausedEvent = createMockWebhookEvent('customer.subscription.paused', {
        id: MOCK_SUBSCRIPTIONS.ACTIVE,
        status: 'paused',
        pause_collection: {
          behavior: 'void',
          resumes_at: Math.floor(Date.now() / 1000) + 30 * 24 * 60 * 60
        }
      });

      expect(pausedEvent.type).toBe('customer.subscription.paused');
      expect(pausedEvent.data.object.pause_collection.behavior).toBe('void');
    });

    it('should handle subscription resumed webhook', async () => {
      const resumedEvent = createMockWebhookEvent('customer.subscription.resumed', {
        id: MOCK_SUBSCRIPTIONS.ACTIVE,
        status: 'active',
        pause_collection: null
      });

      expect(resumedEvent.type).toBe('customer.subscription.resumed');
      expect(resumedEvent.data.object.pause_collection).toBeNull();
    });
  });
});