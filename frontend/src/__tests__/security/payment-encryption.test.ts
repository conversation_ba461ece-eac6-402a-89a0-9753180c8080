/**
 * Payment Data Encryption Tests
 *
 * Tests for the payment data encryption and decryption functionality
 */

// Mock environment variables before any imports using hoisted
vi.hoisted(() => {
  process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://anwefmklplkjxkmzpnva.supabase.co';
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-anon-key';
  process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-service-key';
  process.env.SENTRY_DSN = '';
  process.env.NODE_ENV = 'test';
});

// Mock Sentry to prevent module resolution issues
vi.mock('@sentry/nextjs', () => ({
  withSentryConfig: vi.fn((config) => config),
  captureException: vi.fn(),
  captureMessage: vi.fn(),
  withSentry: vi.fn((handler) => handler),
}));

import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  encryptPaymentData,
  decryptPaymentData,
  encryptPaymentFields,
  decryptPaymentFields,
  hashPaymentData,
  generateSecureToken,
  validateEncryptionConfig,
  generateEncryptionKey
} from '@/lib/security/payment-encryption';

describe('Payment Data Encryption', () => {
  beforeEach(() => {
    // Set up test environment variables
    process.env.PAYMENT_ENCRYPTION_KEY = generateEncryptionKey();
    process.env.PAYMENT_MASTER_KEY = 'test-master-key-for-field-encryption-minimum-32-chars';
  });

  describe('Basic Encryption/Decryption', () => {
    it('should encrypt and decrypt simple text data', () => {
      const plaintext = 'sensitive payment data';
      
      const encryptResult = encryptPaymentData(plaintext, false);
      expect(encryptResult.success).toBe(true);
      expect(encryptResult.data).toBeDefined();
      expect(encryptResult.data!.encrypted).toBeDefined();
      expect(encryptResult.data!.iv).toBeDefined();
      expect(encryptResult.data!.tag).toBeDefined();
      
      const decryptResult = decryptPaymentData(encryptResult.data!, false);
      expect(decryptResult.success).toBe(true);
      expect(decryptResult.data).toBe(plaintext);
    });

    it('should encrypt and decrypt with field-level encryption', () => {
      const plaintext = 'field-level encrypted data';
      
      const encryptResult = encryptPaymentData(plaintext, true);
      expect(encryptResult.success).toBe(true);
      expect(encryptResult.data).toBeDefined();
      expect(encryptResult.data!.salt).toBeDefined(); // Should have salt for field encryption
      
      const decryptResult = decryptPaymentData(encryptResult.data!, true);
      expect(decryptResult.success).toBe(true);
      expect(decryptResult.data).toBe(plaintext);
    });

    it('should fail to decrypt with wrong encryption type', () => {
      const plaintext = 'test data';
      
      const encryptResult = encryptPaymentData(plaintext, true);
      expect(encryptResult.success).toBe(true);
      
      // Try to decrypt field encryption with transport encryption
      const decryptResult = decryptPaymentData(encryptResult.data!, false);
      expect(decryptResult.success).toBe(false);
      expect(decryptResult.error).toContain('Decryption failed');
    });
  });

  describe('Field-Level Encryption', () => {
    it('should encrypt sensitive payment fields', () => {
      const paymentData = {
        card_number: '****************',
        cvv: '123',
        cardholder_name: 'John Doe',
        billing_address: '123 Main St',
        email: '<EMAIL>',
        non_sensitive_field: 'this should not be encrypted'
      };

      const { encrypted, errors } = encryptPaymentFields(paymentData);
      
      expect(errors).toHaveLength(0);
      expect(encrypted.card_number).not.toBe(paymentData.card_number);
      expect(encrypted.cvv).not.toBe(paymentData.cvv);
      expect(encrypted.cardholder_name).not.toBe(paymentData.cardholder_name);
      expect(encrypted.billing_address).not.toBe(paymentData.billing_address);
      expect(encrypted.email).not.toBe(paymentData.email);
      expect(encrypted.non_sensitive_field).toBe(paymentData.non_sensitive_field);
      
      // Encrypted fields should be objects with encryption data
      expect(typeof encrypted.card_number).toBe('object');
      expect(typeof encrypted.cvv).toBe('object');
    });

    it('should decrypt sensitive payment fields', () => {
      const paymentData = {
        iban: '**********************',
        account_holder_name: 'Jane Smith',
        phone: '+**********',
        regular_field: 'not encrypted'
      };

      const { encrypted, errors: encryptErrors } = encryptPaymentFields(paymentData);
      expect(encryptErrors).toHaveLength(0);

      const { decrypted, errors: decryptErrors } = decryptPaymentFields(encrypted);
      expect(decryptErrors).toHaveLength(0);
      
      expect(decrypted.iban).toBe(paymentData.iban);
      expect(decrypted.account_holder_name).toBe(paymentData.account_holder_name);
      expect(decrypted.phone).toBe(paymentData.phone);
      expect(decrypted.regular_field).toBe(paymentData.regular_field);
    });

    it('should handle empty or invalid data gracefully', () => {
      const { encrypted, errors } = encryptPaymentFields({});
      expect(errors).toHaveLength(0);
      expect(encrypted).toEqual({});

      const { decrypted, errors: decryptErrors } = decryptPaymentFields({});
      expect(decryptErrors).toHaveLength(0);
      expect(decrypted).toEqual({});
    });
  });

  describe('Data Integrity', () => {
    it('should generate consistent hashes for same data', () => {
      const data = 'test payment data';
      const hash1 = hashPaymentData(data);
      const hash2 = hashPaymentData(data);
      
      expect(hash1).toBe(hash2);
      expect(hash1).toHaveLength(64); // SHA-256 hex string
    });

    it('should generate different hashes for different data', () => {
      const data1 = 'test payment data 1';
      const data2 = 'test payment data 2';
      
      const hash1 = hashPaymentData(data1);
      const hash2 = hashPaymentData(data2);
      
      expect(hash1).not.toBe(hash2);
    });

    it('should detect data tampering', () => {
      const plaintext = 'original data';
      const encryptResult = encryptPaymentData(plaintext);
      expect(encryptResult.success).toBe(true);

      // Tamper with encrypted data
      const tamperedData = {
        ...encryptResult.data!,
        encrypted: encryptResult.data!.encrypted.slice(0, -2) + 'XX'
      };

      const decryptResult = decryptPaymentData(tamperedData);
      expect(decryptResult.success).toBe(false);
      expect(decryptResult.error).toContain('Decryption failed');
    });
  });

  describe('Security Utilities', () => {
    it('should generate secure random tokens', () => {
      const token1 = generateSecureToken(32);
      const token2 = generateSecureToken(32);
      
      expect(token1).toHaveLength(64); // 32 bytes = 64 hex chars
      expect(token2).toHaveLength(64);
      expect(token1).not.toBe(token2);
      expect(/^[0-9a-f]+$/.test(token1)).toBe(true); // Valid hex
    });

    it('should generate tokens of specified length', () => {
      const token16 = generateSecureToken(16);
      const token64 = generateSecureToken(64);
      
      expect(token16).toHaveLength(32); // 16 bytes = 32 hex chars
      expect(token64).toHaveLength(128); // 64 bytes = 128 hex chars
    });

    it('should generate valid encryption keys', () => {
      const key = generateEncryptionKey();
      
      expect(key).toHaveLength(64); // 32 bytes = 64 hex chars
      expect(/^[0-9a-f]+$/.test(key)).toBe(true); // Valid hex
    });
  });

  describe('Configuration Validation', () => {
    it('should validate correct encryption configuration', () => {
      const result = validateEncryptionConfig();
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect missing encryption keys', () => {
      delete process.env.PAYMENT_ENCRYPTION_KEY;
      delete process.env.PAYMENT_MASTER_KEY;
      
      const result = validateEncryptionConfig();
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('No encryption key configured (PAYMENT_ENCRYPTION_KEY or PAYMENT_MASTER_KEY)');
    });

    it('should detect invalid encryption key length', () => {
      process.env.PAYMENT_ENCRYPTION_KEY = 'too_short';
      
      const result = validateEncryptionConfig();
      expect(result.valid).toBe(false);
      expect(result.errors.some(e => e.includes('must be 64 hex characters'))).toBe(true);
    });

    it('should detect invalid master key length', () => {
      process.env.PAYMENT_MASTER_KEY = 'short';
      
      const result = validateEncryptionConfig();
      expect(result.valid).toBe(false);
      expect(result.errors.some(e => e.includes('must be at least 32 characters'))).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle encryption errors gracefully', () => {
      const result = encryptPaymentData('');
      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid input data');
    });

    it('should handle decryption errors gracefully', () => {
      const invalidData = {
        encrypted: 'invalid',
        iv: 'invalid',
        tag: 'invalid'
      };
      
      const result = decryptPaymentData(invalidData);
      expect(result.success).toBe(false);
      expect(result.error).toContain('Decryption failed');
    });

    it('should handle missing encryption configuration', () => {
      delete process.env.PAYMENT_ENCRYPTION_KEY;
      delete process.env.PAYMENT_MASTER_KEY;
      
      const result = encryptPaymentData('test data');
      expect(result.success).toBe(false);
      expect(result.error).toContain('Encryption key not configured');
    });
  });
});
