/**
 * Regional Payment Methods Comprehensive Test Suite
 * 
 * Tests for the complete regional payment methods system including
 * database schema, services, API routes, validation, and UI components.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { RegionalPaymentMethodService } from '@/lib/services/regional-payment-method-service';
import { StripePaymentMethodService } from '@/lib/services/stripe-payment-method-service';
import { paymentMethodValidationService } from '@/lib/services/payment-method-validation-service';
import {
  PaymentMethodCode,
  SupportedRegion,
  SupportedCurrency,
  PaymentMethodValidationRequest,
  PaymentMethodSelectionRequest,
  StripePaymentMethodCreationRequest
} from '@/lib/types/payment-methods';

// Mock Supabase client
const mockSupabase = {
  from: vi.fn(() => ({
    select: vi.fn(() => ({
      eq: vi.fn(() => ({
        eq: vi.fn(() => ({
          eq: vi.fn(() => ({
            order: vi.fn(() => Promise.resolve({ data: [], error: null }))
          }))
        }))
      }))
    })),
    insert: vi.fn(() => Promise.resolve({ data: [], error: null })),
    update: vi.fn(() => ({
      eq: vi.fn(() => ({
        eq: vi.fn(() => ({
          eq: vi.fn(() => ({
            select: vi.fn(() => ({
              single: vi.fn(() => Promise.resolve({ data: {}, error: null }))
            }))
          }))
        }))
      }))
    })),
    upsert: vi.fn(() => Promise.resolve({ data: [], error: null }))
  }))
};

// Mock Stripe client
const mockStripe = {
  paymentMethods: {
    create: vi.fn(() => Promise.resolve({ id: 'pm_test_123' }))
  },
  paymentIntents: {
    create: vi.fn(() => Promise.resolve({ 
      id: 'pi_test_123',
      status: 'succeeded',
      client_secret: 'pi_test_123_secret'
    }))
  }
};

describe('Regional Payment Methods System', () => {
  let regionalService: RegionalPaymentMethodService;
  let stripeService: StripePaymentMethodService;

  beforeEach(() => {
    vi.clearAllMocks();
    regionalService = new RegionalPaymentMethodService();
    stripeService = new StripePaymentMethodService();
    
    // Mock the Supabase client
    (regionalService as any)._supabase = mockSupabase;
    (stripeService as any).stripe = mockStripe;
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Database Schema Tests', () => {
    it('should have correct payment method types structure', async () => {
      const mockData = [
        {
          id: '1',
          code: 'card',
          name: 'Credit/Debit Card',
          description: 'Visa, Mastercard, American Express',
          icon_name: 'credit-card',
          requires_bank_details: false,
          supports_recurring: true,
          processing_time_days: 0,
          is_active: true
        }
      ];

      mockSupabase.from.mockReturnValue({
        select: vi.fn(() => Promise.resolve({ data: mockData, error: null }))
      });

      const result = await regionalService.getPaymentMethodTypes();
      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        code: 'card',
        name: 'Credit/Debit Card',
        supports_recurring: true
      });
    });

    it('should have correct regional payment methods structure', async () => {
      const mockData = [
        {
          id: '1',
          payment_method_type_id: '1',
          country_code: 'US',
          currency_code: 'USD',
          is_primary: true,
          is_available: true,
          min_amount_cents: 50,
          max_amount_cents: null,
          processing_fee_percentage: 2.9,
          processing_fee_fixed_cents: 30,
          stripe_configuration: { payment_method_types: ['card'] }
        }
      ];

      mockSupabase.from.mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            eq: vi.fn(() => ({
              eq: vi.fn(() => ({
                order: vi.fn(() => Promise.resolve({ data: mockData, error: null }))
              }))
            }))
          }))
        }))
      });

      const result = await regionalService.getAvailablePaymentMethods('US', 'USD');
      expect(mockSupabase.from).toHaveBeenCalledWith('regional_payment_methods');
    });

    it('should have correct validation rules structure', async () => {
      const mockData = [
        {
          id: '1',
          payment_method_type_id: '1',
          country_code: 'US',
          validation_type: 'format',
          field_name: 'number',
          validation_pattern: '^[0-9]{13,19}$',
          error_message: 'Invalid card number format',
          is_required: true
        }
      ];

      mockSupabase.from.mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            eq: vi.fn(() => Promise.resolve({ data: mockData, error: null }))
          }))
        }))
      });

      const result = await regionalService.getValidationRules('1', 'US');
      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        validation_type: 'format',
        field_name: 'number'
      });
    });
  });

  describe('Regional Payment Method Service Tests', () => {
    it('should get available payment methods for US', async () => {
      const mockData = [
        {
          id: '1',
          payment_method_type_id: '1',
          country_code: 'US',
          currency_code: 'USD',
          is_primary: true,
          is_available: true,
          min_amount_cents: 50,
          processing_fee_percentage: 2.9,
          payment_method_type: {
            code: 'card',
            name: 'Credit/Debit Card'
          }
        }
      ];

      mockSupabase.from.mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            eq: vi.fn(() => ({
              eq: vi.fn(() => ({
                order: vi.fn(() => Promise.resolve({ data: mockData, error: null }))
              }))
            }))
          }))
        }))
      });

      const result = await regionalService.getAvailablePaymentMethods('US', 'USD');
      expect(result).toBeDefined();
    });

    it('should get available payment methods for Belgium (SEPA)', async () => {
      const mockData = [
        {
          id: '2',
          payment_method_type_id: '3',
          country_code: 'BE',
          currency_code: 'EUR',
          is_primary: true,
          is_available: true,
          min_amount_cents: 100,
          payment_method_type: {
            code: 'sepa_debit',
            name: 'SEPA Direct Debit'
          }
        }
      ];

      mockSupabase.from.mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            eq: vi.fn(() => ({
              eq: vi.fn(() => ({
                order: vi.fn(() => Promise.resolve({ data: mockData, error: null }))
              }))
            }))
          }))
        }))
      });

      const result = await regionalService.getAvailablePaymentMethods('BE', 'EUR');
      expect(result).toBeDefined();
    });

    it('should filter payment methods by amount', async () => {
      const mockData = [
        {
          id: '1',
          min_amount_cents: 50,
          max_amount_cents: 100000,
          payment_method_type: { code: 'card' }
        },
        {
          id: '2',
          min_amount_cents: 500,
          max_amount_cents: null,
          payment_method_type: { code: 'sepa_debit' }
        }
      ];

      mockSupabase.from.mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            eq: vi.fn(() => ({
              eq: vi.fn(() => ({
                order: vi.fn(() => Promise.resolve({ data: mockData, error: null }))
              }))
            }))
          }))
        }))
      });

      // Mock validation rules
      regionalService.getValidationRules = vi.fn(() => Promise.resolve([]));

      const result = await regionalService.getAvailablePaymentMethods('US', 'USD', 200);
      expect(regionalService.getValidationRules).toHaveBeenCalled();
    });

    it('should recommend payment methods based on preferences', async () => {
      const request: PaymentMethodSelectionRequest = {
        country_code: 'US',
        currency_code: 'USD',
        amount_cents: 1000,
        customer_id: 'cust_123',
        preferred_types: ['card']
      };

      // Mock customer preferences
      mockSupabase.from.mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            single: vi.fn(() => Promise.resolve({
              data: {
                preferred_payment_method_type_id: '1',
                backup_payment_method_type_id: '2'
              },
              error: null
            }))
          }))
        }))
      });

      const result = await regionalService.recommendPaymentMethods(request);
      expect(result).toBeDefined();
    });

    it('should save customer payment preferences', async () => {
      const preferences = {
        customer_id: 'cust_123',
        country_code: 'US' as SupportedRegion,
        preferred_payment_method_type_id: '1',
        backup_payment_method_type_id: '2'
      };

      await regionalService.saveCustomerPreferences(preferences);
      expect(mockSupabase.from).toHaveBeenCalledWith('customer_payment_preferences');
    });
  });

  describe('Payment Method Validation Tests', () => {
    it('should validate credit card numbers with Luhn algorithm', async () => {
      const request: PaymentMethodValidationRequest = {
        payment_method_code: 'card',
        country_code: 'US',
        payment_data: {
          number: '****************',
          exp_month: '12',
          exp_year: '2025',
          cvc: '123'
        }
      };

      const result = await paymentMethodValidationService.validatePaymentMethod(request);
      expect(result.is_valid).toBe(true);
    });

    it('should validate IBAN for SEPA payments', async () => {
      const request: PaymentMethodValidationRequest = {
        payment_method_code: 'sepa_debit',
        country_code: 'BE',
        payment_data: {
          iban: '****************'
        }
      };

      const result = await paymentMethodValidationService.validatePaymentMethod(request);
      expect(result.is_valid).toBe(true);
    });

    it('should validate US ACH routing numbers', async () => {
      const request: PaymentMethodValidationRequest = {
        payment_method_code: 'ach_debit',
        country_code: 'US',
        payment_data: {
          routing_number: '*********',
          account_number: '************'
        }
      };

      const result = await paymentMethodValidationService.validatePaymentMethod(request);
      expect(result.is_valid).toBe(true);
    });

    it('should reject invalid payment data', async () => {
      const request: PaymentMethodValidationRequest = {
        payment_method_code: 'card',
        country_code: 'US',
        payment_data: {
          number: '****************', // Invalid Luhn
          exp_month: '13', // Invalid month
          exp_year: '2020', // Expired
          cvc: '12' // Too short
        }
      };

      const result = await paymentMethodValidationService.validatePaymentMethod(request);
      expect(result.is_valid).toBe(false);
      expect(result.errors).toHaveLength(4);
    });

    it('should format payment data correctly', async () => {
      const request: PaymentMethodValidationRequest = {
        payment_method_code: 'card',
        country_code: 'US',
        payment_data: {
          number: '4242 4242 4242 4242', // With spaces
          exp_month: '12',
          exp_year: '25', // 2-digit year
          cvc: '123'
        }
      };

      const result = await paymentMethodValidationService.validatePaymentMethod(request);
      expect(result.formatted_data?.number).toBe('****************');
      expect(result.formatted_data?.exp_year).toBe('2025');
    });
  });

  describe('Stripe Payment Method Service Tests', () => {
    it('should create Stripe payment method for cards', async () => {
      const request: StripePaymentMethodCreationRequest = {
        payment_method_code: 'card',
        country_code: 'US',
        currency_code: 'USD',
        tenant_id: 'tenant_123',
        payment_data: {
          number: '****************',
          exp_month: '12',
          exp_year: '2025',
          cvc: '123'
        }
      };

      const result = await stripeService.createPaymentMethod(request);
      expect(result.stripe_payment_method_id).toBe('pm_test_123');
      expect(result.status).toBe('succeeded');
      expect(mockStripe.paymentMethods.create).toHaveBeenCalled();
    });

    it('should process payment with created payment method', async () => {
      const result = await stripeService.processPayment(
        'pm_test_123',
        1000,
        'USD',
        'tenant_123'
      );

      expect(result.status).toBe('succeeded');
      expect(mockStripe.paymentIntents.create).toHaveBeenCalledWith(
        expect.objectContaining({
          amount: 1000,
          currency: 'usd',
          payment_method: 'pm_test_123'
        }),
        expect.any(Object)
      );
    });

    it('should handle payment method creation errors', async () => {
      mockStripe.paymentMethods.create.mockRejectedValue(
        new Error('Invalid card number')
      );

      const request: StripePaymentMethodCreationRequest = {
        payment_method_code: 'card',
        country_code: 'US',
        currency_code: 'USD',
        tenant_id: 'tenant_123',
        payment_data: {
          number: '****************'
        }
      };

      await expect(stripeService.createPaymentMethod(request))
        .rejects.toThrow('Invalid card number');
    });
  });

  describe('Multi-Country Support Tests', () => {
    const countries: Array<{ code: SupportedRegion; currency: SupportedCurrency; methods: PaymentMethodCode[] }> = [
      { code: 'US', currency: 'USD', methods: ['card', 'ach_debit'] },
      { code: 'BE', currency: 'EUR', methods: ['card', 'sepa_debit', 'bancontact'] },
      { code: 'GB', currency: 'GBP', methods: ['card', 'bacs_debit'] },
      { code: 'CA', currency: 'CAD', methods: ['card', 'acss_debit'] }
    ];

    countries.forEach(({ code, currency, methods }) => {
      it(`should support payment methods for ${code}`, async () => {
        const mockData = methods.map((method, index) => ({
          id: `${index + 1}`,
          payment_method_type_id: `${index + 1}`,
          country_code: code,
          currency_code: currency,
          is_available: true,
          payment_method_type: {
            code: method,
            name: method.replace('_', ' ').toUpperCase()
          }
        }));

        mockSupabase.from.mockReturnValue({
          select: vi.fn(() => ({
            eq: vi.fn(() => ({
              eq: vi.fn(() => ({
                eq: vi.fn(() => ({
                  order: vi.fn(() => Promise.resolve({ data: mockData, error: null }))
                }))
              }))
            }))
          }))
        });

        const result = await regionalService.getAvailablePaymentMethods(code, currency);
        expect(result).toBeDefined();
      });
    });

    it('should handle currency conversion for processing fees', async () => {
      const fees = await regionalService.calculateProcessingFees(
        'card',
        'US',
        'USD',
        1000
      );

      expect(fees).toMatchObject({
        percentage_fee: expect.any(Number),
        fixed_fee_cents: expect.any(Number),
        total_fee_cents: expect.any(Number)
      });
    });
  });

  describe('Error Handling Tests', () => {
    it('should handle database connection errors', async () => {
      mockSupabase.from.mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            eq: vi.fn(() => ({
              eq: vi.fn(() => ({
                order: vi.fn(() => Promise.resolve({ 
                  data: null, 
                  error: { message: 'Connection failed' }
                }))
              }))
            }))
          }))
        }))
      });

      await expect(regionalService.getAvailablePaymentMethods('US', 'USD'))
        .rejects.toThrow('Failed to fetch payment methods');
    });

    it('should handle invalid country codes', async () => {
      await expect(regionalService.getAvailablePaymentMethods('XX' as SupportedRegion, 'USD'))
        .rejects.toThrow();
    });

    it('should handle invalid currency codes', async () => {
      await expect(regionalService.getAvailablePaymentMethods('US', 'XXX' as SupportedCurrency))
        .rejects.toThrow();
    });

    it('should handle Stripe API errors gracefully', async () => {
      mockStripe.paymentMethods.create.mockRejectedValue({
        type: 'StripeCardError',
        code: 'card_declined',
        message: 'Your card was declined.'
      });

      const request: StripePaymentMethodCreationRequest = {
        payment_method_code: 'card',
        country_code: 'US',
        currency_code: 'USD',
        tenant_id: 'tenant_123',
        payment_data: {
          number: '****************' // Declined card
        }
      };

      await expect(stripeService.createPaymentMethod(request))
        .rejects.toThrow('Your card was declined.');
    });
  });

  describe('Performance Tests', () => {
    it('should handle concurrent payment method requests', async () => {
      const requests = Array.from({ length: 10 }, (_, i) =>
        regionalService.getAvailablePaymentMethods('US', 'USD')
      );

      const results = await Promise.all(requests);
      expect(results).toHaveLength(10);
    });

    it('should cache validation rules for performance', async () => {
      // First call
      await regionalService.getValidationRules('1', 'US');
      // Second call should use cache
      await regionalService.getValidationRules('1', 'US');

      // Should only call database once due to caching
      expect(mockSupabase.from).toHaveBeenCalledTimes(1);
    });
  });

  describe('Integration Tests', () => {
    it('should complete full payment flow for US customer', async () => {
      // 1. Get available payment methods
      const availableMethods = await regionalService.getAvailablePaymentMethods('US', 'USD');
      expect(availableMethods).toBeDefined();

      // 2. Validate payment data
      const validationRequest: PaymentMethodValidationRequest = {
        payment_method_code: 'card',
        country_code: 'US',
        payment_data: {
          number: '****************',
          exp_month: '12',
          exp_year: '2025',
          cvc: '123'
        }
      };

      const validation = await paymentMethodValidationService.validatePaymentMethod(validationRequest);
      expect(validation.is_valid).toBe(true);

      // 3. Create Stripe payment method
      const creationRequest: StripePaymentMethodCreationRequest = {
        payment_method_code: 'card',
        country_code: 'US',
        currency_code: 'USD',
        tenant_id: 'tenant_123',
        payment_data: validation.formatted_data || validationRequest.payment_data
      };

      const paymentMethod = await stripeService.createPaymentMethod(creationRequest);
      expect(paymentMethod.stripe_payment_method_id).toBeDefined();

      // 4. Process payment
      const payment = await stripeService.processPayment(
        paymentMethod.stripe_payment_method_id,
        1000,
        'USD',
        'tenant_123'
      );

      expect(payment.status).toBe('succeeded');
    });

    it('should complete full payment flow for EU customer with SEPA', async () => {
      // Mock SEPA-specific data
      mockSupabase.from.mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            eq: vi.fn(() => ({
              eq: vi.fn(() => ({
                order: vi.fn(() => Promise.resolve({
                  data: [{
                    id: '1',
                    payment_method_type: { code: 'sepa_debit' },
                    country_code: 'BE',
                    currency_code: 'EUR'
                  }],
                  error: null
                }))
              }))
            }))
          }))
        }))
      });

      // 1. Get available payment methods for Belgium
      const availableMethods = await regionalService.getAvailablePaymentMethods('BE', 'EUR');
      expect(availableMethods).toBeDefined();

      // 2. Validate IBAN
      const validationRequest: PaymentMethodValidationRequest = {
        payment_method_code: 'sepa_debit',
        country_code: 'BE',
        payment_data: {
          iban: '****************'
        }
      };

      const validation = await paymentMethodValidationService.validatePaymentMethod(validationRequest);
      expect(validation.is_valid).toBe(true);

      // 3. Create Stripe payment method
      const creationRequest: StripePaymentMethodCreationRequest = {
        payment_method_code: 'sepa_debit',
        country_code: 'BE',
        currency_code: 'EUR',
        tenant_id: 'tenant_123',
        payment_data: validation.formatted_data || validationRequest.payment_data
      };

      const paymentMethod = await stripeService.createPaymentMethod(creationRequest);
      expect(paymentMethod.stripe_payment_method_id).toBeDefined();
    });
  });

  describe('SEPA Direct Debit Tests', () => {
    it('should validate IBAN with MOD-97 checksum', async () => {
      const validIBANs = [
        '****************',
        '**********************',
        '***************************',
        '******************'
      ];

      for (const iban of validIBANs) {
        const request: PaymentMethodValidationRequest = {
          payment_method_code: 'sepa_debit',
          country_code: 'BE',
          payment_data: { iban }
        };

        const result = await paymentMethodValidationService.validatePaymentMethod(request);
        expect(result.is_valid).toBe(true);
      }
    });

    it('should reject invalid IBANs', async () => {
      const invalidIBANs = [
        '****************', // Wrong checksum
        '**********************', // Wrong checksum
        'XX****************', // Invalid country code
        'BE123', // Too short
        '********************************78901234567890' // Too long
      ];

      for (const iban of invalidIBANs) {
        const request: PaymentMethodValidationRequest = {
          payment_method_code: 'sepa_debit',
          country_code: 'BE',
          payment_data: { iban }
        };

        const result = await paymentMethodValidationService.validatePaymentMethod(request);
        expect(result.is_valid).toBe(false);
      }
    });

    it('should format IBAN correctly', async () => {
      const request: PaymentMethodValidationRequest = {
        payment_method_code: 'sepa_debit',
        country_code: 'BE',
        payment_data: {
          iban: 'be68 5390 0754 7034' // Lowercase with spaces
        }
      };

      const result = await paymentMethodValidationService.validatePaymentMethod(request);
      expect(result.is_valid).toBe(true);
      expect(result.formatted_data?.iban).toBe('****************');
    });

    it('should validate BIC format', async () => {
      const validBICs = [
        'DEUTDEFF',
        'DEUTDEFF500',
        'BNPAFRPP',
        'ABNANL2A'
      ];

      for (const bic of validBICs) {
        const request: PaymentMethodValidationRequest = {
          payment_method_code: 'sepa_debit',
          country_code: 'DE',
          payment_data: {
            iban: '**********************',
            bic
          }
        };

        const result = await paymentMethodValidationService.validatePaymentMethod(request);
        expect(result.is_valid).toBe(true);
      }
    });

    it('should reject invalid BIC format', async () => {
      const invalidBICs = [
        'DEUT', // Too short
        'DEUTDEFF5001', // Too long
        'deut1234', // Lowercase
        '12345678' // All numbers
      ];

      for (const bic of invalidBICs) {
        const request: PaymentMethodValidationRequest = {
          payment_method_code: 'sepa_debit',
          country_code: 'DE',
          payment_data: {
            iban: '**********************',
            bic
          }
        };

        const result = await paymentMethodValidationService.validatePaymentMethod(request);
        expect(result.is_valid).toBe(false);
      }
    });

    it('should handle SEPA mandate creation', async () => {
      const mockSEPAService = {
        createSEPAPaymentMethod: vi.fn().mockResolvedValue({
          stripe_payment_method_id: 'pm_sepa_123',
          mandate_id: 'mandate_123',
          mandate_reference: 'SEPA-TENANT123-ABC123',
          mandate_url: 'https://stripe.com/mandate/123',
          status: 'succeeded'
        })
      };

      const sepaRequest = {
        iban: '****************',
        account_holder_name: 'John Doe',
        customer_email: '<EMAIL>',
        customer_name: 'John Doe',
        country_code: 'BE' as const,
        currency_code: 'EUR' as const,
        tenant_id: 'tenant_123',
        mandate_acceptance_type: 'online' as const
      };

      const result = await mockSEPAService.createSEPAPaymentMethod(sepaRequest);
      expect(result.stripe_payment_method_id).toBe('pm_sepa_123');
      expect(result.mandate_id).toBe('mandate_123');
      expect(result.status).toBe('succeeded');
    });

    it('should process SEPA payment with proper validation', async () => {
      const mockSEPAService = {
        processSEPAPayment: vi.fn().mockResolvedValue({
          payment_intent_id: 'pi_sepa_123',
          status: 'processing',
          client_secret: 'pi_sepa_123_secret'
        })
      };

      const paymentRequest = {
        payment_method_id: 'pm_sepa_123',
        amount_cents: 5000, // €50.00
        currency: 'EUR' as const,
        tenant_id: 'tenant_123',
        description: 'SEPA subscription payment'
      };

      const result = await mockSEPAService.processSEPAPayment(paymentRequest);
      expect(result.payment_intent_id).toBe('pi_sepa_123');
      expect(result.status).toBe('processing');
    });

    it('should enforce SEPA amount limits', async () => {
      // Test minimum amount (€0.50)
      const tooLowRequest = {
        payment_method_id: 'pm_sepa_123',
        amount_cents: 49, // €0.49
        currency: 'EUR' as const,
        tenant_id: 'tenant_123'
      };

      // Test maximum amount (€999,999.99)
      const tooHighRequest = {
        payment_method_id: 'pm_sepa_123',
        amount_cents: 100000000, // €1,000,000.00
        currency: 'EUR' as const,
        tenant_id: 'tenant_123'
      };

      // These should be validated at the API level
      expect(tooLowRequest.amount_cents).toBeLessThan(50);
      expect(tooHighRequest.amount_cents).toBeGreaterThan(99999999);
    });

    it('should handle SEPA country restrictions', async () => {
      const sepaCountries = ['BE', 'DE', 'FR', 'NL', 'IT', 'ES', 'AT'];
      const nonSepaCountries = ['US', 'GB', 'CA'];

      // SEPA countries should be supported
      for (const country of sepaCountries) {
        const request: PaymentMethodValidationRequest = {
          payment_method_code: 'sepa_debit',
          country_code: country as any,
          payment_data: {
            iban: '****************'
          }
        };

        // This should not throw an error for SEPA countries
        expect(() => request).not.toThrow();
      }

      // Non-SEPA countries should be handled appropriately
      for (const country of nonSepaCountries) {
        // These countries should not have SEPA as an available payment method
        expect(sepaCountries).not.toContain(country);
      }
    });
  });
});
