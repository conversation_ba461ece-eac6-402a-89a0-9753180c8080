/**
 * End-to-End Payment Flow Tests
 * 
 * Comprehensive tests for the complete payment flow including
 * UI components, API integration, and multi-country scenarios.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { PaymentMethodSelector } from '@/components/payment/PaymentMethodSelector';
import { PaymentMethodForm } from '@/components/payment/PaymentMethodForm';
import { PaymentMethodManager } from '@/components/payment/PaymentMethodManager';
import { usePaymentMethods } from '@/lib/hooks/usePaymentMethods';
import { usePaymentProcessing } from '@/lib/hooks/usePaymentProcessing';

// Mock the hooks
vi.mock('@/lib/hooks/usePaymentMethods');
vi.mock('@/lib/hooks/usePaymentProcessing');

const mockUsePaymentMethods = usePaymentMethods as any;
const mockUsePaymentProcessing = usePaymentProcessing as any;

describe('End-to-End Payment Flow', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Default mock implementations
    mockUsePaymentMethods.mockReturnValue({
      availableMethods: [],
      loading: false,
      error: null,
      fetchAvailableMethods: vi.fn(),
      validatePaymentMethod: vi.fn(),
      recommendPaymentMethods: vi.fn()
    });

    mockUsePaymentProcessing.mockReturnValue({
      processing: false,
      error: null,
      createPaymentMethod: vi.fn(),
      processPayment: vi.fn()
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('US Customer Payment Flow', () => {
    it('should complete credit card payment for US customer', async () => {
      const mockAvailableMethods = [
        {
          id: '1',
          code: 'card',
          name: 'Credit/Debit Card',
          country_code: 'US',
          currency_code: 'USD',
          is_primary: true,
          processing_fee_percentage: 2.9,
          processing_fee_fixed_cents: 30,
          estimated_completion_time: 'Instant',
          validation_rules: [
            {
              field_name: 'number',
              validation_type: 'format',
              validation_pattern: '^[0-9]{13,19}$',
              is_required: true
            }
          ]
        },
        {
          id: '2',
          code: 'ach_debit',
          name: 'ACH Direct Debit',
          country_code: 'US',
          currency_code: 'USD',
          is_primary: false,
          processing_fee_percentage: 0.8,
          processing_fee_fixed_cents: 5,
          estimated_completion_time: '3-5 business days'
        }
      ];

      const mockValidatePaymentMethod = vi.fn().mockResolvedValue({
        is_valid: true,
        errors: [],
        formatted_data: {
          number: '****************',
          exp_month: '12',
          exp_year: '2025',
          cvc: '123'
        }
      });

      const mockCreatePaymentMethod = vi.fn().mockResolvedValue({
        stripe_payment_method_id: 'pm_test_123',
        status: 'succeeded'
      });

      mockUsePaymentMethods.mockReturnValue({
        availableMethods: mockAvailableMethods,
        loading: false,
        error: null,
        fetchAvailableMethods: vi.fn(),
        validatePaymentMethod: mockValidatePaymentMethod,
        recommendPaymentMethods: vi.fn()
      });

      mockUsePaymentProcessing.mockReturnValue({
        processing: false,
        error: null,
        createPaymentMethod: mockCreatePaymentMethod,
        processPayment: vi.fn()
      });

      const onPaymentMethodCreated = vi.fn();

      render(
        <PaymentMethodManager
          countryCode="US"
          currencyCode="USD"
          tenantId="tenant_123"
          onPaymentMethodCreated={onPaymentMethodCreated}
        />
      );

      // Step 1: Select payment method
      await waitFor(() => {
        expect(screen.getByText('Credit/Debit Card')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText('Credit/Debit Card'));

      // Step 2: Fill payment form
      await waitFor(() => {
        expect(screen.getByLabelText(/card number/i)).toBeInTheDocument();
      });

      fireEvent.change(screen.getByLabelText(/card number/i), {
        target: { value: '****************' }
      });

      fireEvent.change(screen.getByLabelText(/expiry month/i), {
        target: { value: '12' }
      });

      fireEvent.change(screen.getByLabelText(/expiry year/i), {
        target: { value: '2025' }
      });

      fireEvent.change(screen.getByLabelText(/cvc/i), {
        target: { value: '123' }
      });

      // Step 3: Submit payment method
      fireEvent.click(screen.getByText(/create payment method/i));

      await waitFor(() => {
        expect(mockValidatePaymentMethod).toHaveBeenCalledWith({
          payment_method_code: 'card',
          country_code: 'US',
          payment_data: {
            number: '****************',
            exp_month: '12',
            exp_year: '2025',
            cvc: '123'
          }
        });
      });

      await waitFor(() => {
        expect(mockCreatePaymentMethod).toHaveBeenCalledWith({
          payment_method_code: 'card',
          country_code: 'US',
          currency_code: 'USD',
          tenant_id: 'tenant_123',
          payment_data: {
            number: '****************',
            exp_month: '12',
            exp_year: '2025',
            cvc: '123'
          }
        });
      });

      await waitFor(() => {
        expect(onPaymentMethodCreated).toHaveBeenCalledWith({
          stripe_payment_method_id: 'pm_test_123',
          status: 'succeeded'
        });
      });
    });

    it('should complete ACH payment for US customer', async () => {
      const mockAvailableMethods = [
        {
          id: '2',
          code: 'ach_debit',
          name: 'ACH Direct Debit',
          country_code: 'US',
          currency_code: 'USD',
          is_primary: false,
          processing_fee_percentage: 0.8,
          processing_fee_fixed_cents: 5,
          estimated_completion_time: '3-5 business days',
          validation_rules: [
            {
              field_name: 'routing_number',
              validation_type: 'format',
              validation_pattern: '^[0-9]{9}$',
              is_required: true
            },
            {
              field_name: 'account_number',
              validation_type: 'format',
              validation_pattern: '^[0-9]{4,17}$',
              is_required: true
            }
          ]
        }
      ];

      const mockValidatePaymentMethod = vi.fn().mockResolvedValue({
        is_valid: true,
        errors: [],
        formatted_data: {
          routing_number: '*********',
          account_number: '************'
        }
      });

      const mockCreatePaymentMethod = vi.fn().mockResolvedValue({
        stripe_payment_method_id: 'pm_test_ach_123',
        status: 'succeeded'
      });

      mockUsePaymentMethods.mockReturnValue({
        availableMethods: mockAvailableMethods,
        loading: false,
        error: null,
        fetchAvailableMethods: vi.fn(),
        validatePaymentMethod: mockValidatePaymentMethod,
        recommendPaymentMethods: vi.fn()
      });

      mockUsePaymentProcessing.mockReturnValue({
        processing: false,
        error: null,
        createPaymentMethod: mockCreatePaymentMethod,
        processPayment: vi.fn()
      });

      const onPaymentMethodCreated = vi.fn();

      render(
        <PaymentMethodManager
          countryCode="US"
          currencyCode="USD"
          tenantId="tenant_123"
          onPaymentMethodCreated={onPaymentMethodCreated}
        />
      );

      // Select ACH payment method
      await waitFor(() => {
        expect(screen.getByText('ACH Direct Debit')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText('ACH Direct Debit'));

      // Fill ACH form
      await waitFor(() => {
        expect(screen.getByLabelText(/routing number/i)).toBeInTheDocument();
      });

      fireEvent.change(screen.getByLabelText(/routing number/i), {
        target: { value: '*********' }
      });

      fireEvent.change(screen.getByLabelText(/account number/i), {
        target: { value: '************' }
      });

      // Submit payment method
      fireEvent.click(screen.getByText(/create payment method/i));

      await waitFor(() => {
        expect(mockCreatePaymentMethod).toHaveBeenCalledWith({
          payment_method_code: 'ach_debit',
          country_code: 'US',
          currency_code: 'USD',
          tenant_id: 'tenant_123',
          payment_data: {
            routing_number: '*********',
            account_number: '************'
          }
        });
      });

      await waitFor(() => {
        expect(onPaymentMethodCreated).toHaveBeenCalledWith({
          stripe_payment_method_id: 'pm_test_ach_123',
          status: 'succeeded'
        });
      });
    });
  });

  describe('EU Customer Payment Flow', () => {
    it('should complete SEPA payment for Belgian customer', async () => {
      const mockAvailableMethods = [
        {
          id: '3',
          code: 'sepa_debit',
          name: 'SEPA Direct Debit',
          country_code: 'BE',
          currency_code: 'EUR',
          is_primary: true,
          processing_fee_percentage: 0.35,
          processing_fee_fixed_cents: 0,
          estimated_completion_time: '1-3 business days',
          validation_rules: [
            {
              field_name: 'iban',
              validation_type: 'checksum',
              validation_pattern: '^[A-Z]{2}[0-9]{2}[A-Z0-9]{4}[0-9]{7}([A-Z0-9]?){0,16}$',
              is_required: true
            }
          ]
        }
      ];

      const mockValidatePaymentMethod = vi.fn().mockResolvedValue({
        is_valid: true,
        errors: [],
        formatted_data: {
          iban: '****************'
        }
      });

      const mockCreatePaymentMethod = vi.fn().mockResolvedValue({
        stripe_payment_method_id: 'pm_test_sepa_123',
        status: 'succeeded'
      });

      mockUsePaymentMethods.mockReturnValue({
        availableMethods: mockAvailableMethods,
        loading: false,
        error: null,
        fetchAvailableMethods: vi.fn(),
        validatePaymentMethod: mockValidatePaymentMethod,
        recommendPaymentMethods: vi.fn()
      });

      mockUsePaymentProcessing.mockReturnValue({
        processing: false,
        error: null,
        createPaymentMethod: mockCreatePaymentMethod,
        processPayment: vi.fn()
      });

      const onPaymentMethodCreated = vi.fn();

      render(
        <PaymentMethodManager
          countryCode="BE"
          currencyCode="EUR"
          tenantId="tenant_123"
          onPaymentMethodCreated={onPaymentMethodCreated}
        />
      );

      // Select SEPA payment method
      await waitFor(() => {
        expect(screen.getByText('SEPA Direct Debit')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText('SEPA Direct Debit'));

      // Fill SEPA form
      await waitFor(() => {
        expect(screen.getByLabelText(/iban/i)).toBeInTheDocument();
      });

      fireEvent.change(screen.getByLabelText(/iban/i), {
        target: { value: '****************' }
      });

      // Submit payment method
      fireEvent.click(screen.getByText(/create payment method/i));

      await waitFor(() => {
        expect(mockCreatePaymentMethod).toHaveBeenCalledWith({
          payment_method_code: 'sepa_debit',
          country_code: 'BE',
          currency_code: 'EUR',
          tenant_id: 'tenant_123',
          payment_data: {
            iban: '****************'
          }
        });
      });

      await waitFor(() => {
        expect(onPaymentMethodCreated).toHaveBeenCalledWith({
          stripe_payment_method_id: 'pm_test_sepa_123',
          status: 'succeeded'
        });
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle validation errors gracefully', async () => {
      const mockValidatePaymentMethod = vi.fn().mockResolvedValue({
        is_valid: false,
        errors: [
          { field: 'number', message: 'Invalid card number', code: 'INVALID_LUHN' }
        ]
      });

      mockUsePaymentMethods.mockReturnValue({
        availableMethods: [
          {
            id: '1',
            code: 'card',
            name: 'Credit/Debit Card',
            country_code: 'US',
            currency_code: 'USD'
          }
        ],
        loading: false,
        error: null,
        fetchAvailableMethods: vi.fn(),
        validatePaymentMethod: mockValidatePaymentMethod,
        recommendPaymentMethods: vi.fn()
      });

      render(
        <PaymentMethodManager
          countryCode="US"
          currencyCode="USD"
          tenantId="tenant_123"
          onPaymentMethodCreated={vi.fn()}
        />
      );

      // Select card and fill invalid data
      fireEvent.click(screen.getByText('Credit/Debit Card'));

      await waitFor(() => {
        expect(screen.getByLabelText(/card number/i)).toBeInTheDocument();
      });

      fireEvent.change(screen.getByLabelText(/card number/i), {
        target: { value: '1234567890123456' }
      });

      fireEvent.click(screen.getByText(/create payment method/i));

      await waitFor(() => {
        expect(screen.getByText('Invalid card number')).toBeInTheDocument();
      });
    });

    it('should handle network errors', async () => {
      const mockCreatePaymentMethod = vi.fn().mockRejectedValue(
        new Error('Network error')
      );

      mockUsePaymentProcessing.mockReturnValue({
        processing: false,
        error: 'Network error',
        createPaymentMethod: mockCreatePaymentMethod,
        processPayment: vi.fn()
      });

      render(
        <PaymentMethodManager
          countryCode="US"
          currencyCode="USD"
          tenantId="tenant_123"
          onPaymentMethodCreated={vi.fn()}
        />
      );

      await waitFor(() => {
        expect(screen.getByText(/network error/i)).toBeInTheDocument();
      });
    });
  });

  describe('Multi-Country Support', () => {
    const countries = [
      { code: 'US', currency: 'USD', methods: ['card', 'ach_debit'] },
      { code: 'BE', currency: 'EUR', methods: ['card', 'sepa_debit', 'bancontact'] },
      { code: 'GB', currency: 'GBP', methods: ['card', 'bacs_debit'] },
      { code: 'CA', currency: 'CAD', methods: ['card', 'acss_debit'] }
    ];

    countries.forEach(({ code, currency, methods }) => {
      it(`should support payment methods for ${code}`, async () => {
        const mockAvailableMethods = methods.map((method, index) => ({
          id: `${index + 1}`,
          code: method,
          name: method.replace('_', ' ').toUpperCase(),
          country_code: code,
          currency_code: currency,
          is_primary: index === 0
        }));

        mockUsePaymentMethods.mockReturnValue({
          availableMethods: mockAvailableMethods,
          loading: false,
          error: null,
          fetchAvailableMethods: vi.fn(),
          validatePaymentMethod: vi.fn(),
          recommendPaymentMethods: vi.fn()
        });

        render(
          <PaymentMethodManager
            countryCode={code}
            currencyCode={currency}
            tenantId="tenant_123"
            onPaymentMethodCreated={vi.fn()}
          />
        );

        await waitFor(() => {
          methods.forEach(method => {
            expect(screen.getByText(new RegExp(method.replace('_', ' '), 'i'))).toBeInTheDocument();
          });
        });
      });
    });
  });
});
