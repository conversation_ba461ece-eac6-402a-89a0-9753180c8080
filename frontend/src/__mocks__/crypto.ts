/**
 * Crypto Mock for Testing
 * 
 * Mock implementation of Node.js crypto module for browser tests
 */

import { vi } from 'vitest';

// Mock cipher for encryption tests
const mockCipher = {
  update: vi.fn((data: string) => Buffer.from(data + '_encrypted')),
  final: vi.fn(() => Buffer.from('_final')),
  getAuthTag: vi.fn(() => Buffer.from('mock_auth_tag')),
};

// Mock decipher for decryption tests
const mockDecipher = {
  update: vi.fn((data: Buffer) => {
    const str = data.toString();
    return Buffer.from(str.replace('_encrypted', '').replace('_final', ''));
  }),
  final: vi.fn(() => Buffer.from('')),
  setAuthTag: vi.fn(),
};

export const mockCrypto = {
  createCipher: vi.fn(() => mockCipher),
  createDecipher: vi.fn(() => mockDecipher),
  createCipheriv: vi.fn(() => mockCipher),
  createDecipheriv: vi.fn(() => mockDecipher),
  
  randomBytes: vi.fn((size: number) => {
    const bytes = new Array(size).fill(0).map(() => Math.floor(Math.random() * 256));
    return Buffer.from(bytes);
  }),
  
  randomUUID: vi.fn(() => 'mock-uuid-' + Math.random().toString(36).substr(2, 9)),
  
  createHash: vi.fn(() => ({
    update: vi.fn().mockReturnThis(),
    digest: vi.fn(() => 'mock_hash_digest'),
  })),
  
  createHmac: vi.fn(() => ({
    update: vi.fn().mockReturnThis(),
    digest: vi.fn(() => 'mock_hmac_digest'),
  })),
  
  pbkdf2: vi.fn((password, salt, iterations, keylen, digest, callback) => {
    const key = Buffer.from('mock_derived_key_' + keylen);
    if (callback) {
      callback(null, key);
    }
    return key;
  }),
  
  pbkdf2Sync: vi.fn((password, salt, iterations, keylen, digest) => {
    return Buffer.from('mock_derived_key_' + keylen);
  }),
  
  scrypt: vi.fn((password, salt, keylen, callback) => {
    const key = Buffer.from('mock_scrypt_key_' + keylen);
    if (callback) {
      callback(null, key);
    }
    return key;
  }),
  
  scryptSync: vi.fn((password, salt, keylen) => {
    return Buffer.from('mock_scrypt_key_' + keylen);
  }),
  
  timingSafeEqual: vi.fn((a, b) => {
    return a.length === b.length && a.toString() === b.toString();
  }),
  
  constants: {
    RSA_PKCS1_PADDING: 1,
    RSA_SSLV23_PADDING: 2,
    RSA_NO_PADDING: 3,
    RSA_PKCS1_OAEP_PADDING: 4,
    RSA_X931_PADDING: 5,
    RSA_PKCS1_PSS_PADDING: 6,
  },
};

// Export for CommonJS
module.exports = mockCrypto;

// Export for ES modules
export default mockCrypto;
