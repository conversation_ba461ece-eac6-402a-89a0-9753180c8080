/**
 * <PERSON><PERSON> Mock for Testing
 * 
 * Comprehensive mock of Stripe SDK for payment testing
 */

import { vi } from 'vitest';

export const mockStripe = {
  elements: vi.fn(() => ({
    create: vi.fn(() => ({
      mount: vi.fn(),
      unmount: vi.fn(),
      on: vi.fn(),
      off: vi.fn(),
      update: vi.fn(),
      focus: vi.fn(),
      blur: vi.fn(),
      clear: vi.fn(),
    })),
    getElement: vi.fn(),
    fetchUpdates: vi.fn(),
  })),
  
  createPaymentMethod: vi.fn().mockResolvedValue({
    paymentMethod: {
      id: 'pm_test_123',
      type: 'card',
      card: {
        brand: 'visa',
        last4: '4242',
        exp_month: 12,
        exp_year: 2025,
      },
    },
    error: null,
  }),
  
  confirmPayment: vi.fn().mockResolvedValue({
    paymentIntent: {
      id: 'pi_test_123',
      status: 'succeeded',
      amount: 5000,
      currency: 'usd',
    },
    error: null,
  }),
  
  confirmCardPayment: vi.fn().mockResolvedValue({
    paymentIntent: {
      id: 'pi_test_123',
      status: 'succeeded',
      amount: 5000,
      currency: 'usd',
    },
    error: null,
  }),
  
  retrievePaymentIntent: vi.fn().mockResolvedValue({
    id: 'pi_test_123',
    status: 'succeeded',
    amount: 5000,
    currency: 'usd',
  }),
  
  paymentIntents: {
    create: vi.fn().mockResolvedValue({
      id: 'pi_test_123',
      status: 'requires_payment_method',
      amount: 5000,
      currency: 'usd',
      client_secret: 'pi_test_123_secret',
    }),
    
    retrieve: vi.fn().mockResolvedValue({
      id: 'pi_test_123',
      status: 'succeeded',
      amount: 5000,
      currency: 'usd',
    }),
    
    update: vi.fn().mockResolvedValue({
      id: 'pi_test_123',
      status: 'requires_payment_method',
      amount: 5000,
      currency: 'usd',
    }),
    
    confirm: vi.fn().mockResolvedValue({
      id: 'pi_test_123',
      status: 'succeeded',
      amount: 5000,
      currency: 'usd',
    }),
  },
  
  paymentMethods: {
    create: vi.fn().mockResolvedValue({
      id: 'pm_test_123',
      type: 'card',
      card: {
        brand: 'visa',
        last4: '4242',
        exp_month: 12,
        exp_year: 2025,
      },
    }),
    
    retrieve: vi.fn().mockResolvedValue({
      id: 'pm_test_123',
      type: 'card',
      card: {
        brand: 'visa',
        last4: '4242',
        exp_month: 12,
        exp_year: 2025,
      },
    }),
    
    attach: vi.fn().mockResolvedValue({
      id: 'pm_test_123',
      customer: 'cus_test_123',
    }),
    
    detach: vi.fn().mockResolvedValue({
      id: 'pm_test_123',
      customer: null,
    }),
  },
  
  customers: {
    create: vi.fn().mockResolvedValue({
      id: 'cus_test_123',
      email: '<EMAIL>',
    }),
    
    retrieve: vi.fn().mockResolvedValue({
      id: 'cus_test_123',
      email: '<EMAIL>',
    }),
    
    update: vi.fn().mockResolvedValue({
      id: 'cus_test_123',
      email: '<EMAIL>',
    }),
  },
  
  subscriptions: {
    create: vi.fn().mockResolvedValue({
      id: 'sub_test_123',
      status: 'active',
      current_period_start: Math.floor(Date.now() / 1000),
      current_period_end: Math.floor(Date.now() / 1000) + 2592000, // 30 days
    }),
    
    retrieve: vi.fn().mockResolvedValue({
      id: 'sub_test_123',
      status: 'active',
      current_period_start: Math.floor(Date.now() / 1000),
      current_period_end: Math.floor(Date.now() / 1000) + 2592000,
    }),
    
    update: vi.fn().mockResolvedValue({
      id: 'sub_test_123',
      status: 'active',
    }),
    
    cancel: vi.fn().mockResolvedValue({
      id: 'sub_test_123',
      status: 'canceled',
    }),
  },
};

// Mock Stripe constructor
export const Stripe = vi.fn().mockImplementation(() => mockStripe);

// Default export for ES modules
export default Stripe;
