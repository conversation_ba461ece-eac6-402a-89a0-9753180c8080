<?xml version="1.0" encoding="UTF-8" ?>
<testsuites name="vitest tests" tests="59" failures="0" errors="0" time="0.016169917">
    <testsuite name="src/__tests__/coverage-validation.test.ts" timestamp="2025-08-04T07:48:18.460Z" hostname="<PERSON><PERSON><PERSON><PERSON>-Mac-mini.local" tests="14" failures="0" errors="0" skipped="0" time="0.004802584">
        <testcase classname="src/__tests__/coverage-validation.test.ts" name="Test Coverage Validation &gt; Critical Component Coverage &gt; should have test files for all critical payment components" time="0.000420084">
        </testcase>
        <testcase classname="src/__tests__/coverage-validation.test.ts" name="Test Coverage Validation &gt; Critical Component Coverage &gt; should have comprehensive test suites for security functions" time="0.000078208">
        </testcase>
        <testcase classname="src/__tests__/coverage-validation.test.ts" name="Test Coverage Validation &gt; Critical Component Coverage &gt; should have authentication tests covering all scenarios" time="0.0000615">
        </testcase>
        <testcase classname="src/__tests__/coverage-validation.test.ts" name="Test Coverage Validation &gt; Test Quality Metrics &gt; should have meaningful test descriptions" time="0.001829625">
        </testcase>
        <testcase classname="src/__tests__/coverage-validation.test.ts" name="Test Coverage Validation &gt; Test Quality Metrics &gt; should have proper test structure and organization" time="0.000564166">
        </testcase>
        <testcase classname="src/__tests__/coverage-validation.test.ts" name="Test Coverage Validation &gt; Security Test Coverage &gt; should test all encryption scenarios" time="0.000262541">
        </testcase>
        <testcase classname="src/__tests__/coverage-validation.test.ts" name="Test Coverage Validation &gt; Security Test Coverage &gt; should test all authentication scenarios" time="0.000132166">
        </testcase>
        <testcase classname="src/__tests__/coverage-validation.test.ts" name="Test Coverage Validation &gt; Security Test Coverage &gt; should test error handling comprehensively" time="0.000366167">
        </testcase>
        <testcase classname="src/__tests__/coverage-validation.test.ts" name="Test Coverage Validation &gt; Integration Test Coverage &gt; should test complete payment flows" time="0.000125542">
        </testcase>
        <testcase classname="src/__tests__/coverage-validation.test.ts" name="Test Coverage Validation &gt; Integration Test Coverage &gt; should test tenant isolation" time="0.000102042">
        </testcase>
        <testcase classname="src/__tests__/coverage-validation.test.ts" name="Test Coverage Validation &gt; Test Configuration Validation &gt; should have proper Jest configuration" time="0.00027875">
        </testcase>
        <testcase classname="src/__tests__/coverage-validation.test.ts" name="Test Coverage Validation &gt; Test Configuration Validation &gt; should have test setup files" time="0.000226791">
        </testcase>
        <testcase classname="src/__tests__/coverage-validation.test.ts" name="Test Coverage Validation &gt; Mock Quality &gt; should have comprehensive mocks for external dependencies" time="0.000144583">
        </testcase>
        <testcase classname="src/__tests__/coverage-validation.test.ts" name="Test Coverage Validation &gt; Mock Quality &gt; should clean up mocks properly" time="0.000110083">
        </testcase>
    </testsuite>
    <testsuite name="src/__tests__/unit/compliance-services.test.ts" timestamp="2025-08-04T07:48:18.463Z" hostname="Jean-Philippes-Mac-mini.local" tests="19" failures="0" errors="0" skipped="6" time="0.002665583">
        <testcase classname="src/__tests__/unit/compliance-services.test.ts" name="Compliance Services &gt; Compliance Audit Service &gt; PCI DSS Compliance &gt; should validate PCI DSS Level 1 requirements" time="0.000457">
        </testcase>
        <testcase classname="src/__tests__/unit/compliance-services.test.ts" name="Compliance Services &gt; Compliance Audit Service &gt; PCI DSS Compliance &gt; should identify PCI DSS violations" time="0.000290166">
        </testcase>
        <testcase classname="src/__tests__/unit/compliance-services.test.ts" name="Compliance Services &gt; Compliance Audit Service &gt; PCI DSS Compliance &gt; should calculate compliance scores correctly" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/__tests__/unit/compliance-services.test.ts" name="Compliance Services &gt; Compliance Audit Service &gt; GDPR Compliance &gt; should validate GDPR data protection requirements" time="0.000172291">
        </testcase>
        <testcase classname="src/__tests__/unit/compliance-services.test.ts" name="Compliance Services &gt; Compliance Audit Service &gt; GDPR Compliance &gt; should identify GDPR violations" time="0.00013625">
        </testcase>
        <testcase classname="src/__tests__/unit/compliance-services.test.ts" name="Compliance Services &gt; Compliance Audit Service &gt; GDPR Compliance &gt; should validate data subject rights implementation" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/__tests__/unit/compliance-services.test.ts" name="Compliance Services &gt; Compliance Audit Service &gt; SOX Compliance &gt; should validate SOX financial controls" time="0.000171125">
        </testcase>
        <testcase classname="src/__tests__/unit/compliance-services.test.ts" name="Compliance Services &gt; Compliance Audit Service &gt; SOX Compliance &gt; should identify SOX control deficiencies" time="0.000110042">
        </testcase>
        <testcase classname="src/__tests__/unit/compliance-services.test.ts" name="Compliance Services &gt; Compliance Audit Service &gt; Regional Compliance &gt; should validate SEPA compliance for EU payments" time="0.000168">
        </testcase>
        <testcase classname="src/__tests__/unit/compliance-services.test.ts" name="Compliance Services &gt; Compliance Audit Service &gt; Regional Compliance &gt; should validate PSD2 compliance for EU payments" time="0.000240875">
        </testcase>
        <testcase classname="src/__tests__/unit/compliance-services.test.ts" name="Compliance Services &gt; Compliance Framework Validator &gt; Framework Selection &gt; should select appropriate frameworks for US operations" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/__tests__/unit/compliance-services.test.ts" name="Compliance Services &gt; Compliance Framework Validator &gt; Framework Selection &gt; should select appropriate frameworks for EU operations" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/__tests__/unit/compliance-services.test.ts" name="Compliance Services &gt; Compliance Framework Validator &gt; Framework Selection &gt; should handle multi-jurisdiction operations" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/__tests__/unit/compliance-services.test.ts" name="Compliance Services &gt; Compliance Framework Validator &gt; Risk Assessment &gt; should assess compliance risk levels" time="0.000177541">
        </testcase>
        <testcase classname="src/__tests__/unit/compliance-services.test.ts" name="Compliance Services &gt; Compliance Framework Validator &gt; Risk Assessment &gt; should provide risk mitigation recommendations" time="0.00010075">
        </testcase>
        <testcase classname="src/__tests__/unit/compliance-services.test.ts" name="Compliance Services &gt; Compliance Framework Validator &gt; Compliance Monitoring &gt; should generate compliance monitoring schedules" time="0.000160958">
        </testcase>
        <testcase classname="src/__tests__/unit/compliance-services.test.ts" name="Compliance Services &gt; Compliance Framework Validator &gt; Compliance Monitoring &gt; should track compliance metrics" time="0.000136">
        </testcase>
        <testcase classname="src/__tests__/unit/compliance-services.test.ts" name="Compliance Services &gt; Compliance Framework Validator &gt; Audit Trail &gt; should log compliance events" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/__tests__/unit/compliance-services.test.ts" name="Compliance Services &gt; Compliance Framework Validator &gt; Audit Trail &gt; should generate compliance reports" time="0.00026075">
        </testcase>
    </testsuite>
    <testsuite name="src/__tests__/unit/validation-services.test.ts" timestamp="2025-08-04T07:48:18.464Z" hostname="Jean-Philippes-Mac-mini.local" tests="26" failures="0" errors="0" skipped="6" time="0.00870175">
        <testcase classname="src/__tests__/unit/validation-services.test.ts" name="Payment Validation Services &gt; Regional Payment Method Service &gt; getAvailablePaymentMethods &gt; should return available payment methods for US/USD" time="0.001436584">
        </testcase>
        <testcase classname="src/__tests__/unit/validation-services.test.ts" name="Payment Validation Services &gt; Regional Payment Method Service &gt; getAvailablePaymentMethods &gt; should return SEPA methods for EU countries" time="0.000497459">
        </testcase>
        <testcase classname="src/__tests__/unit/validation-services.test.ts" name="Payment Validation Services &gt; Regional Payment Method Service &gt; getAvailablePaymentMethods &gt; should filter methods by minimum amount" time="0.000492125">
        </testcase>
        <testcase classname="src/__tests__/unit/validation-services.test.ts" name="Payment Validation Services &gt; Regional Payment Method Service &gt; getAvailablePaymentMethods &gt; should handle unsupported country/currency combinations" time="0.000431333">
        </testcase>
        <testcase classname="src/__tests__/unit/validation-services.test.ts" name="Payment Validation Services &gt; Regional Payment Method Service &gt; validatePaymentMethod &gt; should validate US credit card data" time="0.000916791">
        </testcase>
        <testcase classname="src/__tests__/unit/validation-services.test.ts" name="Payment Validation Services &gt; Regional Payment Method Service &gt; validatePaymentMethod &gt; should validate SEPA IBAN" time="0.000485333">
        </testcase>
        <testcase classname="src/__tests__/unit/validation-services.test.ts" name="Payment Validation Services &gt; Regional Payment Method Service &gt; validatePaymentMethod &gt; should reject invalid credit card numbers" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/__tests__/unit/validation-services.test.ts" name="Payment Validation Services &gt; Regional Payment Method Service &gt; validatePaymentMethod &gt; should reject invalid IBAN" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/__tests__/unit/validation-services.test.ts" name="Payment Validation Services &gt; Regional Payment Method Service &gt; validatePaymentMethod &gt; should validate expiration dates" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/__tests__/unit/validation-services.test.ts" name="Payment Validation Services &gt; Payment Validation Rules Service &gt; Card Validation Rules &gt; should validate Visa card numbers" time="0.000344">
        </testcase>
        <testcase classname="src/__tests__/unit/validation-services.test.ts" name="Payment Validation Services &gt; Payment Validation Rules Service &gt; Card Validation Rules &gt; should validate Mastercard numbers" time="0.000195">
        </testcase>
        <testcase classname="src/__tests__/unit/validation-services.test.ts" name="Payment Validation Services &gt; Payment Validation Rules Service &gt; Card Validation Rules &gt; should validate American Express numbers" time="0.000327833">
        </testcase>
        <testcase classname="src/__tests__/unit/validation-services.test.ts" name="Payment Validation Services &gt; Payment Validation Rules Service &gt; Card Validation Rules &gt; should reject invalid card numbers" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/__tests__/unit/validation-services.test.ts" name="Payment Validation Services &gt; Payment Validation Rules Service &gt; Card Validation Rules &gt; should validate CVC codes" time="0.000496083">
        </testcase>
        <testcase classname="src/__tests__/unit/validation-services.test.ts" name="Payment Validation Services &gt; Payment Validation Rules Service &gt; IBAN Validation Rules &gt; should validate correct IBAN formats" time="0.000497667">
        </testcase>
        <testcase classname="src/__tests__/unit/validation-services.test.ts" name="Payment Validation Services &gt; Payment Validation Rules Service &gt; IBAN Validation Rules &gt; should reject invalid IBAN formats" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/__tests__/unit/validation-services.test.ts" name="Payment Validation Services &gt; Payment Validation Rules Service &gt; IBAN Validation Rules &gt; should extract country codes from IBANs" time="0.000174167">
        </testcase>
        <testcase classname="src/__tests__/unit/validation-services.test.ts" name="Payment Validation Services &gt; Payment Validation Rules Service &gt; Regional Validation Rules &gt; should apply US-specific validation rules" time="0.000402958">
        </testcase>
        <testcase classname="src/__tests__/unit/validation-services.test.ts" name="Payment Validation Services &gt; Payment Validation Rules Service &gt; Regional Validation Rules &gt; should apply EU-specific validation rules" time="0.000166625">
        </testcase>
        <testcase classname="src/__tests__/unit/validation-services.test.ts" name="Payment Validation Services &gt; Payment Validation Rules Service &gt; Regional Validation Rules &gt; should handle unsupported regions" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/__tests__/unit/validation-services.test.ts" name="Payment Validation Services &gt; Payment Validation Rules Service &gt; Amount Validation Rules &gt; should validate minimum amounts by region" time="0.000143292">
        </testcase>
        <testcase classname="src/__tests__/unit/validation-services.test.ts" name="Payment Validation Services &gt; Payment Validation Rules Service &gt; Amount Validation Rules &gt; should validate maximum amounts by region" time="0.000111834">
        </testcase>
        <testcase classname="src/__tests__/unit/validation-services.test.ts" name="Payment Validation Services &gt; Payment Validation Rules Service &gt; Address Validation Rules &gt; should validate US addresses" time="0.000160666">
        </testcase>
        <testcase classname="src/__tests__/unit/validation-services.test.ts" name="Payment Validation Services &gt; Payment Validation Rules Service &gt; Address Validation Rules &gt; should validate EU addresses" time="0.000103875">
        </testcase>
        <testcase classname="src/__tests__/unit/validation-services.test.ts" name="Payment Validation Services &gt; Payment Validation Rules Service &gt; Address Validation Rules &gt; should reject incomplete addresses" time="0.000090042">
        </testcase>
        <testcase classname="src/__tests__/unit/validation-services.test.ts" name="Payment Validation Services &gt; Payment Validation Rules Service &gt; Address Validation Rules &gt; should validate postal codes by country" time="0.000191834">
        </testcase>
    </testsuite>
</testsuites>
