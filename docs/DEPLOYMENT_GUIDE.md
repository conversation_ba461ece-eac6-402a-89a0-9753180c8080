# Production Deployment Guide

## PI Lawyer AI Payment System - Complete Deployment Instructions

This guide provides step-by-step instructions for deploying the PI Lawyer AI payment system to production.

---

## 📋 **Pre-Deployment Checklist**

### 1. Environment Setup
```bash
# 1. Clone and setup repository
git clone https://github.com/Jpkay/pi_lawyer_ai.git
cd pi_lawyer_ai
git checkout stripe  # Use the stripe branch with latest features

# 2. Install dependencies
npm install
cd backend && pip install -r requirements.txt && cd ..

# 3. Setup production environment configuration
node scripts/setup-production-config.js
```

### 2. Configuration Validation
```bash
# Validate environment configuration
node scripts/validate-production-environment.js

# Validate deployment readiness
node scripts/validate-deployment-readiness.js

# Run deployment health checks
./scripts/deployment-health-check.sh
```

### 3. Security Verification
- [ ] All secrets are production-ready (32+ characters)
- [ ] Stripe live keys configured (sk_live_*, pk_live_*)
- [ ] HTTPS enforced for all external URLs
- [ ] Payment encryption keys properly generated
- [ ] Database RLS policies enabled and tested

---

## 🚀 **Deployment Steps**

### Step 1: Database Setup

```bash
# 1. Setup Supabase production database
# - Create new Supabase project for production
# - Configure database schema and RLS policies
# - Update environment variables with production URLs

# 2. Run database migrations
npm run db:migrate:prod

# 3. Verify database connectivity
npm run db:test
```

### Step 2: Application Build

```bash
# 1. Build production application
npm run build

# 2. Test production build locally
npm run start

# 3. Verify all endpoints are working
curl http://localhost:3000/api/health/detailed
```

### Step 3: Infrastructure Deployment

#### Option A: Vercel Deployment (Recommended)
```bash
# 1. Install Vercel CLI
npm install -g vercel

# 2. Login to Vercel
vercel login

# 3. Deploy to production
vercel --prod

# 4. Configure environment variables in Vercel dashboard
# - Go to Project Settings > Environment Variables
# - Add all production environment variables
# - Ensure they're set for "Production" environment
```

#### Option B: Docker Deployment
```bash
# 1. Build Docker image
docker build -t pi-lawyer-ai:latest .

# 2. Run container with environment variables
docker run -d \
  --name pi-lawyer-ai \
  -p 3000:3000 \
  --env-file .env.production \
  pi-lawyer-ai:latest

# 3. Verify container is running
docker ps
docker logs pi-lawyer-ai
```

#### Option C: Traditional Server Deployment
```bash
# 1. Copy files to production server
rsync -av --exclude node_modules . user@server:/path/to/app/

# 2. Install dependencies on server
ssh user@server "cd /path/to/app && npm install --production"

# 3. Start application with PM2
ssh user@server "cd /path/to/app && pm2 start ecosystem.config.js"
```

### Step 4: Monitoring Setup

```bash
# 1. Deploy monitoring stack
./scripts/setup-payment-alerting.sh

# 2. Configure Grafana dashboards
# - Import monitoring/grafana-payment-dashboard.json
# - Configure data sources
# - Set up alert rules

# 3. Test alerting channels
# - Send test Slack notification
# - Test PagerDuty integration
# - Verify email alerts
```

### Step 5: DNS and SSL Configuration

```bash
# 1. Configure DNS records
# - Point domain to deployment server/service
# - Configure CNAME for www subdomain
# - Set up CDN if using one

# 2. SSL Certificate setup
# - Configure SSL certificate (Let's Encrypt, CloudFlare, etc.)
# - Verify HTTPS is working
# - Test SSL configuration with SSL Labs

# 3. Update environment variables with production URLs
# - NEXT_PUBLIC_SUPABASE_URL
# - NEXT_PUBLIC_BACKEND_API_URL
# - NEXT_PUBLIC_LAWS_API_BASE
```

---

## ✅ **Post-Deployment Validation**

### 1. Health Check Validation
```bash
# Test all health endpoints
curl https://your-domain.com/api/health
curl https://your-domain.com/api/health/detailed
curl https://your-domain.com/api/metrics

# Verify response codes and content
```

### 2. Payment System Testing
```bash
# Test payment method creation (with test data)
curl -X POST https://your-domain.com/api/payment-methods \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TEST_TOKEN" \
  -d '{"type": "card", "test": true}'

# Test payment processing (with test amounts)
curl -X POST https://your-domain.com/api/payment-methods/stripe/process \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TEST_TOKEN" \
  -d '{"amount": 100, "currency": "usd", "test": true}'
```

### 3. Monitoring Verification
```bash
# Check Prometheus metrics
curl https://your-domain.com/api/metrics

# Verify Grafana dashboards
# - Open Grafana dashboard
# - Check all panels are loading data
# - Verify alerts are configured

# Test alerting
# - Trigger a test alert
# - Verify notifications are received
```

### 4. Security Testing
```bash
# Test HTTPS enforcement
curl -I http://your-domain.com  # Should redirect to HTTPS

# Test API security
curl https://your-domain.com/api/payment-methods  # Should require auth

# Verify headers
curl -I https://your-domain.com  # Check security headers
```

---

## 📊 **Monitoring and Maintenance**

### Daily Monitoring
- [ ] Check Grafana dashboards for anomalies
- [ ] Review error rates in Sentry
- [ ] Monitor payment success rates
- [ ] Check system performance metrics

### Weekly Maintenance
- [ ] Review and update dependencies
- [ ] Check SSL certificate expiration
- [ ] Review security logs
- [ ] Update documentation if needed

### Monthly Reviews
- [ ] Performance optimization review
- [ ] Security audit
- [ ] Backup and disaster recovery testing
- [ ] Capacity planning review

---

## 🆘 **Troubleshooting**

### Common Issues

#### 1. Health Check Failures
```bash
# Check application logs
docker logs pi-lawyer-ai
# or
pm2 logs

# Check database connectivity
node -e "console.log(process.env.NEXT_PUBLIC_SUPABASE_URL)"

# Test Stripe connectivity
curl -H "Authorization: Bearer sk_live_..." https://api.stripe.com/v1/account
```

#### 2. Payment Processing Errors
```bash
# Check Stripe webhook configuration
curl https://your-domain.com/api/webhooks/stripe

# Verify payment encryption
node -e "
const crypto = require('crypto');
const key = process.env.PAYMENT_ENCRYPTION_KEY;
console.log('Key length:', key?.length);
"

# Check payment method validation
curl -X POST https://your-domain.com/api/payment-methods/validate \
  -H "Content-Type: application/json" \
  -d '{"type": "card", "number": "****************"}'
```

#### 3. Monitoring Issues
```bash
# Check Prometheus metrics endpoint
curl https://your-domain.com/api/metrics | head -20

# Verify Sentry configuration
node -e "console.log(process.env.NEXT_PUBLIC_SENTRY_DSN)"

# Test alerting
curl -X POST https://your-domain.com/api/test-alert
```

### Emergency Procedures

#### 1. Rollback Deployment
```bash
# Vercel rollback
vercel rollback

# Docker rollback
docker stop pi-lawyer-ai
docker run -d --name pi-lawyer-ai-backup previous-image:tag

# Traditional server rollback
ssh user@server "cd /path/to/app && git checkout previous-commit && pm2 restart all"
```

#### 2. Emergency Maintenance Mode
```bash
# Enable maintenance mode
echo "MAINTENANCE_MODE=true" >> .env.production

# Restart application
pm2 restart all
# or
docker restart pi-lawyer-ai
```

---

## 📞 **Support and Contacts**

### Emergency Contacts
- **Technical Lead**: [Contact Information]
- **DevOps Team**: [Contact Information]
- **Security Team**: [Contact Information]

### Monitoring Dashboards
- **Grafana**: https://grafana.your-domain.com
- **Sentry**: https://sentry.io/organizations/your-org/
- **Stripe Dashboard**: https://dashboard.stripe.com

### Documentation Links
- **API Documentation**: https://docs.your-domain.com
- **Runbooks**: https://docs.your-domain.com/runbooks
- **Architecture Diagrams**: https://docs.your-domain.com/architecture

---

## 🔄 **Continuous Deployment**

### Automated Deployment Pipeline
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm install
      - name: Run tests
        run: npm test
      - name: Validate environment
        run: node scripts/validate-production-environment.js
      - name: Build application
        run: npm run build
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          vercel-args: '--prod'
      - name: Run post-deployment tests
        run: ./scripts/deployment-health-check.sh
```

### Deployment Checklist Automation
```bash
# Create deployment checklist script
cat > scripts/pre-deployment-check.sh << 'EOF'
#!/bin/bash
set -e

echo "Running pre-deployment checks..."

# Environment validation
node scripts/validate-production-environment.js

# Security checks
node scripts/validate-deployment-readiness.js

# Build verification
npm run build
npm run test

echo "All pre-deployment checks passed!"
EOF

chmod +x scripts/pre-deployment-check.sh
```

---

**✅ Deployment Complete!**

Your PI Lawyer AI payment system is now deployed to production with comprehensive monitoring, alerting, and health checks. Monitor the system closely for the first 24-48 hours and follow the maintenance procedures outlined above.
