# Payment API Security Implementation

## Overview

This document outlines the comprehensive security implementation for PI Lawyer AI payment endpoints, including authentication, authorization, rate limiting, and data protection measures.

## Security Features Implemented

### 1. JWT Authentication
- **Requirement**: All payment endpoints require valid JWT tokens
- **Header**: `Authorization: Bearer <jwt_token>`
- **Validation**: Tokens are verified using `SUPABASE_JWT_SECRET`
- **Claims**: Must contain user ID, email, role, and tenant ID

### 2. API Key Validation
- **Requirement**: Critical endpoints require API key validation
- **Header**: `X-API-Key: <api_key>`
- **Environment Variable**: `PAYMENT_API_KEY`
- **Endpoints**: Payment creation and processing endpoints

### 3. Tenant-Based Authorization
- **Validation**: Users can only access their own tenant's data
- **Exception**: Super admins can access cross-tenant data
- **Implementation**: Automatic tenant ID verification from JWT claims

### 4. Role-Based Access Control (RBAC)
- **Read Operations**: `['partner', 'attorney', 'paralegal', 'staff', 'client']`
- **Write Operations**: `['partner', 'attorney', 'paralegal', 'staff']`
- **Critical Operations**: `['partner', 'attorney']` (with API key)

### 5. Rate Limiting
- **Payment Creation**: 10 requests per minute per user
- **Payment Processing**: 20 requests per minute per user
- **Validation**: 50 requests per minute per user
- **Default**: 30 requests per minute per user
- **Storage**: Redis-backed with in-memory fallback

### 6. Payment Data Encryption
- **Algorithm**: AES-256-GCM for maximum security
- **Key Management**: Separate keys for transport and field-level encryption
- **Field-Level Encryption**: Automatic encryption of sensitive payment fields
- **Data at Rest**: All payment data encrypted before database storage
- **Data in Transit**: Additional encryption layer for API communications
- **Integrity Verification**: SHA-256 hashing for data integrity checks
- **Automatic Expiration**: Configurable TTL for temporary payment data

### 7. Security Headers
All payment responses include comprehensive security headers:
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- `Referrer-Policy: strict-origin-when-cross-origin`
- `Cache-Control: no-store, no-cache, must-revalidate`

## Environment Variables Required

### Core Authentication
```bash
# JWT Secret for token validation
SUPABASE_JWT_SECRET=your_jwt_secret_here

# Supabase configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# Payment API Key (for critical operations)
PAYMENT_API_KEY=your_secure_payment_api_key_here
```

### Payment Data Encryption
```bash
# Primary encryption key (64 hex characters = 32 bytes for AES-256)
PAYMENT_ENCRYPTION_KEY=your_64_character_hex_encryption_key_here

# Master key for field-level encryption (minimum 32 characters)
PAYMENT_MASTER_KEY=your_secure_master_key_for_field_encryption_here
```

### Optional (for enhanced features)
```bash
# Redis for rate limiting (optional - falls back to in-memory)
REDIS_URL=redis://localhost:6379

# Stripe configuration
STRIPE_SECRET_KEY=sk_test_or_live_key_here
```

## Protected Endpoints

### 1. GET /api/payment-methods
- **Authentication**: JWT required
- **Authorization**: All authenticated users
- **Rate Limit**: 30/minute
- **Purpose**: Get available payment methods

### 2. POST /api/payment-methods
- **Authentication**: JWT + API Key required
- **Authorization**: Staff roles only
- **Rate Limit**: 10/minute
- **Purpose**: Create new payment method

### 3. POST /api/payment-methods/stripe/process
- **Authentication**: JWT + API Key required
- **Authorization**: Staff roles only
- **Rate Limit**: 20/minute
- **Purpose**: Process payments

### 4. POST /api/payment-methods/validate
- **Authentication**: JWT required
- **Authorization**: All authenticated users
- **Rate Limit**: 50/minute
- **Purpose**: Validate payment data

### 5. POST /api/payment-methods/sepa
- **Authentication**: JWT + API Key required
- **Authorization**: Staff roles only
- **Rate Limit**: 10/minute
- **Purpose**: Create SEPA payment methods

## Error Codes

### Authentication Errors
- `INVALID_API_KEY`: API key missing or invalid
- `MISSING_AUTH_HEADER`: Authorization header missing
- `AUTH_FAILED`: JWT token validation failed

### Authorization Errors
- `INSUFFICIENT_PERMISSIONS`: User role not authorized
- `TENANT_ACCESS_DENIED`: Access to tenant denied

### Rate Limiting Errors
- `RATE_LIMIT_EXCEEDED`: Too many requests

### Validation Errors
- `MISSING_REQUIRED_FIELDS`: Required fields missing
- `MISSING_PARAMETERS`: Required parameters missing
- `INVALID_AMOUNT`: Invalid payment amount

## Security Best Practices

### 1. Token Management
- Tokens should be stored securely on client side
- Implement token refresh mechanism
- Use short-lived tokens (recommended: 1 hour)

### 2. API Key Management
- Store API keys securely in environment variables
- Rotate API keys regularly
- Use different keys for different environments

### 3. Network Security
- Use HTTPS only in production
- Implement proper CORS policies
- Consider IP whitelisting for critical operations

### 4. Monitoring and Logging
- Log all authentication failures
- Monitor rate limit violations
- Track payment processing attempts
- Set up alerts for suspicious activity

## Testing Security

### 1. Authentication Testing
```bash
# Test without token
curl -X GET https://your-api.com/api/payment-methods

# Test with invalid token
curl -X GET https://your-api.com/api/payment-methods \
  -H "Authorization: Bearer invalid_token"

# Test with valid token
curl -X GET https://your-api.com/api/payment-methods \
  -H "Authorization: Bearer valid_jwt_token"
```

### 2. API Key Testing
```bash
# Test without API key
curl -X POST https://your-api.com/api/payment-methods \
  -H "Authorization: Bearer valid_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{"tenant_id":"test","payment_method_code":"card"}'

# Test with API key
curl -X POST https://your-api.com/api/payment-methods \
  -H "Authorization: Bearer valid_jwt_token" \
  -H "X-API-Key: your_api_key" \
  -H "Content-Type: application/json" \
  -d '{"tenant_id":"test","payment_method_code":"card"}'
```

### 3. Rate Limiting Testing
```bash
# Test rate limiting by making multiple rapid requests
for i in {1..15}; do
  curl -X POST https://your-api.com/api/payment-methods/validate \
    -H "Authorization: Bearer valid_jwt_token" \
    -H "Content-Type: application/json" \
    -d '{"payment_method_code":"card","country_code":"US","payment_data":{}}'
done
```

## Production Deployment Checklist

- [ ] All environment variables configured
- [ ] API keys generated and secured
- [ ] Rate limiting configured with Redis
- [ ] Security headers verified
- [ ] HTTPS enforced
- [ ] Monitoring and alerting set up
- [ ] Security testing completed
- [ ] Documentation updated

## Incident Response

### 1. Authentication Bypass
- Immediately rotate JWT secrets
- Invalidate all existing tokens
- Review authentication logs
- Notify affected users

### 2. API Key Compromise
- Immediately rotate API keys
- Review API access logs
- Block suspicious IP addresses
- Update client applications

### 3. Rate Limit Bypass
- Review rate limiting implementation
- Temporarily reduce limits
- Block suspicious traffic
- Update rate limiting rules
